"""
健康检查路由
提供系统健康状态和监控接口
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from ...agents.agent_coordinator import get_coordinator
from ...core.config import get_config

logger = logging.getLogger(__name__)

router = APIRouter()


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: str
    version: str
    uptime_seconds: float
    components: Dict[str, Any]


class SystemMetrics(BaseModel):
    """系统指标响应模型"""
    active_tasks: int
    pending_tasks: int
    completed_tasks: int
    failed_tasks: int
    active_sessions: int
    memory_usage: Dict[str, Any]
    system_load: Dict[str, Any]


# 应用启动时间
_start_time = datetime.now()


@router.get("/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """
    系统健康检查
    
    返回系统的整体健康状态，包括各个组件的状态信息
    """
    try:
        config = get_config()
        coordinator = await get_coordinator()
        
        # 计算运行时间
        uptime = (datetime.now() - _start_time).total_seconds()
        
        # 检查各个组件状态
        components = {
            "database": await _check_database_health(coordinator),
            "qwen_service": await _check_qwen_service_health(coordinator),
            "knowledge_base": await _check_knowledge_base_health(coordinator),
            "agent_coordinator": await _check_coordinator_health(coordinator)
        }
        
        # 判断整体状态
        overall_status = "healthy"
        for component, status in components.items():
            if status.get("status") != "healthy":
                overall_status = "degraded"
                break
        
        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now().isoformat(),
            version=config.app.version,
            uptime_seconds=uptime,
            components=components
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now().isoformat(),
            version="unknown",
            uptime_seconds=0.0,
            components={"error": str(e)}
        )


@router.get("/metrics", response_model=SystemMetrics, summary="系统指标")
async def get_system_metrics():
    """
    获取系统运行指标
    
    返回详细的系统运行指标，包括任务状态、会话信息等
    """
    try:
        coordinator = await get_coordinator()
        system_status = coordinator.get_system_status()
        
        # 获取内存使用情况
        memory_info = _get_memory_info()
        
        # 获取系统负载
        load_info = _get_system_load()
        
        return SystemMetrics(
            active_tasks=system_status.get("active_tasks", 0),
            pending_tasks=system_status.get("pending_tasks", 0),
            completed_tasks=system_status.get("completed_tasks", 0),
            failed_tasks=system_status.get("failed_tasks", 0),
            active_sessions=system_status.get("active_sessions", 0),
            memory_usage=memory_info,
            system_load=load_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get system metrics: {e}")
        return SystemMetrics(
            active_tasks=0,
            pending_tasks=0,
            completed_tasks=0,
            failed_tasks=0,
            active_sessions=0,
            memory_usage={"error": str(e)},
            system_load={"error": str(e)}
        )


@router.get("/ping", summary="简单ping检查")
async def ping():
    """
    简单的ping检查
    
    快速检查API是否响应
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "message": "pong"
    }


async def _check_database_health(coordinator) -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        # 尝试测试数据库连接
        db_executor = coordinator.vanna_core.db_executor
        if db_executor:
            is_healthy = await db_executor.test_connection()
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "message": "Database connection OK" if is_healthy else "Database connection failed"
            }
        else:
            return {
                "status": "unknown",
                "message": "Database executor not available"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Database check failed: {str(e)}"
        }


async def _check_qwen_service_health(coordinator) -> Dict[str, Any]:
    """检查千问服务健康状态"""
    try:
        # 检查千问服务是否可用
        text_generator = coordinator.router_agent.text_generator
        if text_generator:
            return {
                "status": "healthy",
                "message": "Qwen service available"
            }
        else:
            return {
                "status": "unhealthy",
                "message": "Qwen service not available"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Qwen service check failed: {str(e)}"
        }


async def _check_knowledge_base_health(coordinator) -> Dict[str, Any]:
    """检查知识库健康状态"""
    try:
        # 检查知识库状态
        kb_manager = coordinator.vanna_core.kb_manager
        if kb_manager:
            stats = await kb_manager.get_stats()
            total_items = stats.get("total_items", 0)
            
            return {
                "status": "healthy" if total_items > 0 else "warning",
                "message": f"Knowledge base has {total_items} items",
                "stats": stats
            }
        else:
            return {
                "status": "unhealthy",
                "message": "Knowledge base not available"
            }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Knowledge base check failed: {str(e)}"
        }


async def _check_coordinator_health(coordinator) -> Dict[str, Any]:
    """检查协调器健康状态"""
    try:
        system_status = coordinator.get_system_status()
        
        return {
            "status": "healthy",
            "message": "Agent coordinator running",
            "stats": system_status
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Coordinator check failed: {str(e)}"
        }


def _get_memory_info() -> Dict[str, Any]:
    """获取内存使用信息"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        
        return {
            "total_mb": round(memory.total / 1024 / 1024, 2),
            "available_mb": round(memory.available / 1024 / 1024, 2),
            "used_mb": round(memory.used / 1024 / 1024, 2),
            "percent": memory.percent
        }
    except ImportError:
        return {"error": "psutil not available"}
    except Exception as e:
        return {"error": str(e)}


def _get_system_load() -> Dict[str, Any]:
    """获取系统负载信息"""
    try:
        import psutil
        
        cpu_percent = psutil.cpu_percent(interval=1)
        load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        
        result = {
            "cpu_percent": cpu_percent,
            "cpu_count": psutil.cpu_count()
        }
        
        if load_avg:
            result.update({
                "load_1min": load_avg[0],
                "load_5min": load_avg[1],
                "load_15min": load_avg[2]
            })
        
        return result
    except ImportError:
        return {"error": "psutil not available"}
    except Exception as e:
        return {"error": str(e)}