"""
API集成测试
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import patch, AsyncMock
import json

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from api.main import app
from tests.test_utils import MockDataGenerator, MockServices, TestAssertions


class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_coordinator(self):
        """模拟协调器"""
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置默认响应
            mock_coordinator.process_user_request.return_value = {
                "success": True,
                "message": "查询完成",
                "data": {
                    "type": "complete_analysis",
                    "query": "测试查询",
                    "sql": "SELECT * FROM creators LIMIT 10",
                    "raw_data": {
                        "data": MockDataGenerator.generate_creator_data(10),
                        "columns": ["creator_name", "follower_count"],
                        "row_count": 10,
                        "execution_time": 0.5
                    },
                    "analysis": {
                        "summary": "查询返回了10个达人的数据",
                        "insights": ["头部达人占据主要流量", "粉丝分布呈现长尾特征"],
                        "recommendations": ["建议关注中小达人的成长"],
                        "key_metrics": {"total_creators": 10, "avg_followers": 50000}
                    },
                    "visualizations": [
                        {
                            "chart_type": "bar_chart",
                            "title": "达人粉丝数对比",
                            "description": "展示各达人的粉丝数量",
                            "code": "import plotly.express as px\nfig = px.bar(...)"
                        }
                    ]
                }
            }
            
            yield mock_coordinator
    
    def test_health_check(self, client):
        """测试健康检查接口"""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_query_endpoint_success(self, client, mock_coordinator):
        """测试查询接口成功场景"""
        query_request = {
            "question": "今天涨粉最多的达人是谁？",
            "user_id": "test_user",
            "session_id": "test_session"
        }
        
        response = client.post("/api/v1/query", json=query_request)
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证响应结构
        assert data["success"] is True
        assert "data" in data
        assert data["data"]["type"] == "complete_analysis"
        assert "raw_data" in data["data"]
        assert "analysis" in data["data"]
        assert "visualizations" in data["data"]
        
        # 验证协调器被调用
        mock_coordinator.process_user_request.assert_called_once_with(
            "今天涨粉最多的达人是谁？", "test_session", "test_user"
        )
    
    def test_query_endpoint_validation_error(self, client):
        """测试查询接口验证错误"""
        # 缺少必需字段
        invalid_request = {
            "user_id": "test_user"
            # 缺少question字段
        }
        
        response = client.post("/api/v1/query", json=invalid_request)
        
        assert response.status_code == 422  # Validation error
    
    def test_query_endpoint_empty_question(self, client):
        """测试空问题查询"""
        query_request = {
            "question": "",
            "user_id": "test_user"
        }
        
        response = client.post("/api/v1/query", json=query_request)
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "问题不能为空" in data["message"]
    
    def test_query_endpoint_processing_error(self, client, mock_coordinator):
        """测试查询处理错误"""
        # 配置协调器返回错误
        mock_coordinator.process_user_request.return_value = {
            "success": False,
            "message": "处理失败",
            "error": "模拟错误"
        }
        
        query_request = {
            "question": "测试查询",
            "user_id": "test_user"
        }
        
        response = client.post("/api/v1/query", json=query_request)
        
        assert response.status_code == 200  # API调用成功，但业务处理失败
        data = response.json()
        assert data["success"] is False
        assert data["message"] == "处理失败"
    
    def test_query_endpoint_internal_error(self, client, mock_coordinator):
        """测试内部错误处理"""
        # 配置协调器抛出异常
        mock_coordinator.process_user_request.side_effect = Exception("内部错误")
        
        query_request = {
            "question": "测试查询",
            "user_id": "test_user"
        }
        
        response = client.post("/api/v1/query", json=query_request)
        
        assert response.status_code == 500
        data = response.json()
        assert data["success"] is False
        assert "内部服务器错误" in data["message"]
    
    def test_query_status_endpoint(self, client):
        """测试查询状态接口"""
        query_id = "test_query_123"
        
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置任务状态返回
            mock_coordinator.get_task_status.return_value = {
                "task_id": query_id,
                "status": "completed",
                "progress": 1.0,
                "created_at": "2024-01-01T00:00:00",
                "completed_at": "2024-01-01T00:01:00"
            }
            
            response = client.get(f"/api/v1/status/{query_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == query_id
            assert data["status"] == "completed"
            assert data["progress"] == 1.0
    
    def test_query_status_not_found(self, client):
        """测试查询状态不存在"""
        query_id = "nonexistent_query"
        
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置返回None（任务不存在）
            mock_coordinator.get_task_status.return_value = None
            
            response = client.get(f"/api/v1/status/{query_id}")
            
            assert response.status_code == 404
            data = response.json()
            assert "任务不存在" in data["message"]
    
    def test_query_history_endpoint(self, client):
        """测试查询历史接口"""
        user_id = "test_user"
        
        # 模拟查询历史数据
        mock_history = [
            {
                "query_id": "query_1",
                "question": "今天涨粉最多的达人",
                "timestamp": "2024-01-01T10:00:00",
                "status": "completed"
            },
            {
                "query_id": "query_2", 
                "question": "热门视频排行",
                "timestamp": "2024-01-01T11:00:00",
                "status": "completed"
            }
        ]
        
        with patch('api.main.get_query_history') as mock_get_history:
            mock_get_history.return_value = mock_history
            
            response = client.get(f"/api/v1/query/history/{user_id}")
            
            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["query_id"] == "query_1"
            assert data[1]["query_id"] == "query_2"
    
    def test_metrics_endpoint(self, client):
        """测试系统指标接口"""
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置系统状态返回
            mock_coordinator.get_system_status.return_value = {
                "active_tasks": 5,
                "pending_tasks": 2,
                "completed_tasks": 100,
                "failed_tasks": 3,
                "active_sessions": 10
            }
            
            response = client.get("/api/v1/metrics")
            
            assert response.status_code == 200
            data = response.json()
            assert data["active_tasks"] == 5
            assert data["pending_tasks"] == 2
            assert data["completed_tasks"] == 100
    
    def test_cors_headers(self, client):
        """测试CORS头部"""
        response = client.options("/api/v1/query")
        
        assert response.status_code == 200
        assert "Access-Control-Allow-Origin" in response.headers
        assert "Access-Control-Allow-Methods" in response.headers
    
    def test_request_timeout(self, client, mock_coordinator):
        """测试请求超时处理"""
        # 配置协调器模拟超时
        async def slow_process(*args, **kwargs):
            await asyncio.sleep(10)  # 模拟长时间处理
            return {"success": True, "data": {}}
        
        mock_coordinator.process_user_request.side_effect = slow_process
        
        query_request = {
            "question": "复杂查询",
            "user_id": "test_user"
        }
        
        # 注意：TestClient是同步的，这里主要测试API结构
        # 实际的超时测试需要在异步环境中进行
        response = client.post("/api/v1/query", json=query_request)
        
        # 由于TestClient的限制，这里主要验证API能正常响应
        # 实际的超时逻辑会在生产环境中生效
        assert response.status_code in [200, 500, 504]


class TestAPIAuthentication:
    """API认证测试"""
    
    @pytest.fixture
    def client_with_auth(self):
        """创建带认证的测试客户端"""
        # 如果API有认证机制，在这里配置
        return TestClient(app)
    
    def test_api_without_auth(self, client_with_auth):
        """测试无认证访问"""
        # 如果API需要认证，这里测试无认证访问的情况
        response = client_with_auth.get("/api/v1/health")
        assert response.status_code == 200  # 健康检查通常不需要认证
    
    def test_api_with_invalid_auth(self, client_with_auth):
        """测试无效认证"""
        # 如果有认证机制，测试无效认证
        headers = {"Authorization": "Bearer invalid_token"}
        response = client_with_auth.get("/api/v1/metrics", headers=headers)
        
        # 根据实际认证机制调整期望的状态码
        assert response.status_code in [200, 401, 403]


class TestAPIPerformance:
    """API性能测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_concurrent_requests(self, client):
        """测试并发请求"""
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置快速响应
            mock_coordinator.process_user_request.return_value = {
                "success": True,
                "data": {"type": "chat", "response": "快速响应"}
            }
            
            # 发送多个并发请求
            import threading
            import time
            
            results = []
            
            def make_request():
                start_time = time.time()
                response = client.post("/api/v1/query", json={
                    "question": "测试查询",
                    "user_id": "test_user"
                })
                end_time = time.time()
                results.append({
                    "status_code": response.status_code,
                    "response_time": end_time - start_time
                })
            
            # 创建多个线程
            threads = []
            for _ in range(10):
                thread = threading.Thread(target=make_request)
                threads.append(thread)
            
            # 启动所有线程
            for thread in threads:
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 验证结果
            assert len(results) == 10
            for result in results:
                assert result["status_code"] == 200
                assert result["response_time"] < 5.0  # 响应时间应该在5秒内
    
    def test_large_response_handling(self, client):
        """测试大响应处理"""
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            mock_coordinator = AsyncMock()
            mock_get_coordinator.return_value = mock_coordinator
            
            # 配置大数据响应
            large_data = MockDataGenerator.generate_creator_data(1000)  # 1000条记录
            mock_coordinator.process_user_request.return_value = {
                "success": True,
                "data": {
                    "type": "complete_analysis",
                    "raw_data": {
                        "data": large_data,
                        "columns": ["creator_name", "follower_count"],
                        "row_count": len(large_data)
                    },
                    "analysis": {"summary": "大数据分析"},
                    "visualizations": []
                }
            }
            
            response = client.post("/api/v1/query", json={
                "question": "所有达人数据",
                "user_id": "test_user"
            })
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["data"]["raw_data"]["data"]) == 1000


class TestAPIErrorHandling:
    """API错误处理测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_malformed_json(self, client):
        """测试格式错误的JSON"""
        response = client.post(
            "/api/v1/query",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422
    
    def test_missing_content_type(self, client):
        """测试缺少Content-Type头部"""
        response = client.post("/api/v1/query", data='{"question": "test"}')
        
        # FastAPI通常会处理这种情况
        assert response.status_code in [200, 400, 422]
    
    def test_method_not_allowed(self, client):
        """测试不允许的HTTP方法"""
        response = client.put("/api/v1/query")
        
        assert response.status_code == 405
    
    def test_not_found_endpoint(self, client):
        """测试不存在的端点"""
        response = client.get("/api/v1/nonexistent")
        
        assert response.status_code == 404
    
    def test_server_error_handling(self, client):
        """测试服务器错误处理"""
        with patch('api.main.get_coordinator') as mock_get_coordinator:
            # 模拟获取协调器时抛出异常
            mock_get_coordinator.side_effect = Exception("服务器内部错误")
            
            response = client.post("/api/v1/query", json={
                "question": "测试查询",
                "user_id": "test_user"
            })
            
            assert response.status_code == 500
            data = response.json()
            assert data["success"] is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])