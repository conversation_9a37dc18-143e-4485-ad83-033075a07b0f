"""
UI集成测试
测试生产版本UI与Agent系统的集成
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

try:
    from src.agents.agent_coordinator import AgentCoordinator, get_coordinator
    from src.models.base import APIResponse
    from src.models.query import QueryResult
    from src.core.config import get_config
    IMPORTS_AVAILABLE = True
except ImportError as e:
    IMPORTS_AVAILABLE = False
    IMPORT_ERROR = str(e)


class TestUIIntegration:
    """UI集成测试类"""
    
    @pytest.fixture
    async def coordinator(self):
        """创建测试用的协调器"""
        coordinator = AgentCoordinator()
        await coordinator.start()
        yield coordinator
        await coordinator.close()
    
    @pytest.fixture
    def sample_query_response(self):
        """创建示例查询响应"""
        return APIResponse(
            success=True,
            message="查询成功",
            data={
                "type": "complete_analysis",
                "task_id": "test-task-123",
                "query": "今天涨粉最多的前10个达人是谁？",
                "sql": "SELECT username, display_name, follower_growth FROM creators ORDER BY follower_growth DESC LIMIT 10",
                "raw_data": {
                    "data": [
                        {"username": "gamer_king", "display_name": "游戏王者", "follower_growth": 15420},
                        {"username": "beauty_queen", "display_name": "美妆女王", "follower_growth": 12350},
                        {"username": "dance_star", "display_name": "舞蹈之星", "follower_growth": 9870}
                    ],
                    "columns": ["username", "display_name", "follower_growth"],
                    "row_count": 3,
                    "execution_time": 0.245,
                    "is_mock": True
                },
                "analysis": {
                    "summary": "根据今日数据分析，游戏王者以15,420的涨粉量位居第一。",
                    "insights": [
                        "游戏内容在当前具有很强的吸引力",
                        "美妆和舞蹈内容也表现不错"
                    ],
                    "recommendations": [
                        "关注高涨粉达人的内容策略",
                        "考虑游戏分类的相关合作"
                    ],
                    "key_metrics": {
                        "最高涨粉": "15,420",
                        "平均涨粉": "12,547",
                        "总涨粉": "37,640"
                    }
                },
                "visualizations": [
                    {
                        "chart_type": "bar",
                        "title": "今日涨粉排行榜",
                        "description": "显示今日涨粉最多的达人",
                        "code": "fig = px.bar(df, x='display_name', y='follower_growth', title='今日涨粉排行榜')\nst.plotly_chart(fig, use_container_width=True)"
                    }
                ],
                "generated_at": "2025-01-24T10:30:00"
            }
        )
    
    @pytest.mark.asyncio
    async def test_coordinator_initialization(self, coordinator):
        """测试协调器初始化"""
        assert coordinator is not None
        assert coordinator.vanna_core is not None
        assert coordinator.router_agent is not None
        assert coordinator.display_agent is not None
        assert coordinator.message_bus is not None
    
    @pytest.mark.asyncio
    async def test_process_user_request(self, coordinator):
        """测试用户请求处理"""
        # 模拟用户查询
        user_input = "今天涨粉最多的前10个达人是谁？"
        session_id = "test-session-123"
        
        # 由于需要真实的数据库连接，这里我们模拟响应
        with patch.object(coordinator.router_agent, 'process_user_input') as mock_router:
            mock_router.return_value = APIResponse(
                success=True,
                message="路由成功",
                data={
                    "type": "data_query",
                    "sql": "SELECT * FROM creators ORDER BY follower_growth DESC LIMIT 10",
                    "data": [{"username": "test", "follower_growth": 1000}],
                    "columns": ["username", "follower_growth"],
                    "row_count": 1,
                    "execution_time": 0.1
                }
            )
            
            with patch.object(coordinator.display_agent, 'create_report') as mock_display:
                from agents.display_agent import Report, AnalysisResult, VisualizationSpec
                from datetime import datetime
                
                mock_report = Report(
                    analysis=AnalysisResult(
                        summary="测试分析摘要",
                        insights=["测试洞察"],
                        recommendations=["测试建议"],
                        key_metrics={"测试指标": "100"}
                    ),
                    visualizations=[
                        VisualizationSpec(
                            chart_type="bar",
                            title="测试图表",
                            description="测试描述",
                            code="# 测试代码"
                        )
                    ],
                    generated_at=datetime.now()
                )
                mock_display.return_value = mock_report
                
                response = await coordinator.process_user_request(user_input, session_id)
                
                assert response.success is True
                assert response.data is not None
                assert response.data["type"] == "complete_analysis"
    
    @pytest.mark.asyncio
    async def test_get_global_coordinator(self):
        """测试全局协调器获取"""
        coordinator1 = await get_coordinator()
        coordinator2 = await get_coordinator()
        
        # 应该返回同一个实例
        assert coordinator1 is coordinator2
        assert coordinator1 is not None
    
    def test_config_loading(self):
        """测试配置加载"""
        try:
            config = get_config()
            assert config is not None
            assert hasattr(config, 'app')
            assert hasattr(config, 'qwen')
            assert hasattr(config, 'database')
            assert hasattr(config, 'vanna')
        except Exception as e:
            pytest.skip(f"配置加载失败，跳过测试: {e}")
    
    def test_ui_components_import(self):
        """测试UI组件导入"""
        try:
            # 测试是否能正常导入UI相关模块
            import streamlit as st
            import plotly.express as px
            import plotly.graph_objects as go
            import pandas as pd
            
            # 基本功能测试
            df = pd.DataFrame({"x": [1, 2, 3], "y": [4, 5, 6]})
            fig = px.bar(df, x="x", y="y")
            assert fig is not None
            
        except ImportError as e:
            pytest.skip(f"UI依赖未安装，跳过测试: {e}")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, coordinator):
        """测试错误处理"""
        # 测试无效输入
        response = await coordinator.process_user_request("", "test-session")
        # 根据实际实现，空输入可能被路由为聊天回复或错误
        assert response is not None
        
        # 测试异常情况
        with patch.object(coordinator.router_agent, 'process_user_input') as mock_router:
            mock_router.side_effect = Exception("测试异常")
            
            response = await coordinator.process_user_request("测试查询", "test-session")
            assert response.success is False
            assert "错误" in response.message
    
    def test_data_processing_functions(self, sample_query_response):
        """测试数据处理功能"""
        data = sample_query_response.data
        raw_data = data["raw_data"]
        
        # 测试DataFrame创建
        df = pd.DataFrame(raw_data["data"])
        assert not df.empty
        assert len(df) == raw_data["row_count"]
        assert list(df.columns) == raw_data["columns"]
        
        # 测试数据统计
        assert df["follower_growth"].max() == 15420
        assert df["follower_growth"].min() == 9870
        assert len(df) == 3
    
    def test_visualization_code_generation(self, sample_query_response):
        """测试可视化代码生成"""
        data = sample_query_response.data
        visualizations = data["visualizations"]
        
        assert len(visualizations) > 0
        
        for viz in visualizations:
            assert "chart_type" in viz
            assert "title" in viz
            assert "code" in viz
            
            # 验证代码包含必要的组件
            code = viz["code"]
            assert "px." in code or "go." in code or "st." in code
    
    def test_analysis_results_structure(self, sample_query_response):
        """测试分析结果结构"""
        data = sample_query_response.data
        analysis = data["analysis"]
        
        # 验证分析结果包含必要字段
        assert "summary" in analysis
        assert "insights" in analysis
        assert "recommendations" in analysis
        assert "key_metrics" in analysis
        
        # 验证数据类型
        assert isinstance(analysis["insights"], list)
        assert isinstance(analysis["recommendations"], list)
        assert isinstance(analysis["key_metrics"], dict)
        assert isinstance(analysis["summary"], str)
    
    @pytest.mark.asyncio
    async def test_session_management(self, coordinator):
        """测试会话管理"""
        session_id_1 = "session-1"
        session_id_2 = "session-2"
        
        # 模拟不同会话的请求
        with patch.object(coordinator.router_agent, 'process_user_input') as mock_router:
            mock_router.return_value = APIResponse(
                success=True,
                message="成功",
                data={"type": "chat_response", "response": "测试回复"}
            )
            
            response1 = await coordinator.process_user_request("查询1", session_id_1)
            response2 = await coordinator.process_user_request("查询2", session_id_2)
            
            assert response1.success is True
            assert response2.success is True
            
            # 验证路由器被正确调用
            assert mock_router.call_count == 2
            
            # 验证会话ID被正确传递
            calls = mock_router.call_args_list
            assert calls[0][1]["session_id"] == session_id_1
            assert calls[1][1]["session_id"] == session_id_2
    
    def test_sql_analysis_function(self):
        """测试SQL分析功能"""
        # 导入SQL分析函数（需要从UI模块导入）
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src" / "ui"))
            from streamlit_app import analyze_sql
            
            # 测试简单SELECT查询
            sql = "SELECT username, follower_count FROM creators WHERE category = 'gaming' ORDER BY follower_count DESC LIMIT 10"
            analysis = analyze_sql(sql)
            
            assert "查询类型" in analysis
            assert analysis["查询类型"] == "SELECT (数据查询)"
            assert analysis["条件筛选"] == "是"
            assert analysis["结果排序"] == "是"
            assert analysis["结果限制"] == "是"
            
            # 测试JOIN查询
            sql_join = "SELECT c.username, v.title FROM creators c JOIN videos v ON c.id = v.creator_id"
            analysis_join = analyze_sql(sql_join)
            
            assert analysis_join["表连接"] == "是"
            
        except ImportError:
            pytest.skip("无法导入UI模块，跳过SQL分析测试")
    
    @pytest.mark.asyncio
    async def test_task_management(self, coordinator):
        """测试任务管理"""
        from agents.agent_coordinator import TaskType
        
        # 创建测试任务
        task_id = await coordinator.create_task(
            TaskType.USER_QUERY,
            {"user_input": "测试查询", "session_id": "test"}
        )
        
        assert task_id is not None
        
        # 获取任务状态
        status = await coordinator.get_task_status(task_id)
        assert status is not None
        assert status["task_id"] == task_id
        assert status["task_type"] == TaskType.USER_QUERY.value
        
        # 等待任务完成或超时
        import time
        max_wait = 5  # 最多等待5秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            status = await coordinator.get_task_status(task_id)
            if status["status"] in ["completed", "failed"]:
                break
            await asyncio.sleep(0.1)
        
        # 验证任务最终状态
        final_status = await coordinator.get_task_status(task_id)
        assert final_status["status"] in ["completed", "failed", "running"]
    
    def test_system_status(self, coordinator):
        """测试系统状态"""
        status = coordinator.get_system_status()
        
        assert isinstance(status, dict)
        assert "active_tasks" in status
        assert "pending_tasks" in status
        assert "completed_tasks" in status
        assert "failed_tasks" in status
        assert "active_sessions" in status
        assert "task_processor_running" in status
        
        # 验证数值类型
        assert isinstance(status["active_tasks"], int)
        assert isinstance(status["pending_tasks"], int)
        assert isinstance(status["task_processor_running"], bool)


class TestUIFunctionality:
    """UI功能测试"""
    
    def test_session_state_initialization(self):
        """测试会话状态初始化"""
        # 模拟Streamlit会话状态
        class MockSessionState:
            def __init__(self):
                self._state = {}
            
            def __contains__(self, key):
                return key in self._state
            
            def __getitem__(self, key):
                return self._state[key]
            
            def __setitem__(self, key, value):
                self._state[key] = value
            
            def get(self, key, default=None):
                return self._state.get(key, default)
        
        # 模拟初始化函数
        session_state = MockSessionState()
        
        # 模拟会话状态初始化逻辑
        if 'session_id' not in session_state:
            session_state['session_id'] = str(id(session_state))  # 使用对象ID作为测试
        
        if 'query_history' not in session_state:
            session_state['query_history'] = []
        
        if 'current_query' not in session_state:
            session_state['current_query'] = None
        
        # 验证初始化结果
        assert 'session_id' in session_state
        assert 'query_history' in session_state
        assert 'current_query' in session_state
        assert isinstance(session_state['query_history'], list)
        assert session_state['current_query'] is None
    
    def test_query_history_management(self):
        """测试查询历史管理"""
        from datetime import datetime
        
        # 模拟查询历史
        query_history = []
        
        # 添加查询记录
        def add_query_to_history(question: str):
            query_history.append({
                'question': question,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        
        # 测试添加记录
        add_query_to_history("测试查询1")
        add_query_to_history("测试查询2")
        
        assert len(query_history) == 2
        assert query_history[0]['question'] == "测试查询1"
        assert query_history[1]['question'] == "测试查询2"
        assert 'timestamp' in query_history[0]
        assert 'timestamp' in query_history[1]
    
    def test_error_message_formatting(self):
        """测试错误消息格式化"""
        # 模拟错误处理函数
        def format_error_message(error: Exception, context: str = "") -> str:
            error_msg = f"❌ 系统错误: {str(error)}"
            if context:
                error_msg += f"\n📍 上下文: {context}"
            return error_msg
        
        # 测试不同类型的错误
        test_error = ValueError("测试错误")
        formatted = format_error_message(test_error, "查询处理")
        
        assert "❌ 系统错误" in formatted
        assert "测试错误" in formatted
        assert "📍 上下文: 查询处理" in formatted


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])