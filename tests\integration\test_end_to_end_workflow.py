"""
端到端工作流集成测试
"""

import pytest
import asyncio
from unittest.mock import patch, AsyncMock
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from agents.agent_coordinator import AgentCoordinator
from agents.router_agent import RouterAgent
from agents.display_agent import DisplayAgent
from core.vanna_core import Vanna<PERSON><PERSON>
from models.query import UserQuery, QueryResult
from models.base import APIResponse
from tests.test_utils import MockDataGenerator, MockServices, TestAssertions


class TestEndToEndWorkflow:
    """端到端工作流测试"""
    
    @pytest.fixture
    async def coordinator_with_mocks(self):
        """创建带模拟服务的协调器"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 创建模拟实例
                    mock_vanna = MockServices.create_mock_vanna_core()
                    mock_router = AsyncMock()
                    mock_display = AsyncMock()
                    
                    mock_vanna_class.return_value = mock_vanna
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = mock_display
                    
                    # 配置路由Agent模拟
                    mock_router.process_user_input.return_value = APIResponse(
                        success=True,
                        message="数据查询完成",
                        data={
                            "type": "data_query",
                            "query": "测试查询",
                            "sql": "SELECT * FROM creators LIMIT 10",
                            "data": MockDataGenerator.generate_creator_data(10),
                            "columns": ["creator_name", "follower_count", "category"],
                            "row_count": 10,
                            "execution_time": 0.5
                        }
                    )
                    
                    # 配置展示Agent模拟
                    mock_report = MockDataGenerator.generate_report()
                    mock_display.create_report.return_value = mock_report
                    
                    # 创建协调器
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    yield coordinator
                    
                    await coordinator.close()
    
    @pytest.mark.asyncio
    async def test_complete_data_query_workflow(self, coordinator_with_mocks):
        """测试完整的数据查询工作流"""
        coordinator = coordinator_with_mocks
        
        # 执行用户请求
        response = await coordinator.process_user_request(
            user_input="今天涨粉最多的达人是谁？",
            session_id="test_session",
            user_id="test_user"
        )
        
        # 验证响应
        TestAssertions.assert_api_response(response, success=True)
        assert response.data["type"] == "complete_analysis"
        assert "analysis" in response.data
        assert "visualizations" in response.data
        assert "raw_data" in response.data
        
        # 验证分析结果
        analysis = response.data["analysis"]
        assert "summary" in analysis
        assert "insights" in analysis
        assert "recommendations" in analysis
        assert "key_metrics" in analysis
        
        # 验证可视化结果
        visualizations = response.data["visualizations"]
        assert isinstance(visualizations, list)
        assert len(visualizations) > 0
        
        for viz in visualizations:
            assert "chart_type" in viz
            assert "title" in viz
            assert "description" in viz
            assert "code" in viz
        
        # 验证原始数据
        raw_data = response.data["raw_data"]
        assert "data" in raw_data
        assert "columns" in raw_data
        assert "row_count" in raw_data
        assert raw_data["row_count"] == len(raw_data["data"])
    
    @pytest.mark.asyncio
    async def test_chat_workflow(self, coordinator_with_mocks):
        """测试聊天工作流"""
        coordinator = coordinator_with_mocks
        
        # 配置路由Agent返回聊天响应
        coordinator.router_agent.process_user_input.return_value = APIResponse(
            success=True,
            message="聊天回复",
            data={
                "type": "chat",
                "response": "你好！我是TikTok数据分析助手。",
                "suggestions": ["查询涨粉排行", "分析热门视频"]
            }
        )
        
        # 执行聊天请求
        response = await coordinator.process_user_request(
            user_input="你好",
            session_id="test_session"
        )
        
        # 验证响应
        TestAssertions.assert_api_response(response, success=True)
        assert response.data["type"] == "chat"
        assert "response" in response.data
        assert "suggestions" in response.data
    
    @pytest.mark.asyncio
    async def test_help_workflow(self, coordinator_with_mocks):
        """测试帮助工作流"""
        coordinator = coordinator_with_mocks
        
        # 配置路由Agent返回帮助响应
        coordinator.router_agent.process_user_input.return_value = APIResponse(
            success=True,
            message="帮助信息",
            data={
                "type": "help",
                "content": {
                    "system_info": {"name": "TikTok AI助手"},
                    "capabilities": ["数据查询", "数据分析"],
                    "example_queries": ["涨粉排行", "播放量统计"]
                }
            }
        )
        
        # 执行帮助请求
        response = await coordinator.process_user_request(
            user_input="帮助",
            session_id="test_session"
        )
        
        # 验证响应
        TestAssertions.assert_api_response(response, success=True)
        assert response.data["type"] == "help"
        assert "content" in response.data
        assert "system_info" in response.data["content"]
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, coordinator_with_mocks):
        """测试错误处理工作流"""
        coordinator = coordinator_with_mocks
        
        # 配置路由Agent返回错误
        coordinator.router_agent.process_user_input.return_value = APIResponse(
            success=False,
            message="处理失败",
            error="模拟错误"
        )
        
        # 执行请求
        response = await coordinator.process_user_request(
            user_input="错误查询",
            session_id="test_session"
        )
        
        # 验证错误响应
        assert response.success is False
        assert response.error is not None
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, coordinator_with_mocks):
        """测试并发请求处理"""
        coordinator = coordinator_with_mocks
        
        # 创建多个并发请求
        tasks = []
        for i in range(5):
            task = coordinator.process_user_request(
                user_input=f"查询{i}",
                session_id=f"session_{i}",
                user_id=f"user_{i}"
            )
            tasks.append(task)
        
        # 等待所有请求完成
        responses = await asyncio.gather(*tasks)
        
        # 验证所有响应
        assert len(responses) == 5
        for response in responses:
            TestAssertions.assert_api_response(response, success=True)


class TestAgentIntegration:
    """Agent间集成测试"""
    
    @pytest.fixture
    def router_agent_with_mocks(self):
        """创建带模拟的路由Agent"""
        with patch('agents.router_agent.QwenTextGenerator') as mock_gen:
            with patch('agents.router_agent.VannaCore') as mock_vanna:
                mock_gen.return_value = MockServices.create_mock_qwen_text_generator()
                mock_vanna.return_value = MockServices.create_mock_vanna_core()
                
                agent = RouterAgent()
                return agent
    
    @pytest.fixture
    def display_agent_with_mocks(self):
        """创建带模拟的展示Agent"""
        with patch('agents.display_agent.QwenTextGenerator') as mock_gen:
            mock_gen.return_value = MockServices.create_mock_qwen_text_generator()
            
            agent = DisplayAgent()
            return agent
    
    @pytest.mark.asyncio
    async def test_router_to_vanna_integration(self, router_agent_with_mocks):
        """测试路由Agent到Vanna核心的集成"""
        router_agent = router_agent_with_mocks
        
        # 执行数据查询
        response = await router_agent.process_user_input(
            "今天涨粉最多的达人是谁？",
            "test_session",
            "test_user"
        )
        
        # 验证响应
        TestAssertions.assert_api_response(response, success=True)
        assert response.data["type"] == "data_query"
        assert "sql" in response.data
        assert "data" in response.data
        
        # 验证Vanna核心被调用
        router_agent.vanna_core.process_query.assert_called_once()
        
        await router_agent.close()
    
    @pytest.mark.asyncio
    async def test_display_agent_analysis_integration(self, display_agent_with_mocks):
        """测试展示Agent分析集成"""
        display_agent = display_agent_with_mocks
        
        # 创建查询结果
        query_result = MockDataGenerator.generate_query_result()
        
        # 执行分析
        analysis = await display_agent.analyze_data(query_result, "测试问题")
        
        # 验证分析结果
        TestAssertions.assert_analysis_result(analysis)
        
        # 验证文本生成器被调用
        display_agent.text_generator.generate_analysis.assert_called_once()
        
        await display_agent.close()
    
    @pytest.mark.asyncio
    async def test_display_agent_visualization_integration(self, display_agent_with_mocks):
        """测试展示Agent可视化集成"""
        display_agent = display_agent_with_mocks
        
        # 创建查询结果
        query_result = MockDataGenerator.generate_query_result()
        
        # 执行可视化生成
        visualizations = await display_agent.generate_visualization(query_result)
        
        # 验证可视化结果
        assert isinstance(visualizations, list)
        assert len(visualizations) > 0
        
        for viz in visualizations:
            TestAssertions.assert_visualization_code(viz)
        
        # 验证文本生成器被调用
        display_agent.text_generator.generate_visualization_code.assert_called()
        
        await display_agent.close()
    
    @pytest.mark.asyncio
    async def test_complete_report_generation(self, display_agent_with_mocks):
        """测试完整报告生成"""
        display_agent = display_agent_with_mocks
        
        # 创建查询结果
        query_result = MockDataGenerator.generate_query_result()
        
        # 生成完整报告
        report = await display_agent.create_report(query_result, "测试问题")
        
        # 验证报告
        TestAssertions.assert_report(report)
        
        # 验证各个组件都被调用
        display_agent.text_generator.generate_analysis.assert_called_once()
        display_agent.text_generator.generate_visualization_code.assert_called()
        
        await display_agent.close()


class TestVannaCoreIntegration:
    """Vanna核心集成测试"""
    
    @pytest.fixture
    def vanna_core_with_mocks(self):
        """创建带模拟的Vanna核心"""
        with patch('core.vanna_core.KnowledgeBaseManager') as mock_kb:
            with patch('core.vanna_core.QwenTextGenerator') as mock_gen:
                with patch('core.vanna_core.DatabaseExecutor') as mock_db:
                    
                    mock_kb.return_value = MockServices.create_mock_knowledge_base()
                    mock_gen.return_value = MockServices.create_mock_qwen_text_generator()
                    mock_db.return_value = MockServices.create_mock_database_executor()
                    
                    core = VannaCore()
                    return core
    
    @pytest.mark.asyncio
    async def test_knowledge_base_to_sql_generation(self, vanna_core_with_mocks):
        """测试知识库到SQL生成的集成"""
        vanna_core = vanna_core_with_mocks
        
        # 创建用户查询
        user_query = MockDataGenerator.generate_user_query("今天涨粉最多的达人是谁？")
        
        # 处理查询
        result = await vanna_core.process_query(user_query)
        
        # 验证结果
        TestAssertions.assert_query_result(result)
        
        # 验证调用链
        vanna_core.knowledge_base.retrieve_context.assert_called_once()
        vanna_core.text_generator.generate_sql.assert_called_once()
        vanna_core.database_executor.execute_query.assert_called_once()
        
        await vanna_core.close()
    
    @pytest.mark.asyncio
    async def test_sql_generation_to_execution(self, vanna_core_with_mocks):
        """测试SQL生成到执行的集成"""
        vanna_core = vanna_core_with_mocks
        
        # 测试SQL生成
        question = "粉丝最多的达人"
        context = {"schemas": [], "examples": [], "documents": []}
        
        sql_result = await vanna_core.generate_sql(question, context)
        
        # 验证SQL生成结果
        assert "sql" in sql_result
        assert "confidence" in sql_result
        
        # 测试SQL执行
        execution_result = await vanna_core.execute_query(sql_result["sql"])
        
        # 验证执行结果
        assert "data" in execution_result
        assert "columns" in execution_result
        assert "row_count" in execution_result
        
        await vanna_core.close()


class TestExternalServiceIntegration:
    """外部服务集成测试"""
    
    @pytest.mark.asyncio
    async def test_qwen_api_integration(self):
        """测试千问API集成"""
        with patch('core.qwen_text_generator.QwenTextGenerator') as mock_gen_class:
            mock_gen = MockServices.create_mock_qwen_text_generator()
            mock_gen_class.return_value = mock_gen
            
            # 创建文本生成器
            from core.qwen_text_generator import QwenTextGenerator
            generator = QwenTextGenerator()
            
            # 测试意图分类
            intent_result = await generator.classify_intent("今天涨粉最多的达人是谁？")
            assert "intent" in intent_result
            assert "confidence" in intent_result
            
            # 测试SQL生成
            sql_result = await generator.generate_sql("测试提示词")
            assert "sql" in sql_result
            assert "confidence" in sql_result
            
            # 测试分析生成
            analysis_result = await generator.generate_analysis("测试分析提示词")
            assert isinstance(analysis_result, str)
            assert len(analysis_result) > 0
            
            await generator.close()
    
    @pytest.mark.asyncio
    async def test_database_integration(self):
        """测试数据库集成"""
        with patch('core.database_executor.DatabaseExecutor') as mock_db_class:
            mock_db = MockServices.create_mock_database_executor()
            mock_db_class.return_value = mock_db
            
            # 创建数据库执行器
            from core.database_executor import DatabaseExecutor
            executor = DatabaseExecutor()
            
            # 测试查询执行
            sql = "SELECT creator_name, follower_count FROM creators LIMIT 10"
            result = await executor.execute_query(sql)
            
            # 验证结果
            assert "data" in result
            assert "columns" in result
            assert "row_count" in result
            assert "execution_time" in result
            
            await executor.close()
    
    @pytest.mark.asyncio
    async def test_knowledge_base_integration(self):
        """测试知识库集成"""
        with patch('core.knowledge_base.KnowledgeBaseManager') as mock_kb_class:
            mock_kb = MockServices.create_mock_knowledge_base()
            mock_kb_class.return_value = mock_kb
            
            # 创建知识库管理器
            from core.knowledge_base import KnowledgeBaseManager
            kb_manager = KnowledgeBaseManager()
            
            # 测试上下文检索
            context = await kb_manager.retrieve_context("测试问题", limit=10)
            
            # 验证结果
            assert "schemas" in context
            assert "examples" in context
            assert "documents" in context
            
            # 测试训练
            training_data = {
                "schemas": [{"table": "test", "ddl": "CREATE TABLE test..."}],
                "examples": [{"question": "测试", "sql": "SELECT * FROM test"}],
                "documents": [{"title": "文档", "content": "内容"}]
            }
            
            result = await kb_manager.train(training_data)
            assert result is True
            
            await kb_manager.close()


class TestErrorRecoveryIntegration:
    """错误恢复集成测试"""
    
    @pytest.mark.asyncio
    async def test_api_failure_recovery(self):
        """测试API失败恢复"""
        with patch('agents.agent_coordinator.AgentCoordinator') as mock_coordinator_class:
            mock_coordinator = AsyncMock()
            mock_coordinator_class.return_value = mock_coordinator
            
            # 模拟第一次调用失败，第二次成功
            mock_coordinator.process_user_request.side_effect = [
                Exception("API调用失败"),
                APIResponse(success=True, message="成功", data={"type": "data_query"})
            ]
            
            coordinator = mock_coordinator_class()
            
            # 第一次调用应该失败
            with pytest.raises(Exception):
                await coordinator.process_user_request("测试", "session1")
            
            # 第二次调用应该成功
            response = await coordinator.process_user_request("测试", "session1")
            TestAssertions.assert_api_response(response, success=True)
    
    @pytest.mark.asyncio
    async def test_database_connection_recovery(self):
        """测试数据库连接恢复"""
        with patch('core.database_executor.DatabaseExecutor') as mock_db_class:
            mock_db = AsyncMock()
            mock_db_class.return_value = mock_db
            
            # 模拟连接失败后恢复
            mock_db.execute_query.side_effect = [
                Exception("数据库连接失败"),
                {"data": [], "columns": [], "row_count": 0, "execution_time": 0.1}
            ]
            
            executor = mock_db_class()
            
            # 第一次调用失败
            with pytest.raises(Exception):
                await executor.execute_query("SELECT * FROM test")
            
            # 第二次调用成功
            result = await executor.execute_query("SELECT * FROM test")
            assert "data" in result
    
    @pytest.mark.asyncio
    async def test_partial_failure_handling(self):
        """测试部分失败处理"""
        with patch('agents.agent_coordinator.AgentCoordinator') as mock_coordinator_class:
            mock_coordinator = AsyncMock()
            mock_coordinator_class.return_value = mock_coordinator
            
            # 模拟部分成功的响应（数据查询成功，但分析失败）
            mock_coordinator.process_user_request.return_value = APIResponse(
                success=True,
                message="部分成功",
                data={
                    "type": "complete_analysis",
                    "raw_data": {"data": [{"test": "data"}]},
                    "analysis": {"summary": "分析失败", "error": "分析服务不可用"},
                    "visualizations": []
                }
            )
            
            coordinator = mock_coordinator_class()
            response = await coordinator.process_user_request("测试", "session1")
            
            # 验证部分成功的响应
            TestAssertions.assert_api_response(response, success=True)
            assert "raw_data" in response.data
            assert "analysis" in response.data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])