"""
TikTok AI Agent 生产版Streamlit界面
集成完整的Agent系统，支持实际数据查询和分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import uuid
import sys
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# 创建一个模拟的APIResponse类以避免类型错误
class APIResponse:
    def __init__(self, success=False, message="", data=None, error=None):
        self.success = success
        self.message = message
        self.data = data
        self.error = error

try:
    from src.core.config import get_config
    from src.agents.agent_coordinator import get_coordinator
    from src.models.base import APIResponse as RealAPIResponse
    # 使用真实的APIResponse类
    APIResponse = RealAPIResponse
    config = get_config()
    CONFIG_LOADED = True
except Exception as e:
    CONFIG_LOADED = False
    CONFIG_ERROR = str(e)
    # 如果导入失败，使用模拟的APIResponse类

# 页面配置
st.set_page_config(
    page_title="TikTok AI数据分析助手",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #FF6B6B;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
        margin: 1rem 0;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
        margin: 1rem 0;
    }
    
    .info-box {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #bee5eb;
        margin: 1rem 0;
    }
    
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """初始化会话状态"""
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []
    
    if 'current_query' not in st.session_state:
        st.session_state.current_query = None


def render_header():
    """渲染页面头部"""
    st.markdown('<h1 class="main-header">🎵 TikTok AI数据分析助手</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <p style="font-size: 1.2rem; color: #666;">
            使用自然语言查询TikTok数据，获得智能分析和可视化结果
        </p>
    </div>
    """, unsafe_allow_html=True)


def render_system_status():
    """渲染系统状态"""
    st.subheader("🔧 系统状态")
    
    if CONFIG_LOADED:
        st.markdown('<div class="success-message">✅ 系统配置已加载</div>', unsafe_allow_html=True)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("应用名称", config.app.name)
            st.metric("版本", config.app.version)
        
        with col2:
            st.metric("千问模型", config.qwen.model)
            api_status = "✅ 已配置" if config.qwen.api_key and config.qwen.api_key != "your_qwen_api_key_here" else "❌ 未配置"
            st.metric("API状态", api_status)
        
        with col3:
            st.metric("数据库", f"{config.database.host}:{config.database.port}")
            st.metric("数据库名", config.database.name)
        
        with col4:
            st.metric("会话ID", st.session_state.session_id[:8] + "...")
            st.metric("查询历史", len(st.session_state.query_history))
            
        # 检查API密钥配置
        if not config.qwen.api_key or config.qwen.api_key == "your_qwen_api_key_here":
            st.markdown("""
            <div class="error-message">
                ⚠️ 千问API密钥未正确配置<br>
                请在 .env 文件中设置正确的 QWEN_API_KEY<br>
                获取地址: <a href="https://bailian.console.aliyun.com/" target="_blank">阿里云百炼平台</a>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.markdown(f'<div class="error-message">❌ 系统配置加载失败: {CONFIG_ERROR}</div>', unsafe_allow_html=True)
        st.error("请检查配置文件和环境变量设置")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.header("🔧 功能面板")
        
        # 系统信息
        st.subheader("📊 系统信息")
        if CONFIG_LOADED:
            st.info(f"🔗 数据库: {config.vanna.db_type}")
            st.info(f"🤖 模型: {config.qwen.model}")
            st.info(f"🆔 会话: {st.session_state.session_id[:8]}...")
        
        st.divider()
        
        # 查询示例
        st.subheader("💡 查询示例")
        examples = [
            "今天涨粉最多的前10个达人是谁？",
            "过去一周哪个游戏达人播放量最高？",
            "美妆类视频的平均点赞率是多少？",
            "各个分类达人的平均粉丝数和视频数是多少？",
            "找出互动率最高的100个视频",
            "分析最近热门视频的标签分布",
            "统计不同时间段的用户活跃度"
        ]
        
        for i, example in enumerate(examples):
            if st.button(f"📝 {example[:15]}...", key=f"example_{i}", use_container_width=True):
                st.session_state.query_input = example
                st.rerun()
        
        st.divider()
        
        # 查询历史
        st.subheader("📚 查询历史")
        if st.session_state.query_history:
            for i, query in enumerate(reversed(st.session_state.query_history[-10:])):
                with st.expander(f"查询 {len(st.session_state.query_history) - i}", expanded=False):
                    st.write(f"**问题:** {query['question']}")
                    st.write(f"**时间:** {query['timestamp']}")
                    if st.button("🔄 重新执行", key=f"rerun_{i}"):
                        st.session_state.query_input = query['question']
                        st.rerun()
        else:
            st.info("暂无查询历史")
        
        st.divider()
        
        # 系统操作
        st.subheader("⚙️ 系统操作")
        
        if st.button("🔄 重置会话", use_container_width=True):
            st.session_state.session_id = str(uuid.uuid4())
            st.session_state.query_history = []
            st.session_state.current_query = None
            st.success("会话已重置")
            st.rerun()
        
        if st.button("🧹 清空历史", use_container_width=True):
            st.session_state.query_history = []
            st.success("历史记录已清空")
            st.rerun()


def render_query_interface():
    """渲染查询界面"""
    st.subheader("🔍 智能数据查询")
    
    # 查询输入框
    query_input = st.text_area(
        "请输入您的问题:",
        value=st.session_state.get('query_input', ''),
        height=120,
        placeholder="例如：今天涨粉最多的前10个达人是谁？\n支持复杂查询，如：分析游戏类达人的粉丝增长趋势",
        key="query_textarea",
        help="支持自然语言查询，系统会自动生成SQL并执行分析"
    )
    
    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
    
    with col1:
        if st.button("🚀 开始分析", type="primary", use_container_width=True):
            if query_input.strip():
                if CONFIG_LOADED:
                    st.session_state.current_query = query_input.strip()
                    asyncio.run(process_query_with_agent(query_input.strip()))
                else:
                    st.error("系统配置未加载，无法处理查询")
            else:
                st.warning("请输入您的问题")
    
    with col2:
        if st.button("🧹 清空", use_container_width=True):
            st.session_state.query_input = ""
            st.rerun()
    
    with col3:
        if st.button("📋 示例", use_container_width=True):
            st.session_state.query_input = "今天涨粉最多的前10个达人是谁？"
            st.rerun()
    
    with col4:
        if st.button("❓ 帮助", use_container_width=True):
            show_help_dialog()


def show_help_dialog():
    """显示帮助对话框"""
    with st.expander("📖 使用帮助", expanded=True):
        st.markdown("""
        ### 🎯 支持的查询类型
        
        **📊 数据统计查询**
        - "今天涨粉最多的前10个达人"
        - "各分类达人的平均粉丝数"
        - "最近一周的视频发布量统计"
        
        **📈 趋势分析查询**
        - "游戏类达人的粉丝增长趋势"
        - "美妆视频的互动率变化"
        - "不同时间段的用户活跃度"
        
        **🔍 筛选查询**
        - "找出互动率最高的100个视频"
        - "粉丝数超过10万的美妆达人"
        - "播放量超过100万的游戏视频"
        
        **💡 分析建议查询**
        - "如何提高视频的互动率"
        - "哪个时间段发布视频效果最好"
        - "什么类型的内容最受欢迎"
        
        ### ⚡ 使用技巧
        - 问题描述越具体，分析结果越准确
        - 支持时间范围限定（今天、本周、本月等）
        - 可以指定具体的数据维度和指标
        - 系统会自动生成可视化图表
        """)


async def process_query_with_agent(question: str):
    """使用Agent系统处理查询"""
    # 添加到历史记录
    st.session_state.query_history.append({
        'question': question,
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })
    
    # 创建进度指示器
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            # 步骤1: 初始化系统
            status_text.text("🔧 初始化Agent系统...")
            progress_bar.progress(0.1)
            
            coordinator = await get_coordinator()
            
            # 步骤2: 解析用户意图
            status_text.text("🧠 分析用户意图...")
            progress_bar.progress(0.3)
            
            # 步骤3: 生成SQL查询
            status_text.text("⚡ 生成SQL查询...")
            progress_bar.progress(0.5)
            
            # 步骤4: 执行数据查询
            status_text.text("🔍 执行数据查询...")
            progress_bar.progress(0.7)
            
            # 处理用户请求
            response = await coordinator.process_user_request(
                user_input=question,
                session_id=st.session_state.session_id,
                user_id=None
            )
            
            # 步骤5: 生成分析报告
            status_text.text("📊 生成分析报告...")
            progress_bar.progress(0.9)
            
            # 完成
            status_text.text("✅ 分析完成！")
            progress_bar.progress(1.0)
            
            # 短暂延迟后清除进度指示器
            await asyncio.sleep(0.5)
            progress_bar.empty()
            status_text.empty()
            
            # 渲染结果
            if response.success:
                render_query_results(question, response)
            else:
                st.error(f"❌ 查询处理失败: {response.message}")
                if response.error:
                    with st.expander("🔍 错误详情"):
                        st.error(response.error)
                        
        except Exception as e:
            progress_bar.empty()
            status_text.empty()
            st.error(f"❌ 系统错误: {str(e)}")
            
            with st.expander("🔧 故障排除建议"):
                st.markdown("""
                **可能的解决方案:**
                1. 检查网络连接是否正常
                2. 确认千问API密钥配置正确
                3. 验证数据库连接设置
                4. 重新启动应用程序
                
                **如果问题持续存在，请联系技术支持**
                """)


def render_query_results(question: str, response: APIResponse):
    """渲染查询结果"""
    st.success("✅ 查询处理完成！")
    
    # 结果概览
    st.subheader("📊 分析结果")
    
    # 显示查询信息
    with st.container():
        col1, col2 = st.columns([3, 1])
        with col1:
            st.write(f"**查询问题:** {question}")
        with col2:
            st.write(f"**处理时间:** {datetime.now().strftime('%H:%M:%S')}")
    
    data = response.data
    
    if data and data.get("type") == "complete_analysis":
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📋 数据结果", "📈 可视化", "📝 智能分析", "🔍 SQL查询"])
        
        with tab1:
            render_data_results(data)
        
        with tab2:
            render_visualizations(data)
        
        with tab3:
            render_analysis_results(data)
        
        with tab4:
            render_sql_info(data)
    
    elif data and data.get("type") == "chat_response":
        # 处理聊天回复
        st.subheader("💬 AI回复")
        st.info(data.get("response", "抱歉，我无法理解您的问题。"))
    
    else:
        st.warning("⚠️ 未收到有效的分析结果")


def render_data_results(data: Dict[str, Any]):
    """渲染数据结果"""
    raw_data = data.get("raw_data", {})
    
    if raw_data.get("data"):
        # 数据状态提示
        if raw_data.get("is_mock", False):
            st.info("ℹ️ 当前显示的是模拟数据，用于演示系统功能")
        else:
            st.success("✅ 显示真实数据库查询结果")
        
        # 数据表格
        df = pd.DataFrame(raw_data["data"])
        if not df.empty:
            st.dataframe(
                df, 
                use_container_width=True,
                height=400
            )
            
            # 数据统计信息
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("数据行数", raw_data.get("row_count", len(df)))
            with col2:
                st.metric("数据列数", len(df.columns))
            with col3:
                st.metric("查询耗时", f"{raw_data.get('execution_time', 0):.3f}秒")
            with col4:
                st.metric("数据大小", f"{df.memory_usage(deep=True).sum() / 1024:.1f}KB")
            
            # 数据下载
            csv = df.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="📥 下载CSV数据",
                data=csv,
                file_name=f"tiktok_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        else:
            st.warning("📭 查询结果为空")
    else:
        st.warning("📭 没有数据结果")


def render_visualizations(data: Dict[str, Any]):
    """渲染可视化结果"""
    visualizations = data.get("visualizations", [])
    raw_data = data.get("raw_data", {})
    
    if visualizations and raw_data.get("data"):
        df = pd.DataFrame(raw_data["data"])
        
        for i, viz in enumerate(visualizations):
            st.subheader(f"📊 {viz.get('title', f'图表 {i+1}')}")
            
            if viz.get('description'):
                st.write(viz['description'])
            
            try:
                # 执行可视化代码
                exec_globals = {
                    'df': df,
                    'px': px,
                    'go': go,
                    'st': st,
                    'pd': pd
                }
                exec(viz.get('code', ''), exec_globals)
                
            except Exception as e:
                st.error(f"❌ 图表 {i+1} 生成失败: {str(e)}")
                with st.expander("🔍 错误详情"):
                    st.code(viz.get('code', ''), language='python')
                    st.error(str(e))
    else:
        st.info("📊 暂无可视化内容")


def render_analysis_results(data: Dict[str, Any]):
    """渲染分析结果"""
    analysis = data.get("analysis", {})
    
    if analysis:
        # 分析摘要
        if analysis.get("summary"):
            st.subheader("📋 分析摘要")
            st.write(analysis["summary"])
        
        # 关键洞察
        insights = analysis.get("insights", [])
        if insights:
            st.subheader("💡 关键洞察")
            for i, insight in enumerate(insights, 1):
                st.write(f"**{i}.** {insight}")
        
        # 关键指标
        key_metrics = analysis.get("key_metrics", {})
        if key_metrics:
            st.subheader("📊 关键指标")
            
            # 创建指标卡片
            cols = st.columns(min(len(key_metrics), 4))
            for i, (metric, value) in enumerate(key_metrics.items()):
                with cols[i % len(cols)]:
                    st.metric(metric, value)
        
        # 建议
        recommendations = analysis.get("recommendations", [])
        if recommendations:
            st.subheader("🎯 行动建议")
            for i, rec in enumerate(recommendations, 1):
                st.write(f"**{i}.** {rec}")
    else:
        st.info("📝 暂无分析内容")


def render_sql_info(data: Dict[str, Any]):
    """渲染SQL信息"""
    sql = data.get("sql", "")
    raw_data = data.get("raw_data", {})
    
    if sql:
        st.subheader("🔍 生成的SQL查询")
        st.code(sql, language='sql')
        
        # SQL执行信息
        col1, col2 = st.columns(2)
        with col1:
            st.metric("执行时间", f"{raw_data.get('execution_time', 0):.3f}秒")
        with col2:
            st.metric("返回行数", raw_data.get('row_count', 0))
        
        # SQL分析
        st.subheader("📖 SQL解析")
        sql_analysis = analyze_sql(sql)
        for key, value in sql_analysis.items():
            st.write(f"**{key}:** {value}")
    else:
        st.info("🔍 暂无SQL查询信息")


def analyze_sql(sql: str) -> Dict[str, str]:
    """简单的SQL分析"""
    analysis = {}
    
    sql_upper = sql.upper()
    
    # 查询类型
    if 'SELECT' in sql_upper:
        analysis['查询类型'] = 'SELECT (数据查询)'
    elif 'INSERT' in sql_upper:
        analysis['查询类型'] = 'INSERT (数据插入)'
    elif 'UPDATE' in sql_upper:
        analysis['查询类型'] = 'UPDATE (数据更新)'
    elif 'DELETE' in sql_upper:
        analysis['查询类型'] = 'DELETE (数据删除)'
    
    # 涉及的表
    import re
    tables = re.findall(r'FROM\s+(\w+)', sql_upper)
    if tables:
        analysis['主要表'] = ', '.join(tables)
    
    # 是否有JOIN
    if 'JOIN' in sql_upper:
        analysis['表连接'] = '是'
    else:
        analysis['表连接'] = '否'
    
    # 是否有WHERE条件
    if 'WHERE' in sql_upper:
        analysis['条件筛选'] = '是'
    else:
        analysis['条件筛选'] = '否'
    
    # 是否有排序
    if 'ORDER BY' in sql_upper:
        analysis['结果排序'] = '是'
    else:
        analysis['结果排序'] = '否'
    
    # 是否有限制
    if 'LIMIT' in sql_upper:
        analysis['结果限制'] = '是'
    else:
        analysis['结果限制'] = '否'
    
    return analysis


def main():
    """主函数"""
    # 初始化会话状态
    initialize_session_state()
    
    # 渲染页面
    render_header()
    
    # 系统状态
    render_system_status()
    
    st.divider()
    
    # 侧边栏
    render_sidebar()
    
    # 主要内容区域
    render_query_interface()
    
    st.divider()
    
    # 页脚
    st.markdown("---")
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(
            "<div style='text-align: center; color: #666;'>"
            "🎵 TikTok AI数据分析助手 | 基于Vanna和千问模型构建 | 生产环境 🚀"
            "</div>",
            unsafe_allow_html=True
        )


if __name__ == "__main__":
    main()