#!/usr/bin/env python3
"""
UI界面启动脚本
"""

import subprocess
import sys
from pathlib import Path


def main():
    print("🎵 启动TikTok AI Agent UI界面")
    print("📱 界面地址: http://localhost:8501")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)

    try:
        subprocess.run(
            [
                "streamlit",
                "run",
                "src/ui/streamlit_app.py",
                "--server.port",
                "8501",
                "--server.address",
                "localhost",
            ]
        )
    except FileNotFoundError:
        print("❌ Streamlit未安装")
        print("请运行: pip install streamlit")
    except KeyboardInterrupt:
        print("\n👋 UI界面已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
