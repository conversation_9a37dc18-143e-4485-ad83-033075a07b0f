"""
测试工具类和模拟数据生成器
"""

import random
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from unittest.mock import AsyncMock, Mock

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from models.query import UserQuery, QueryResult
from models.base import APIResponse
from agents.display_agent import AnalysisResult, VisualizationCode, Report


class MockDataGenerator:
    """模拟数据生成器"""
    
    @staticmethod
    def generate_creator_data(count: int = 10) -> List[Dict[str, Any]]:
        """生成达人数据"""
        creators = []
        categories = ["游戏", "美妆", "音乐", "舞蹈", "美食", "旅游", "教育", "科技"]
        
        for i in range(count):
            creator = {
                "creator_id": f"creator_{i+1:03d}",
                "creator_name": f"达人{i+1}",
                "category": random.choice(categories),
                "follower_count": random.randint(1000, 1000000),
                "video_count": random.randint(10, 500),
                "total_views": random.randint(10000, 10000000),
                "total_likes": random.randint(1000, 1000000),
                "total_comments": random.randint(100, 100000),
                "total_shares": random.randint(50, 50000),
                "engagement_rate": round(random.uniform(1.0, 10.0), 2),
                "created_at": datetime.now() - timedelta(days=random.randint(30, 365)),
                "last_active": datetime.now() - timedelta(days=random.randint(0, 30))
            }
            creators.append(creator)
        
        return creators
    
    @staticmethod
    def generate_video_data(count: int = 20) -> List[Dict[str, Any]]:
        """生成视频数据"""
        videos = []
        categories = ["游戏", "美妆", "音乐", "舞蹈", "美食", "旅游", "教育", "科技"]
        
        for i in range(count):
            video = {
                "video_id": f"video_{i+1:04d}",
                "creator_id": f"creator_{random.randint(1, 10):03d}",
                "title": f"精彩视频{i+1}",
                "category": random.choice(categories),
                "duration": random.randint(15, 300),  # 秒
                "view_count": random.randint(100, 1000000),
                "like_count": random.randint(10, 100000),
                "comment_count": random.randint(1, 10000),
                "share_count": random.randint(1, 5000),
                "engagement_rate": round(random.uniform(0.5, 15.0), 2),
                "published_at": datetime.now() - timedelta(days=random.randint(0, 90)),
                "is_trending": random.choice([True, False]),
                "hashtags": [f"#标签{j}" for j in range(random.randint(1, 5))]
            }
            videos.append(video)
        
        return videos
    
    @staticmethod
    def generate_metrics_data(count: int = 50) -> List[Dict[str, Any]]:
        """生成指标数据"""
        metrics = []
        
        for i in range(count):
            metric = {
                "metric_id": f"metric_{i+1:04d}",
                "creator_id": f"creator_{random.randint(1, 10):03d}",
                "date": datetime.now() - timedelta(days=random.randint(0, 30)),
                "daily_views": random.randint(1000, 100000),
                "daily_likes": random.randint(100, 10000),
                "daily_comments": random.randint(10, 1000),
                "daily_shares": random.randint(5, 500),
                "daily_followers_gained": random.randint(-10, 1000),
                "daily_followers_lost": random.randint(0, 100),
                "net_follower_growth": random.randint(-50, 950),
                "engagement_rate": round(random.uniform(1.0, 12.0), 2)
            }
            metrics.append(metric)
        
        return metrics
    
    @staticmethod
    def generate_user_query(question: str = None, user_id: str = None) -> UserQuery:
        """生成用户查询"""
        questions = [
            "今天涨粉最多的达人是谁？",
            "过去一周播放量最高的视频有哪些？",
            "美妆类达人的平均粉丝数是多少？",
            "互动率最高的前10个视频",
            "各个分类的达人数量分布",
            "最近热门的标签有哪些？"
        ]
        
        return UserQuery(
            question=question or random.choice(questions),
            user_id=user_id or f"user_{random.randint(1, 100):03d}",
            session_id=f"session_{uuid.uuid4().hex[:8]}"
        )
    
    @staticmethod
    def generate_query_result(data: List[Dict] = None, columns: List[str] = None) -> QueryResult:
        """生成查询结果"""
        if data is None:
            data = MockDataGenerator.generate_creator_data(5)
        
        if columns is None:
            columns = list(data[0].keys()) if data else []
        
        return QueryResult(
            data=data,
            columns=columns,
            row_count=len(data),
            execution_time=round(random.uniform(0.1, 2.0), 3),
            sql_executed=f"SELECT {', '.join(columns)} FROM test_table LIMIT {len(data)}",
            executed_at=datetime.now()
        )
    
    @staticmethod
    def generate_analysis_result() -> AnalysisResult:
        """生成分析结果"""
        insights = [
            "数据显示了明显的增长趋势",
            "头部达人占据了大部分流量",
            "美妆类别的互动率最高",
            "短视频的完播率更好",
            "周末的发布效果更佳"
        ]
        
        recommendations = [
            "建议增加内容发布频率",
            "优化视频标题和封面",
            "关注热门话题和标签",
            "提高与粉丝的互动"
        ]
        
        return AnalysisResult(
            summary="本次分析涵盖了多个维度的TikTok数据，发现了一些有价值的洞察。",
            insights=random.sample(insights, random.randint(2, 4)),
            recommendations=random.sample(recommendations, random.randint(2, 3)),
            key_metrics={
                "total_creators": random.randint(100, 1000),
                "avg_followers": random.randint(10000, 100000),
                "total_videos": random.randint(1000, 10000),
                "avg_engagement_rate": round(random.uniform(3.0, 8.0), 2)
            }
        )
    
    @staticmethod
    def generate_visualization_code(chart_type: str = "bar_chart") -> VisualizationCode:
        """生成可视化代码"""
        code_templates = {
            "bar_chart": """import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.bar(df, x='creator_name', y='follower_count', 
             title='达人粉丝数对比',
             labels={'creator_name': '达人名称', 'follower_count': '粉丝数'})
fig.update_layout(xaxis_tickangle=-45)
fig.show()""",
            
            "line_chart": """import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.line(df, x='date', y='views', 
              title='播放量趋势',
              labels={'date': '日期', 'views': '播放量'})
fig.show()""",
            
            "pie_chart": """import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.pie(df, names='category', values='count',
             title='分类分布')
fig.show()"""
        }
        
        return VisualizationCode(
            chart_type=chart_type,
            code=code_templates.get(chart_type, code_templates["bar_chart"]),
            title=f"TikTok数据分析 - {chart_type}",
            description=f"展示了数据的{chart_type}可视化"
        )
    
    @staticmethod
    def generate_report(query_result: QueryResult = None) -> Report:
        """生成完整报告"""
        if query_result is None:
            query_result = MockDataGenerator.generate_query_result()
        
        analysis = MockDataGenerator.generate_analysis_result()
        visualizations = [
            MockDataGenerator.generate_visualization_code("bar_chart"),
            MockDataGenerator.generate_visualization_code("pie_chart")
        ]
        
        return Report(analysis, visualizations, query_result)


class MockServices:
    """模拟服务类"""
    
    @staticmethod
    def create_mock_qwen_text_generator():
        """创建模拟千问文本生成器"""
        mock = AsyncMock()
        
        # 模拟意图分类
        mock.classify_intent.return_value = {
            "intent": "data_query",
            "confidence": 0.8,
            "explanation": "检测到数据查询意图"
        }
        
        # 模拟SQL生成
        mock.generate_sql.return_value = {
            "sql": "SELECT creator_name, follower_count FROM creators ORDER BY follower_count DESC LIMIT 10",
            "confidence": 0.85,
            "explanation": "查询粉丝数最多的前10个达人"
        }
        
        # 模拟分析生成
        mock.generate_analysis.return_value = """## 数据摘要
查询返回了10个达人的粉丝数据，显示了明显的头部效应。

## 关键洞察
1. 头部达人的粉丝数远超其他达人
2. 粉丝数分布呈现长尾特征
3. 前3名达人占据了总粉丝数的60%

## 建议
1. 中小达人可以学习头部达人的内容策略
2. 平台可以给予中小达人更多流量扶持"""
        
        # 模拟可视化代码生成
        mock.generate_visualization_code.return_value = """import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.bar(df, x='creator_name', y='follower_count', title='达人粉丝数对比')
fig.show()"""
        
        return mock
    
    @staticmethod
    def create_mock_knowledge_base():
        """创建模拟知识库"""
        mock = AsyncMock()
        
        # 模拟上下文检索
        mock.retrieve_context.return_value = {
            "schemas": [
                {
                    "table_name": "creators",
                    "ddl": "CREATE TABLE creators (id INT, creator_name VARCHAR(100), follower_count INT)",
                    "relevance": 0.9
                }
            ],
            "examples": [
                {
                    "question": "粉丝最多的达人",
                    "sql": "SELECT creator_name FROM creators ORDER BY follower_count DESC LIMIT 1",
                    "relevance": 0.8
                }
            ],
            "documents": [
                {
                    "title": "粉丝数统计说明",
                    "content": "粉丝数为达人的关注者总数，每日更新",
                    "relevance": 0.7
                }
            ]
        }
        
        # 模拟训练
        mock.train.return_value = True
        
        return mock
    
    @staticmethod
    def create_mock_database_executor():
        """创建模拟数据库执行器"""
        mock = AsyncMock()
        
        # 模拟查询执行
        mock.execute_query.return_value = {
            "data": MockDataGenerator.generate_creator_data(5),
            "columns": ["creator_id", "creator_name", "follower_count", "category"],
            "row_count": 5,
            "execution_time": 0.5
        }
        
        return mock
    
    @staticmethod
    def create_mock_vanna_core():
        """创建模拟Vanna核心"""
        mock = AsyncMock()
        
        # 模拟查询处理
        mock.process_query.return_value = MockDataGenerator.generate_query_result()
        
        # 模拟上下文检索
        mock.retrieve_context.return_value = {
            "schemas": [],
            "examples": [],
            "documents": []
        }
        
        # 模拟SQL生成
        mock.generate_sql.return_value = {
            "sql": "SELECT * FROM creators LIMIT 10",
            "confidence": 0.8
        }
        
        # 模拟查询执行
        mock.execute_query.return_value = {
            "data": MockDataGenerator.generate_creator_data(10),
            "columns": ["creator_name", "follower_count"],
            "row_count": 10,
            "execution_time": 0.3
        }
        
        return mock


class TestAssertions:
    """测试断言工具"""
    
    @staticmethod
    def assert_api_response(response: APIResponse, success: bool = True, 
                          data_keys: List[str] = None):
        """断言API响应格式"""
        assert isinstance(response, APIResponse)
        assert response.success == success
        assert response.message is not None
        
        if success and data_keys:
            assert response.data is not None
            for key in data_keys:
                assert key in response.data
    
    @staticmethod
    def assert_query_result(result: QueryResult, min_rows: int = 0, 
                          required_columns: List[str] = None):
        """断言查询结果格式"""
        assert isinstance(result, QueryResult)
        assert isinstance(result.data, list)
        assert len(result.data) >= min_rows
        assert result.row_count == len(result.data)
        assert isinstance(result.execution_time, (int, float))
        
        if required_columns:
            assert all(col in result.columns for col in required_columns)
            if result.data:
                assert all(col in result.data[0] for col in required_columns)
    
    @staticmethod
    def assert_analysis_result(analysis: AnalysisResult, min_insights: int = 1,
                             min_recommendations: int = 1):
        """断言分析结果格式"""
        assert isinstance(analysis, AnalysisResult)
        assert isinstance(analysis.summary, str)
        assert len(analysis.summary) > 0
        assert isinstance(analysis.insights, list)
        assert len(analysis.insights) >= min_insights
        assert isinstance(analysis.recommendations, list)
        assert len(analysis.recommendations) >= min_recommendations
        assert isinstance(analysis.key_metrics, dict)
    
    @staticmethod
    def assert_visualization_code(viz: VisualizationCode):
        """断言可视化代码格式"""
        assert isinstance(viz, VisualizationCode)
        assert isinstance(viz.chart_type, str)
        assert len(viz.chart_type) > 0
        assert isinstance(viz.code, str)
        assert len(viz.code) > 0
        assert "import" in viz.code  # 应该包含导入语句
        assert isinstance(viz.title, str)
        assert isinstance(viz.description, str)
    
    @staticmethod
    def assert_report(report: Report):
        """断言报告格式"""
        assert isinstance(report, Report)
        TestAssertions.assert_analysis_result(report.analysis)
        assert isinstance(report.visualizations, list)
        for viz in report.visualizations:
            TestAssertions.assert_visualization_code(viz)
        TestAssertions.assert_query_result(report.raw_data)


class TestFixtures:
    """测试夹具工具"""
    
    @staticmethod
    def create_test_database_data():
        """创建测试数据库数据"""
        return {
            "creators": MockDataGenerator.generate_creator_data(20),
            "videos": MockDataGenerator.generate_video_data(100),
            "metrics": MockDataGenerator.generate_metrics_data(200)
        }
    
    @staticmethod
    def create_test_queries():
        """创建测试查询集合"""
        return [
            {
                "question": "今天涨粉最多的前10个达人是谁？",
                "expected_sql_keywords": ["ORDER BY", "DESC", "LIMIT 10"],
                "expected_columns": ["creator_name", "follower_growth"]
            },
            {
                "question": "美妆类视频的平均播放量是多少？",
                "expected_sql_keywords": ["AVG", "WHERE", "category"],
                "expected_columns": ["avg_views"]
            },
            {
                "question": "各个分类的达人数量分布",
                "expected_sql_keywords": ["COUNT", "GROUP BY", "category"],
                "expected_columns": ["category", "creator_count"]
            }
        ]
    
    @staticmethod
    def create_error_scenarios():
        """创建错误场景"""
        return [
            {
                "name": "API超时",
                "error": Exception("API request timeout"),
                "expected_type": "API_ERROR",
                "expected_retry": True
            },
            {
                "name": "数据库连接失败",
                "error": Exception("Database connection failed"),
                "expected_type": "DATABASE_ERROR",
                "expected_retry": True
            },
            {
                "name": "参数验证失败",
                "error": ValueError("Invalid parameter"),
                "expected_type": "VALIDATION_ERROR",
                "expected_retry": False
            }
        ]


# 导出常用的测试工具
__all__ = [
    'MockDataGenerator',
    'MockServices', 
    'TestAssertions',
    'TestFixtures'
]