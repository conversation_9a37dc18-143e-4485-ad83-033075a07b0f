"""
性能优化测试和基准测试
"""

import pytest
import asyncio
import time
import cProfile
import pstats
import io
from memory_profiler import profile
from unittest.mock import patch, AsyncMock
import gc
import psutil
from typing import Dict, Any, List

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from agents.agent_coordinator import AgentCoordinator
from tests.test_utils import MockDataGenerator, MockServices


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiler = None
        self.memory_usage = []
        self.start_time = None
        self.end_time = None
    
    def start_profiling(self):
        """开始性能分析"""
        self.profiler = cProfile.Profile()
        self.profiler.enable()
        self.start_time = time.time()
        gc.collect()  # 清理垃圾
    
    def stop_profiling(self):
        """停止性能分析"""
        if self.profiler:
            self.profiler.disable()
        self.end_time = time.time()
    
    def get_profile_stats(self, sort_by: str = 'cumulative') -> str:
        """获取性能分析统计"""
        if not self.profiler:
            return "No profiling data available"
        
        s = io.StringIO()
        ps = pstats.Stats(self.profiler, stream=s)
        ps.sort_stats(sort_by)
        ps.print_stats(20)  # 显示前20个最耗时的函数
        
        return s.getvalue()
    
    def get_memory_profile(self) -> Dict[str, Any]:
        """获取内存使用分析"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }


class TestPerformanceOptimization:
    """性能优化测试"""
    
    @pytest.fixture
    async def optimized_coordinator(self):
        """创建优化的协调器"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 创建优化的模拟服务
                    mock_vanna = AsyncMock()
                    mock_router = AsyncMock()
                    mock_display = AsyncMock()
                    
                    # 配置快速响应
                    async def optimized_response(*args, **kwargs):
                        # 模拟优化后的快速处理
                        await asyncio.sleep(0.001)  # 1ms
                        return {
                            "success": True,
                            "data": {
                                "type": "complete_analysis",
                                "raw_data": {"data": [], "columns": [], "row_count": 0},
                                "analysis": {"summary": "优化分析"},
                                "visualizations": []
                            }
                        }
                    
                    mock_router.process_user_input.side_effect = optimized_response
                    mock_display.create_report.return_value = MockDataGenerator.generate_report()
                    
                    mock_vanna_class.return_value = mock_vanna
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = mock_display
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    yield coordinator
                    
                    await coordinator.close()
    
    @pytest.mark.asyncio
    async def test_response_time_optimization(self, optimized_coordinator):
        """测试响应时间优化"""
        coordinator = optimized_coordinator
        profiler = PerformanceProfiler()
        
        # 基准测试
        print("执行响应时间基准测试...")
        
        response_times = []
        
        profiler.start_profiling()
        
        for i in range(100):
            start_time = time.time()
            
            response = await coordinator.process_user_request(
                f"优化测试{i}",
                f"opt_session_{i}",
                f"opt_user_{i}"
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
        
        profiler.stop_profiling()
        
        # 分析结果
        avg_response_time = sum(response_times) / len(response_times)
        min_response_time = min(response_times)
        max_response_time = max(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        
        print(f"\n=== 响应时间优化测试结果 ===")
        print(f"平均响应时间: {avg_response_time:.4f}s")
        print(f"最小响应时间: {min_response_time:.4f}s")
        print(f"最大响应时间: {max_response_time:.4f}s")
        print(f"P95响应时间: {p95_response_time:.4f}s")
        
        # 性能分析
        print("\n=== 性能分析 ===")
        print(profiler.get_profile_stats())
        
        # 优化目标断言
        assert avg_response_time <= 0.1  # 平均响应时间 <= 100ms
        assert p95_response_time <= 0.2  # P95响应时间 <= 200ms
        assert max_response_time <= 0.5  # 最大响应时间 <= 500ms
    
    @pytest.mark.asyncio
    async def test_memory_usage_optimization(self, optimized_coordinator):
        """测试内存使用优化"""
        coordinator = optimized_coordinator
        profiler = PerformanceProfiler()
        
        print("执行内存使用优化测试...")
        
        # 记录初始内存
        initial_memory = profiler.get_memory_profile()
        
        # 执行大量请求
        tasks = []
        for i in range(500):
            task = coordinator.process_user_request(
                f"内存优化测试{i}",
                f"mem_opt_session_{i}",
                f"mem_opt_user_{i}"
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        
        # 强制垃圾回收
        gc.collect()
        await asyncio.sleep(1)
        
        # 记录最终内存
        final_memory = profiler.get_memory_profile()
        
        memory_growth = final_memory['rss_mb'] - initial_memory['rss_mb']
        
        print(f"\n=== 内存使用优化测试结果 ===")
        print(f"初始内存: {initial_memory['rss_mb']:.2f}MB")
        print(f"最终内存: {final_memory['rss_mb']:.2f}MB")
        print(f"内存增长: {memory_growth:.2f}MB")
        print(f"内存增长率: {(memory_growth / initial_memory['rss_mb'] * 100):.2f}%")
        print(f"系统可用内存: {final_memory['available_mb']:.2f}MB")
        
        # 内存优化目标断言
        assert memory_growth <= 50  # 内存增长不超过50MB
        assert memory_growth / initial_memory['rss_mb'] <= 0.3  # 内存增长率不超过30%
    
    @pytest.mark.asyncio
    async def test_throughput_optimization(self, optimized_coordinator):
        """测试吞吐量优化"""
        coordinator = optimized_coordinator
        
        print("执行吞吐量优化测试...")
        
        # 测试不同并发级别的吞吐量
        concurrency_levels = [1, 5, 10, 20, 50]
        throughput_results = {}
        
        for concurrency in concurrency_levels:
            print(f"测试并发级别: {concurrency}")
            
            start_time = time.time()
            
            async def concurrent_requests():
                tasks = []
                for i in range(concurrency):
                    task = coordinator.process_user_request(
                        f"吞吐量测试{i}",
                        f"throughput_session_{i}",
                        f"throughput_user_{i}"
                    )
                    tasks.append(task)
                
                return await asyncio.gather(*tasks)
            
            # 执行多轮测试
            rounds = 10
            total_requests = 0
            
            for round_num in range(rounds):
                await concurrent_requests()
                total_requests += concurrency
            
            end_time = time.time()
            duration = end_time - start_time
            throughput = total_requests / duration
            
            throughput_results[concurrency] = {
                'throughput': throughput,
                'duration': duration,
                'total_requests': total_requests
            }
            
            print(f"  吞吐量: {throughput:.2f} req/s")
        
        print(f"\n=== 吞吐量优化测试结果 ===")
        for concurrency, result in throughput_results.items():
            print(f"并发{concurrency}: {result['throughput']:.2f} req/s")
        
        # 吞吐量优化目标断言
        max_throughput = max(result['throughput'] for result in throughput_results.values())
        assert max_throughput >= 100  # 最大吞吐量 >= 100 req/s
    
    @pytest.mark.asyncio
    async def test_resource_efficiency(self, optimized_coordinator):
        """测试资源效率"""
        coordinator = optimized_coordinator
        
        print("执行资源效率测试...")
        
        # 监控资源使用
        process = psutil.Process()
        
        # 记录基线资源使用
        baseline_cpu = process.cpu_percent(interval=1)
        baseline_memory = process.memory_info().rss / 1024 / 1024
        
        # 执行工作负载
        start_time = time.time()
        
        tasks = []
        for i in range(200):
            task = coordinator.process_user_request(
                f"资源效率测试{i}",
                f"efficiency_session_{i}",
                f"efficiency_user_{i}"
            )
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 记录工作负载后的资源使用
        workload_cpu = process.cpu_percent(interval=1)
        workload_memory = process.memory_info().rss / 1024 / 1024
        
        # 计算资源效率指标
        requests_per_mb = 200 / (workload_memory - baseline_memory) if workload_memory > baseline_memory else float('inf')
        requests_per_cpu_percent = 200 / (workload_cpu - baseline_cpu) if workload_cpu > baseline_cpu else float('inf')
        
        print(f"\n=== 资源效率测试结果 ===")
        print(f"基线CPU: {baseline_cpu:.2f}%")
        print(f"工作负载CPU: {workload_cpu:.2f}%")
        print(f"基线内存: {baseline_memory:.2f}MB")
        print(f"工作负载内存: {workload_memory:.2f}MB")
        print(f"处理时间: {duration:.2f}s")
        print(f"每MB内存处理请求数: {requests_per_mb:.2f}")
        print(f"每CPU百分点处理请求数: {requests_per_cpu_percent:.2f}")
        
        # 资源效率目标断言
        assert requests_per_mb >= 10  # 每MB内存至少处理10个请求
        assert duration <= 10  # 200个请求在10秒内完成


class TestCachingOptimization:
    """缓存优化测试"""
    
    @pytest.mark.asyncio
    async def test_response_caching(self):
        """测试响应缓存优化"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 模拟带缓存的服务
                    cache = {}
                    call_count = 0
                    
                    async def cached_response(user_input, session_id, user_id):
                        nonlocal call_count
                        call_count += 1
                        
                        # 简单的缓存逻辑
                        cache_key = f"{user_input}_{user_id}"
                        
                        if cache_key in cache:
                            # 缓存命中，快速返回
                            await asyncio.sleep(0.001)  # 1ms
                            return cache[cache_key]
                        else:
                            # 缓存未命中，模拟处理时间
                            await asyncio.sleep(0.1)  # 100ms
                            result = {
                                "success": True,
                                "data": {"type": "cached_response", "call_count": call_count}
                            }
                            cache[cache_key] = result
                            return result
                    
                    mock_router = AsyncMock()
                    mock_router.process_user_input.side_effect = cached_response
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 测试缓存效果
                    print("测试缓存优化效果...")
                    
                    # 第一次请求（缓存未命中）
                    start_time = time.time()
                    response1 = await coordinator.process_user_request(
                        "缓存测试查询", "cache_session", "cache_user"
                    )
                    first_request_time = time.time() - start_time
                    
                    # 第二次相同请求（缓存命中）
                    start_time = time.time()
                    response2 = await coordinator.process_user_request(
                        "缓存测试查询", "cache_session", "cache_user"
                    )
                    second_request_time = time.time() - start_time
                    
                    # 第三次相同请求（缓存命中）
                    start_time = time.time()
                    response3 = await coordinator.process_user_request(
                        "缓存测试查询", "cache_session", "cache_user"
                    )
                    third_request_time = time.time() - start_time
                    
                    await coordinator.close()
                    
                    print(f"\n=== 缓存优化测试结果 ===")
                    print(f"第一次请求时间: {first_request_time:.4f}s (缓存未命中)")
                    print(f"第二次请求时间: {second_request_time:.4f}s (缓存命中)")
                    print(f"第三次请求时间: {third_request_time:.4f}s (缓存命中)")
                    print(f"缓存加速比: {first_request_time / second_request_time:.2f}x")
                    
                    # 缓存优化断言
                    assert second_request_time < first_request_time * 0.2  # 缓存命中应该快5倍以上
                    assert third_request_time < first_request_time * 0.2
    
    @pytest.mark.asyncio
    async def test_connection_pooling(self):
        """测试连接池优化"""
        print("测试连接池优化...")
        
        # 模拟连接池
        connection_pool = asyncio.Queue(maxsize=10)
        
        # 初始化连接池
        for i in range(10):
            await connection_pool.put(f"connection_{i}")
        
        connection_usage = []
        
        async def use_connection(task_id: int):
            """使用连接池中的连接"""
            start_time = time.time()
            
            # 获取连接
            connection = await connection_pool.get()
            get_connection_time = time.time() - start_time
            
            try:
                # 模拟使用连接
                await asyncio.sleep(0.01)  # 10ms处理时间
                
                processing_time = time.time() - start_time - get_connection_time
                
                connection_usage.append({
                    'task_id': task_id,
                    'connection': connection,
                    'get_connection_time': get_connection_time,
                    'processing_time': processing_time,
                    'total_time': time.time() - start_time
                })
                
            finally:
                # 归还连接
                await connection_pool.put(connection)
        
        # 测试并发连接使用
        tasks = [use_connection(i) for i in range(50)]
        await asyncio.gather(*tasks)
        
        # 分析连接池效果
        avg_get_time = sum(usage['get_connection_time'] for usage in connection_usage) / len(connection_usage)
        max_get_time = max(usage['get_connection_time'] for usage in connection_usage)
        avg_total_time = sum(usage['total_time'] for usage in connection_usage) / len(connection_usage)
        
        print(f"\n=== 连接池优化测试结果 ===")
        print(f"连接池大小: 10")
        print(f"并发任务数: 50")
        print(f"平均获取连接时间: {avg_get_time:.4f}s")
        print(f"最大获取连接时间: {max_get_time:.4f}s")
        print(f"平均总处理时间: {avg_total_time:.4f}s")
        
        # 连接池优化断言
        assert avg_get_time <= 0.01  # 平均获取连接时间 <= 10ms
        assert max_get_time <= 0.1   # 最大获取连接时间 <= 100ms


class TestAsyncOptimization:
    """异步优化测试"""
    
    @pytest.mark.asyncio
    async def test_async_processing_efficiency(self):
        """测试异步处理效率"""
        print("测试异步处理效率...")
        
        # 同步处理模拟
        async def sync_style_processing():
            """模拟同步风格的处理"""
            start_time = time.time()
            
            for i in range(10):
                await asyncio.sleep(0.1)  # 模拟I/O操作
            
            return time.time() - start_time
        
        # 异步处理模拟
        async def async_style_processing():
            """模拟异步风格的处理"""
            start_time = time.time()
            
            tasks = [asyncio.sleep(0.1) for _ in range(10)]
            await asyncio.gather(*tasks)
            
            return time.time() - start_time
        
        # 测试同步风格
        sync_time = await sync_style_processing()
        
        # 测试异步风格
        async_time = await async_style_processing()
        
        print(f"\n=== 异步优化测试结果 ===")
        print(f"同步风格处理时间: {sync_time:.4f}s")
        print(f"异步风格处理时间: {async_time:.4f}s")
        print(f"异步加速比: {sync_time / async_time:.2f}x")
        
        # 异步优化断言
        assert async_time < sync_time * 0.2  # 异步处理应该快5倍以上
    
    @pytest.mark.asyncio
    async def test_batch_processing_optimization(self):
        """测试批处理优化"""
        print("测试批处理优化...")
        
        # 单个处理模拟
        async def individual_processing(items):
            """逐个处理项目"""
            start_time = time.time()
            results = []
            
            for item in items:
                await asyncio.sleep(0.01)  # 模拟处理时间
                results.append(f"processed_{item}")
            
            return results, time.time() - start_time
        
        # 批处理模拟
        async def batch_processing(items, batch_size=5):
            """批量处理项目"""
            start_time = time.time()
            results = []
            
            for i in range(0, len(items), batch_size):
                batch = items[i:i + batch_size]
                batch_tasks = [self._process_item(item) for item in batch]
                batch_results = await asyncio.gather(*batch_tasks)
                results.extend(batch_results)
            
            return results, time.time() - start_time
        
        # 测试数据
        test_items = list(range(50))
        
        # 测试单个处理
        individual_results, individual_time = await individual_processing(test_items)
        
        # 测试批处理
        batch_results, batch_time = await batch_processing(test_items)
        
        print(f"\n=== 批处理优化测试结果 ===")
        print(f"项目数量: {len(test_items)}")
        print(f"单个处理时间: {individual_time:.4f}s")
        print(f"批处理时间: {batch_time:.4f}s")
        print(f"批处理加速比: {individual_time / batch_time:.2f}x")
        
        # 批处理优化断言
        assert len(batch_results) == len(individual_results)
        assert batch_time < individual_time * 0.8  # 批处理应该快20%以上
    
    async def _process_item(self, item):
        """处理单个项目"""
        await asyncio.sleep(0.01)
        return f"processed_{item}"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])