# 需求文档

## 介绍

本项目旨在构建一个基于Vanna和千问模型的TikTok数据分析AI Agent系统。该系统通过多Agent协作的方式，能够理解用户的自然语言查询，自动生成SQL语句查询TikTok相关数据，并生成包含数据分析和可视化的智能报告。

系统采用离线训练+在线交互的架构模式，通过预先构建的知识库支持实时的数据查询和分析任务。

## 需求

### 需求 1 - 知识库训练系统

**用户故事:** 作为系统管理员，我希望能够构建和维护TikTok领域的知识库，以便为AI Agent提供准确的数据查询能力。

#### 验收标准

1. 当管理员提供TikTok数据表Schema(DDL)时，系统应当能够将其存储到Vanna知识库中
2. 当管理员提供业务文档(如涨粉量计算方式)时，系统应当能够将其嵌入到知识库中
3. 当管理员提供高质量的问题-SQL范例时，系统应当能够将其作为训练数据存储
4. 系统应当使用千问模型进行文本嵌入和知识库构建
5. 如果知识库更新，系统应当能够重新训练和更新嵌入向量

### 需求 2 - 交互与路由Agent

**用户故事:** 作为用户，我希望能够用自然语言提出关于TikTok数据的问题，系统能够理解我的意图并正确路由到相应的处理模块。

#### 验收标准

1. 当用户输入自然语言问题时，Agent应当能够接收并解析用户输入
2. 当问题涉及数据查询时，Agent应当能够识别这是数据分析任务
3. 当问题是闲聊内容时，Agent应当能够识别并提供适当的回应
4. 系统应当使用千问模型的Function Calling能力进行意图识别
5. 如果是数据查询任务，Agent应当将任务转发给Vanna核心处理模块
6. 如果输入格式不正确或无法理解，Agent应当提供友好的错误提示

### 需求 3 - Vanna数据查询核心

**用户故事:** 作为系统核心组件，我需要能够将自然语言问题转换为准确的SQL查询，并执行查询获取数据。

#### 验收标准

1. 当接收到数据查询任务时，系统应当使用RAG机制检索相关的Schema、文档和SQL范例
2. 当检索到相关上下文后，系统应当使用千问模型生成准确的SQL语句
3. 当SQL生成完成后，系统应当安全地连接数据库并执行查询
4. 当查询执行成功时，系统应当返回原始数据结果
5. 如果SQL执行失败，系统应当捕获错误并尝试重新生成SQL
6. 如果数据库连接失败，系统应当提供适当的错误处理
7. 系统应当记录所有SQL查询和执行结果用于后续优化

### 需求 4 - 结果合成与展示Agent

**用户故事:** 作为用户，我希望获得的不仅是原始数据，还包括有洞察力的分析总结和直观的数据可视化。

#### 验收标准

1. 当接收到原始数据时，Agent应当使用千问模型生成数据分析摘要
2. 当数据适合可视化时，Agent应当生成相应的图表代码
3. 当分析完成后，Agent应当将文字总结、图表和关键数据整合成最终报告
4. 系统应当支持多种图表类型(柱状图、折线图、饼图等)
5. 如果数据为空或异常，Agent应当提供相应的说明
6. 最终报告应当格式清晰、易于理解
7. 系统应当支持报告的导出功能

### 需求 5 - 系统集成与配置

**用户故事:** 作为开发者，我需要一个易于部署和配置的系统架构，支持各个组件的协调工作。

#### 验收标准

1. 系统应当提供统一的配置管理，包括模型配置、数据库连接等
2. 各个Agent之间应当通过标准化的接口进行通信
3. 系统应当支持异步处理，避免长时间查询阻塞用户界面
4. 当任何组件出现故障时，系统应当提供详细的错误日志
5. 系统应当支持水平扩展，能够处理并发用户请求
6. 如果千问模型API调用失败，系统应当有重试机制
7. 系统应当提供健康检查接口用于监控

### 需求 6 - 用户界面与体验

**用户故事:** 作为最终用户，我希望有一个直观友好的界面来与AI Agent交互，查看分析结果。

#### 验收标准

1. 系统应当提供Web界面供用户输入问题
2. 当处理查询时，界面应当显示处理进度
3. 当结果返回时，界面应当清晰地展示文字分析和图表
4. 用户应当能够查看历史查询记录
5. 如果查询时间较长，系统应当提供实时状态更新
6. 界面应当支持移动端访问
7. 系统应当提供查询结果的分享功能