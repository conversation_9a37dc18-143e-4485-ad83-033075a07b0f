# 设计文档

## 概述

TikTok AI Agent系统是一个基于多Agent协作的智能数据分析平台，采用Vanna框架和千问模型构建。系统通过离线知识库训练和在线实时交互的方式，为用户提供自然语言到SQL的转换能力，并生成包含数据分析和可视化的智能报告。

系统核心特点：
- 多Agent协作架构，职责分离
- 基于RAG的知识检索增强生成
- 支持自然语言到SQL的智能转换
- 自动化数据分析和可视化生成
- 异步处理和高并发支持

## 架构

### 整体架构图

```mermaid
graph TB
    subgraph "离线训练阶段"
        A1[TikTok Schema DDL] --> KB[Vanna知识库]
        A2[业务文档] --> KB
        A3[问题-SQL范例] --> KB
        KB --> EMB[千问模型嵌入]
    end
    
    subgraph "在线交互阶段"
        USER[用户输入] --> RA[路由Agent]
        RA --> VA[Vanna核心]
        VA --> DA[展示Agent]
        DA --> USER
        
        subgraph "Vanna内部流程"
            VA --> RAG[RAG检索]
            RAG --> SQL[SQL生成]
            SQL --> EXEC[SQL执行]
            EXEC --> RESULT[数据返回]
        end
    end
    
    subgraph "基础设施"
        DB[(TikTok数据库)]
        QWEN[千问模型API]
        CACHE[Redis缓存]
        LOG[日志系统]
    end
    
    VA --> DB
    RA --> QWEN
    VA --> QWEN
    DA --> QWEN
    VA --> CACHE
```

### 分层架构

1. **表示层 (Presentation Layer)**
   - Web UI界面 (Streamlit/Gradio)
   - FastAPI RESTful接口
   - WebSocket实时通信

2. **应用层 (Application Layer)**
   - 路由Agent服务
   - 展示Agent服务
   - 任务编排服务

3. **核心层 (Core Layer)**
   - Vanna核心引擎
   - RAG检索服务
   - SQL生成与执行引擎

4. **数据层 (Data Layer)**
   - TikTok业务数据库
   - Vanna知识库
   - 缓存层

5. **基础设施层 (Infrastructure Layer)**
   - 千问模型API
   - 消息队列
   - 监控与日志

## 组件和接口

### 1. 路由Agent (Router Agent)

**职责:** 用户交互入口，意图识别和任务分发

**核心接口:**
```python
class RouterAgent:
    async def process_user_input(self, user_input: str) -> AgentResponse
    async def classify_intent(self, input_text: str) -> IntentType
    async def route_to_handler(self, intent: IntentType, context: dict) -> HandlerResponse
```

**关键功能:**
- 自然语言理解和意图分类
- 基于千问模型的Function Calling
- 任务路由和上下文管理
- 用户会话状态维护

### 2. Vanna核心引擎 (Vanna Core Engine)

**职责:** Text-to-SQL转换和数据查询执行

**核心接口:**
```python
class VannaCore:
    async def generate_sql(self, question: str, context: dict) -> SQLResult
    async def execute_query(self, sql: str) -> QueryResult
    async def train_knowledge_base(self, training_data: TrainingData) -> bool
    async def retrieve_context(self, question: str) -> RetrievalResult
```

**关键功能:**
- RAG检索相关Schema和范例
- 基于千问模型的SQL生成
- 安全的SQL执行和结果返回
- 知识库管理和更新

### 3. 展示Agent (Display Agent)

**职责:** 数据分析和可视化报告生成

**核心接口:**
```python
class DisplayAgent:
    async def analyze_data(self, data: QueryResult) -> AnalysisResult
    async def generate_visualization(self, data: QueryResult) -> VisualizationCode
    async def create_report(self, analysis: AnalysisResult, viz: VisualizationCode) -> Report
```

**关键功能:**
- 基于千问模型的数据分析
- 自动化图表生成
- 报告格式化和展示
- 多媒体内容整合

### 4. 知识库管理器 (Knowledge Base Manager)

**职责:** 离线知识库构建和维护

**核心接口:**
```python
class KnowledgeBaseManager:
    async def add_schema(self, ddl: str) -> bool
    async def add_documentation(self, doc: str) -> bool
    async def add_sql_examples(self, examples: List[SQLExample]) -> bool
    async def update_embeddings(self) -> bool
```

### 5. FastAPI接口层 (API Layer)

**职责:** 提供RESTful API接口供外部系统调用

**核心接口:**
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

class QueryRequest(BaseModel):
    question: str
    user_id: str = None
    session_id: str = None

class QueryResponse(BaseModel):
    query_id: str
    sql: str
    data: List[Dict]
    analysis: str
    visualizations: List[Dict]
    execution_time: float

app = FastAPI(title="TikTok AI Agent API")

@app.post("/api/v1/query", response_model=QueryResponse)
async def process_query(request: QueryRequest)

@app.get("/api/v1/health")
async def health_check()

@app.get("/api/v1/status/{query_id}")
async def get_query_status(query_id: str)

@app.get("/api/v1/query/history/{user_id}")
async def get_user_query_history(user_id: str)
```

**API端点设计:**

1. **查询接口**
   - `POST /api/v1/query` - 处理自然语言查询
   - `GET /api/v1/query/{query_id}` - 获取查询结果
   - `GET /api/v1/query/history/{user_id}` - 获取用户查询历史

2. **系统管理接口**
   - `GET /api/v1/health` - 健康检查
   - `GET /api/v1/metrics` - 系统指标
   - `GET /api/v1/status` - 服务状态

**注意:** 知识库训练是离线预处理步骤，在系统启动前完成。API模式启动时会加载已训练好的知识库。

## 数据模型

### 核心数据结构

```python
@dataclass
class UserQuery:
    query_id: str
    user_id: str
    question: str
    timestamp: datetime
    session_id: str

@dataclass
class SQLResult:
    sql: str
    confidence: float
    context_used: List[str]
    generation_time: float

@dataclass
class QueryResult:
    data: List[Dict]
    columns: List[str]
    row_count: int
    execution_time: float

@dataclass
class AnalysisResult:
    summary: str
    insights: List[str]
    key_metrics: Dict[str, Any]
    recommendations: List[str]

@dataclass
class Report:
    text_analysis: str
    visualizations: List[VisualizationSpec]
    raw_data: QueryResult
    metadata: ReportMetadata
```

### 知识库数据模型

```python
@dataclass
class SchemaInfo:
    table_name: str
    ddl: str
    description: str
    embedding: List[float]

@dataclass
class BusinessDoc:
    doc_id: str
    title: str
    content: str
    category: str
    embedding: List[float]

@dataclass
class SQLExample:
    question: str
    sql: str
    explanation: str
    difficulty: str
    embedding: List[float]
```

## 错误处理

### 错误分类和处理策略

1. **用户输入错误**
   - 无效查询格式
   - 超出系统能力范围
   - 处理方式：友好提示，建议修正

2. **模型调用错误**
   - API限流或超时
   - 模型返回格式错误
   - 处理方式：重试机制，降级处理

3. **SQL执行错误**
   - 语法错误
   - 权限不足
   - 数据库连接失败
   - 处理方式：SQL修正，错误日志记录

4. **系统级错误**
   - 服务不可用
   - 资源耗尽
   - 处理方式：熔断机制，监控告警

### 错误处理流程

```python
class ErrorHandler:
    async def handle_error(self, error: Exception, context: dict) -> ErrorResponse:
        if isinstance(error, UserInputError):
            return self.create_user_friendly_response(error)
        elif isinstance(error, ModelAPIError):
            return await self.retry_with_backoff(error, context)
        elif isinstance(error, SQLExecutionError):
            return await self.attempt_sql_correction(error, context)
        else:
            return self.create_system_error_response(error)
```

## 测试策略

### 单元测试

1. **Agent组件测试**
   - 意图识别准确性
   - SQL生成质量
   - 数据分析逻辑

2. **工具函数测试**
   - 数据处理函数
   - 格式转换函数
   - 缓存操作函数

### 集成测试

1. **Agent间协作测试**
   - 端到端工作流测试
   - 数据传递完整性
   - 异常情况处理

2. **外部服务集成测试**
   - 千问模型API调用
   - 数据库连接和查询
   - 缓存服务集成

### 性能测试

1. **并发处理能力**
   - 多用户同时查询
   - 系统资源使用情况
   - 响应时间分布

2. **大数据量处理**
   - 复杂SQL查询性能
   - 大结果集处理能力
   - 内存使用优化

### 测试数据准备

```python
class TestDataManager:
    def create_mock_tiktok_data(self) -> Dict:
        return {
            "creators": self.generate_creator_data(1000),
            "videos": self.generate_video_data(10000),
            "metrics": self.generate_metrics_data(50000)
        }
    
    def create_test_queries(self) -> List[TestQuery]:
        return [
            TestQuery("过去一周哪个游戏达人播放量最高？", expected_sql="..."),
            TestQuery("美妆类视频的平均点赞率是多少？", expected_sql="..."),
            # 更多测试用例...
        ]
```

## 部署和运维

### 容器化部署

```dockerfile
# 基础镜像
FROM python:3.12-slim

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 应用代码
COPY src/ /app/src/
WORKDIR /app

# 启动命令
CMD ["python", "-m", "src.main"]
```

### 启动模式配置

系统支持两种启动模式：

1. **UI模式** - 提供Web界面交互
2. **API模式** - 提供FastAPI接口服务

```python
# main.py
import argparse
from src.ui.streamlit_app import run_ui_mode
from src.api.fastapi_app import run_api_mode

def main():
    parser = argparse.ArgumentParser(description='TikTok AI Agent')
    parser.add_argument('--mode', choices=['ui', 'api'], default='ui',
                       help='启动模式: ui(界面模式) 或 api(接口模式)')
    parser.add_argument('--host', default='0.0.0.0', help='服务主机地址')
    parser.add_argument('--port', type=int, default=8000, help='服务端口')
    
    args = parser.parse_args()
    
    if args.mode == 'ui':
        run_ui_mode(host=args.host, port=args.port)
    elif args.mode == 'api':
        run_api_mode(host=args.host, port=args.port)

if __name__ == "__main__":
    main()
```

### 服务编排

```yaml
# docker-compose.yml
version: '3.8'
services:
  # UI模式服务
  tiktok-agent-ui:
    build: .
    command: ["python", "main.py", "--mode", "ui", "--port", "8501"]
    ports:
      - "8501:8501"
    environment:
      - QWEN_API_KEY=${QWEN_API_KEY}
      - DB_CONNECTION=${DB_CONNECTION}
    depends_on:
      - redis
      - postgres
    
  # API模式服务
  tiktok-agent-api:
    build: .
    command: ["python", "main.py", "--mode", "api", "--port", "8000"]
    ports:
      - "8000:8000"
    environment:
      - QWEN_API_KEY=${QWEN_API_KEY}
      - DB_CONNECTION=${DB_CONNECTION}
    depends_on:
      - redis
      - postgres
    
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=tiktok_data
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 监控和日志

1. **应用监控**
   - 请求响应时间
   - 错误率统计
   - 资源使用情况

2. **业务监控**
   - SQL生成成功率
   - 用户查询类型分布
   - 模型调用频次

3. **日志管理**
   - 结构化日志格式
   - 分级日志记录
   - 日志聚合和分析