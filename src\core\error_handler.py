"""
统一错误处理系统
提供错误分类、重试机制和自动修正功能
"""

import asyncio
import logging
import traceback
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
import re

logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    API_ERROR = "api_error"
    DATABASE_ERROR = "database_error"
    SQL_ERROR = "sql_error"
    VALIDATION_ERROR = "validation_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTHENTICATION_ERROR = "auth_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    SYSTEM_ERROR = "system_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorInfo:
    """错误信息类"""
    
    def __init__(self, error_type: ErrorType, message: str, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 context: Optional[Dict[str, Any]] = None,
                 original_exception: Optional[Exception] = None):
        """
        初始化错误信息
        
        Args:
            error_type: 错误类型
            message: 错误消息
            severity: 错误严重程度
            context: 错误上下文
            original_exception: 原始异常
        """
        self.error_type = error_type
        self.message = message
        self.severity = severity
        self.context = context or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc() if original_exception else None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "error_type": self.error_type.value,
            "message": self.message,
            "severity": self.severity.value,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback
        }


class RetryConfig:
    """重试配置"""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0,
                 max_delay: float = 60.0, backoff_factor: float = 2.0,
                 retryable_errors: Optional[List[ErrorType]] = None):
        """
        初始化重试配置
        
        Args:
            max_attempts: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            backoff_factor: 退避因子
            retryable_errors: 可重试的错误类型
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.retryable_errors = retryable_errors or [
            ErrorType.NETWORK_ERROR,
            ErrorType.TIMEOUT_ERROR,
            ErrorType.RATE_LIMIT_ERROR,
            ErrorType.API_ERROR
        ]
    
    def calculate_delay(self, attempt: int) -> float:
        """计算延迟时间"""
        delay = self.base_delay * (self.backoff_factor ** (attempt - 1))
        return min(delay, self.max_delay)
    
    def is_retryable(self, error_type: ErrorType) -> bool:
        """判断错误是否可重试"""
        return error_type in self.retryable_errors


class ErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        """初始化错误处理器"""
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        self.retry_config = RetryConfig()
        
        # SQL错误修正规则
        self.sql_fix_rules = [
            (r"column \"(\w+)\" does not exist", self._fix_column_not_exist),
            (r"table \"(\w+)\" does not exist", self._fix_table_not_exist),
            (r"syntax error at or near \"(\w+)\"", self._fix_syntax_error),
            (r"division by zero", self._fix_division_by_zero),
            (r"invalid input syntax for type (\w+)", self._fix_type_error)
        ]
        
        logger.info("ErrorHandler initialized")
    
    def classify_error(self, exception: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
        """
        分类错误
        
        Args:
            exception: 异常对象
            context: 错误上下文
            
        Returns:
            ErrorInfo: 错误信息
        """
        try:
            error_message = str(exception)
            error_type = self._determine_error_type(exception, error_message)
            severity = self._determine_severity(error_type, exception)
            
            error_info = ErrorInfo(
                error_type=error_type,
                message=error_message,
                severity=severity,
                context=context,
                original_exception=exception
            )
            
            # 记录错误历史
            self._record_error(error_info)
            
            return error_info
            
        except Exception as e:
            logger.error(f"Failed to classify error: {e}")
            return ErrorInfo(
                error_type=ErrorType.UNKNOWN_ERROR,
                message=str(exception),
                severity=ErrorSeverity.MEDIUM,
                context=context,
                original_exception=exception
            )
    
    def _determine_error_type(self, exception: Exception, message: str) -> ErrorType:
        """确定错误类型"""
        message_lower = message.lower()
        
        # 网络错误
        if any(keyword in message_lower for keyword in ['connection', 'network', 'timeout', 'unreachable']):
            return ErrorType.NETWORK_ERROR
        
        # API错误
        if any(keyword in message_lower for keyword in ['api', 'http', 'status code', 'unauthorized']):
            return ErrorType.API_ERROR
        
        # 数据库错误
        if any(keyword in message_lower for keyword in ['database', 'connection refused', 'psycopg2']):
            return ErrorType.DATABASE_ERROR
        
        # SQL错误
        if any(keyword in message_lower for keyword in ['sql', 'syntax error', 'column', 'table']):
            return ErrorType.SQL_ERROR
        
        # 验证错误
        if any(keyword in message_lower for keyword in ['validation', 'invalid', 'required']):
            return ErrorType.VALIDATION_ERROR
        
        # 超时错误
        if 'timeout' in message_lower:
            return ErrorType.TIMEOUT_ERROR
        
        # 认证错误
        if any(keyword in message_lower for keyword in ['auth', 'unauthorized', 'forbidden', 'api key']):
            return ErrorType.AUTHENTICATION_ERROR
        
        # 速率限制错误
        if any(keyword in message_lower for keyword in ['rate limit', 'too many requests', '429']):
            return ErrorType.RATE_LIMIT_ERROR
        
        # 根据异常类型判断
        if isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorType.NETWORK_ERROR
        elif isinstance(exception, ValueError):
            return ErrorType.VALIDATION_ERROR
        elif isinstance(exception, PermissionError):
            return ErrorType.AUTHENTICATION_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    def _determine_severity(self, error_type: ErrorType, exception: Exception) -> ErrorSeverity:
        """确定错误严重程度"""
        # 关键错误
        if error_type in [ErrorType.SYSTEM_ERROR, ErrorType.DATABASE_ERROR]:
            return ErrorSeverity.CRITICAL
        
        # 高严重程度错误
        if error_type in [ErrorType.AUTHENTICATION_ERROR, ErrorType.SQL_ERROR]:
            return ErrorSeverity.HIGH
        
        # 中等严重程度错误
        if error_type in [ErrorType.API_ERROR, ErrorType.NETWORK_ERROR]:
            return ErrorSeverity.MEDIUM
        
        # 低严重程度错误
        return ErrorSeverity.LOW
    
    def _record_error(self, error_info: ErrorInfo):
        """记录错误历史"""
        self.error_history.append(error_info)
        
        # 限制历史记录大小
        if len(self.error_history) > self.max_history_size:
            self.error_history = self.error_history[-self.max_history_size:]
        
        # 记录日志
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.WARNING)
        
        logger.log(log_level, f"Error recorded: {error_info.error_type.value} - {error_info.message}")
    
    async def retry_with_backoff(self, func: Callable, *args, 
                               retry_config: Optional[RetryConfig] = None, **kwargs) -> Any:
        """
        带退避的重试机制
        
        Args:
            func: 要重试的函数
            *args: 函数参数
            retry_config: 重试配置
            **kwargs: 函数关键字参数
            
        Returns:
            Any: 函数执行结果
        """
        config = retry_config or self.retry_config
        last_error = None
        
        for attempt in range(1, config.max_attempts + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
                    
            except Exception as e:
                error_info = self.classify_error(e, {"attempt": attempt, "function": func.__name__})
                last_error = error_info
                
                # 检查是否可重试
                if not config.is_retryable(error_info.error_type):
                    logger.warning(f"Error not retryable: {error_info.error_type.value}")
                    raise e
                
                # 如果是最后一次尝试，直接抛出异常
                if attempt == config.max_attempts:
                    logger.error(f"All retry attempts failed for {func.__name__}")
                    raise e
                
                # 计算延迟时间并等待
                delay = config.calculate_delay(attempt)
                logger.warning(f"Attempt {attempt} failed, retrying in {delay:.2f}s: {error_info.message}")
                await asyncio.sleep(delay)
        
        # 理论上不会到达这里
        if last_error:
            raise last_error.original_exception
        else:
            raise RuntimeError("Retry failed without error information")
    
    def fix_sql_error(self, sql: str, error_message: str) -> Optional[str]:
        """
        自动修正SQL错误
        
        Args:
            sql: 原始SQL语句
            error_message: 错误消息
            
        Returns:
            Optional[str]: 修正后的SQL语句
        """
        try:
            for pattern, fix_func in self.sql_fix_rules:
                match = re.search(pattern, error_message, re.IGNORECASE)
                if match:
                    fixed_sql = fix_func(sql, match)
                    if fixed_sql and fixed_sql != sql:
                        logger.info(f"SQL auto-fixed: {pattern}")
                        return fixed_sql
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to fix SQL error: {e}")
            return None
    
    def _fix_column_not_exist(self, sql: str, match: re.Match) -> Optional[str]:
        """修正列不存在错误"""
        column_name = match.group(1)
        
        # 常见列名映射
        column_mappings = {
            'user_name': 'username',
            'user_id': 'id',
            'follower_count': 'followers',
            'video_count': 'videos',
            'view_count': 'views',
            'like_count': 'likes',
            'comment_count': 'comments',
            'share_count': 'shares'
        }
        
        if column_name in column_mappings:
            return sql.replace(column_name, column_mappings[column_name])
        
        return None
    
    def _fix_table_not_exist(self, sql: str, match: re.Match) -> Optional[str]:
        """修正表不存在错误"""
        table_name = match.group(1)
        
        # 常见表名映射
        table_mappings = {
            'user': 'creators',
            'users': 'creators',
            'video': 'videos',
            'creator': 'creators',
            'daily_metric': 'daily_metrics',
            'metrics': 'daily_metrics'
        }
        
        if table_name in table_mappings:
            return sql.replace(table_name, table_mappings[table_name])
        
        return None
    
    def _fix_syntax_error(self, sql: str, match: re.Match) -> Optional[str]:
        """修正语法错误"""
        error_token = match.group(1)
        
        # 常见语法错误修正
        if error_token.upper() == 'LIMIT':
            # 可能是PostgreSQL vs MySQL语法差异
            if 'LIMIT' in sql.upper() and 'OFFSET' not in sql.upper():
                return sql  # 暂时不修改
        
        return None
    
    def _fix_division_by_zero(self, sql: str, match: re.Match) -> Optional[str]:
        """修正除零错误"""
        # 添加NULLIF或CASE WHEN条件
        if '/' in sql:
            # 简单的除零保护
            sql = re.sub(r'(\w+)\s*/\s*(\w+)', r'CASE WHEN \2 = 0 THEN 0 ELSE \1 / \2 END', sql)
            return sql
        
        return None
    
    def _fix_type_error(self, sql: str, match: re.Match) -> Optional[str]:
        """修正类型错误"""
        data_type = match.group(1)
        
        if data_type.lower() == 'integer':
            # 添加类型转换
            sql = re.sub(r"'(\d+)'", r'\1', sql)  # 移除数字周围的引号
            return sql
        
        return None
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        if not self.error_history:
            return {"total_errors": 0}
        
        # 按类型统计
        type_counts = {}
        severity_counts = {}
        recent_errors = []
        
        # 最近24小时的错误
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for error in self.error_history:
            # 类型统计
            error_type = error.error_type.value
            type_counts[error_type] = type_counts.get(error_type, 0) + 1
            
            # 严重程度统计
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            # 最近错误
            if error.timestamp >= cutoff_time:
                recent_errors.append(error.to_dict())
        
        return {
            "total_errors": len(self.error_history),
            "error_types": type_counts,
            "error_severities": severity_counts,
            "recent_errors_24h": len(recent_errors),
            "recent_errors": recent_errors[-10:]  # 最近10个错误
        }
    
    def clear_error_history(self):
        """清空错误历史"""
        self.error_history.clear()
        logger.info("Error history cleared")


# 全局错误处理器实例
_error_handler_instance: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _error_handler_instance
    
    if _error_handler_instance is None:
        _error_handler_instance = ErrorHandler()
    
    return _error_handler_instance


def handle_error(exception: Exception, context: Optional[Dict[str, Any]] = None) -> ErrorInfo:
    """处理错误的便捷函数"""
    error_handler = get_error_handler()
    return error_handler.classify_error(exception, context)


async def retry_on_error(func: Callable, *args, **kwargs) -> Any:
    """重试执行函数的便捷函数"""
    error_handler = get_error_handler()
    return await error_handler.retry_with_backoff(func, *args, **kwargs)


def fix_sql(sql: str, error_message: str) -> Optional[str]:
    """修正SQL的便捷函数"""
    error_handler = get_error_handler()
    return error_handler.fix_sql_error(sql, error_message)