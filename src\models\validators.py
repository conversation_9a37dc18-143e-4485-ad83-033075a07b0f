"""数据模型验证器"""

import re
from typing import Any, Dict, List

from pydantic import validator


class QueryValidators:
    """查询相关验证器"""
    
    @staticmethod
    @validator('question')
    def validate_question(cls, v: str) -> str:
        """验证问题格式"""
        if not v or not v.strip():
            raise ValueError("问题不能为空")
        
        if len(v.strip()) < 2:
            raise ValueError("问题长度至少2个字符")
            
        if len(v) > 1000:
            raise ValueError("问题长度不能超过1000个字符")
            
        return v.strip()
    
    @staticmethod
    @validator('confidence')
    def validate_confidence(cls, v: float) -> float:
        """验证置信度"""
        if not 0.0 <= v <= 1.0:
            raise ValueError("置信度必须在0.0到1.0之间")
        return v


class SQLValidators:
    """SQL相关验证器"""
    
    @staticmethod
    @validator('sql')
    def validate_sql(cls, v: str) -> str:
        """验证SQL语句"""
        if not v or not v.strip():
            raise ValueError("SQL语句不能为空")
            
        # 基础SQL关键词检查
        sql_lower = v.lower().strip()
        if not any(keyword in sql_lower for keyword in ['select', 'insert', 'update', 'delete']):
            raise ValueError("无效的SQL语句")
            
        # 危险操作检查
        dangerous_keywords = ['drop', 'truncate', 'delete from', 'alter table']
        if any(keyword in sql_lower for keyword in dangerous_keywords):
            raise ValueError("包含危险的SQL操作")
            
        return v.strip()


class KnowledgeValidators:
    """知识库相关验证器"""
    
    @staticmethod
    @validator('table_name')
    def validate_table_name(cls, v: str) -> str:
        """验证表名"""
        if not v or not v.strip():
            raise ValueError("表名不能为空")
            
        # 表名格式检查
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', v):
            raise ValueError("表名格式不正确")
            
        return v.strip().lower()
    
    @staticmethod
    @validator('ddl')
    def validate_ddl(cls, v: str) -> str:
        """验证DDL语句"""
        if not v or not v.strip():
            raise ValueError("DDL语句不能为空")
            
        ddl_lower = v.lower().strip()
        if not ddl_lower.startswith('create table'):
            raise ValueError("DDL必须是CREATE TABLE语句")
            
        return v.strip()
    
    @staticmethod
    @validator('embedding')
    def validate_embedding(cls, v: List[float]) -> List[float]:
        """验证嵌入向量"""
        if v is None:
            return v
            
        if not isinstance(v, list):
            raise ValueError("嵌入向量必须是列表")
            
        if len(v) == 0:
            raise ValueError("嵌入向量不能为空")
            
        if not all(isinstance(x, (int, float)) for x in v):
            raise ValueError("嵌入向量必须包含数字")
            
        return v


class APIValidators:
    """API相关验证器"""
    
    @staticmethod
    @validator('user_id')
    def validate_user_id(cls, v: str) -> str:
        """验证用户ID"""
        if v is None:
            return v
            
        if not v.strip():
            raise ValueError("用户ID不能为空字符串")
            
        if len(v) > 255:
            raise ValueError("用户ID长度不能超过255个字符")
            
        return v.strip()
    
    @staticmethod
    @validator('session_id')
    def validate_session_id(cls, v: str) -> str:
        """验证会话ID"""
        if v is None:
            return v
            
        if not v.strip():
            raise ValueError("会话ID不能为空字符串")
            
        if len(v) > 255:
            raise ValueError("会话ID长度不能超过255个字符")
            
        return v.strip()


def validate_chart_type(chart_type: str) -> bool:
    """验证图表类型"""
    valid_types = [
        'bar', 'line', 'pie', 'scatter', 'histogram',
        'box', 'violin', 'heatmap', 'area', 'funnel'
    ]
    return chart_type.lower() in valid_types


def validate_query_data(data: List[Dict[str, Any]]) -> bool:
    """验证查询数据格式"""
    if not isinstance(data, list):
        return False
        
    if len(data) == 0:
        return True  # 空结果是有效的
        
    # 检查所有行是否有相同的键
    if not data:
        return True
        
    first_keys = set(data[0].keys()) if data else set()
    return all(set(row.keys()) == first_keys for row in data)


def sanitize_sql_input(sql: str) -> str:
    """清理SQL输入"""
    if not sql:
        return ""
        
    # 移除注释
    sql = re.sub(r'--.*$', '', sql, flags=re.MULTILINE)
    sql = re.sub(r'/\*.*?\*/', '', sql, flags=re.DOTALL)
    
    # 移除多余空白
    sql = ' '.join(sql.split())
    
    return sql.strip()