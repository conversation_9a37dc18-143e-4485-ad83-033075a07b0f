#!/usr/bin/env python3
"""
测试知识库修复后的查询功能
验证"粉丝数最多的达人"查询是否能正确生成SQL
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_knowledge_base():
    """测试知识库查询功能"""
    try:
        from core.knowledge_base import KnowledgeBaseManager
        from core.config import load_config

        print("🔍 测试知识库修复后的查询功能...")

        # 加载配置
        config = load_config()
        print("✅ 配置加载成功")

        # 创建知识库管理器
        kb_manager = KnowledgeBaseManager(db_path="data/knowledge_base.db")
        print("✅ 知识库管理器初始化成功")

        # 1. 检查表结构
        print("\n📊 检查知识库中的表结构...")
        schemas = await kb_manager.get_all_schemas()
        print(f"知识库中共有 {len(schemas)} 个表:")
        for schema in schemas:
            print(f"  - {schema.table_name}: {schema.description}")

        # 2. 测试相似度搜索
        print("\n🔍 测试查询: '粉丝数最多的达人是哪个'")
        test_question = "粉丝数最多的达人是哪个"

        result = await kb_manager.similarity_search(test_question, top_k=5)
        print(f"检索到 {result.total_results} 个相关项目")

        if result.schemas:
            print(f"相关表结构 ({len(result.schemas)} 个):")
            for schema in result.schemas:
                print(f"  - {schema.table_name}: {schema.description}")
                # 检查是否包含正确的字段
                if "follower_count" in schema.ddl:
                    print(f"    ✅ 包含 follower_count 字段")
                else:
                    print(f"    ❌ 缺少 follower_count 字段")

        if result.sql_examples:
            print(f"相关SQL示例 ({len(result.sql_examples)} 个):")
            for example in result.sql_examples:
                print(f"  - 问题: {example.question}")
                print(f"    SQL: {example.sql[:100]}...")
                # 检查SQL是否使用正确的表名
                if "at_tiktok_author_pool" in example.sql:
                    print(f"    ✅ 使用正确的表名 at_tiktok_author_pool")
                else:
                    print(f"    ❌ 使用了错误的表名")

        # 3. 检查特定的SQL示例
        print("\n📝 检查特定的SQL示例...")
        all_examples = await kb_manager.get_all_sql_examples()
        fan_examples = [ex for ex in all_examples if "粉丝" in ex.question]

        if fan_examples:
            print(f"找到 {len(fan_examples)} 个与粉丝相关的示例:")
            for example in fan_examples:
                print(f"  - {example.question}")
                print(f"    SQL: {example.sql}")
                print()
        else:
            print("❌ 没有找到与粉丝相关的SQL示例")

        await kb_manager.close()
        print("✅ 测试完成")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 设置环境变量
    os.environ.setdefault("QWEN_API_KEY", "test_key")

    success = asyncio.run(test_knowledge_base())
    if success:
        print("\n🎉 知识库修复验证成功！")
        print("现在系统应该能正确处理'粉丝数最多的达人'查询了。")
    else:
        print("\n❌ 知识库修复验证失败，需要进一步检查。")
