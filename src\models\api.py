"""API相关数据模型"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .query import AnalysisResult, QueryResult, VisualizationSpec


class QueryRequest(BaseModel):
    """查询请求模型"""
    question: str = Field(..., description="用户问题", min_length=1)
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    
    class Config:
        json_schema_extra = {
            "example": {
                "question": "过去一周哪个游戏达人播放量最高？",
                "user_id": "user123",
                "session_id": "session456"
            }
        }


class QueryResponse(BaseModel):
    """查询响应模型"""
    query_id: UUID = Field(..., description="查询ID")
    sql: str = Field(..., description="生成的SQL")
    data: List[Dict[str, Any]] = Field(..., description="查询数据")
    analysis: str = Field(..., description="数据分析")
    visualizations: List[Dict[str, Any]] = Field(default_factory=list, description="可视化图表")
    execution_time: float = Field(..., description="总执行时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query_id": "123e4567-e89b-12d3-a456-************",
                "sql": "SELECT username, SUM(view_count) FROM...",
                "data": [{"username": "gamer123", "total_views": 1000000}],
                "analysis": "根据数据显示，过去一周播放量最高的游戏达人是...",
                "visualizations": [],
                "execution_time": 2.5
            }
        }


class QueryStatusResponse(BaseModel):
    """查询状态响应模型"""
    query_id: UUID = Field(..., description="查询ID")
    status: str = Field(..., description="查询状态")
    progress: float = Field(ge=0.0, le=1.0, description="进度百分比")
    message: str = Field(default="", description="状态消息")
    result: Optional[QueryResponse] = Field(None, description="查询结果")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: str = Field(..., description="检查时间")
    version: str = Field(..., description="版本号")
    dependencies: Dict[str, str] = Field(default_factory=dict, description="依赖服务状态")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: str = Field(..., description="错误时间")


class UserQueryHistory(BaseModel):
    """用户查询历史模型"""
    queries: List[Dict[str, Any]] = Field(default_factory=list)
    total: int = Field(ge=0, description="总数量")
    page: int = Field(ge=1, description="页码")
    page_size: int = Field(ge=1, le=100, description="每页大小")