"""
压力测试和极限测试
"""

import pytest
import asyncio
import time
import threading
import psutil
from unittest.mock import patch, AsyncMock
from typing import List, Dict, Any

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from agents.agent_coordinator import AgentCoordinator
from tests.test_utils import MockDataGenerator, MockServices


class StressTestMetrics:
    """压力测试指标收集器"""
    
    def __init__(self):
        self.requests_sent = 0
        self.requests_completed = 0
        self.requests_failed = 0
        self.response_times = []
        self.error_messages = []
        self.system_metrics = []
        self.start_time = None
        self.end_time = None
    
    def record_request_sent(self):
        """记录发送的请求"""
        self.requests_sent += 1
    
    def record_request_completed(self, response_time: float, success: bool, error_msg: str = None):
        """记录完成的请求"""
        self.requests_completed += 1
        self.response_times.append(response_time)
        
        if not success:
            self.requests_failed += 1
            if error_msg:
                self.error_messages.append(error_msg)
    
    def record_system_metrics(self):
        """记录系统指标"""
        process = psutil.Process()
        self.system_metrics.append({
            'timestamp': time.time(),
            'memory_mb': process.memory_info().rss / 1024 / 1024,
            'cpu_percent': process.cpu_percent(),
            'open_files': len(process.open_files()),
            'threads': process.num_threads()
        })
    
    def get_summary(self) -> Dict[str, Any]:
        """获取压力测试摘要"""
        duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        return {
            'duration': duration,
            'requests_sent': self.requests_sent,
            'requests_completed': self.requests_completed,
            'requests_failed': self.requests_failed,
            'success_rate': (self.requests_completed - self.requests_failed) / self.requests_completed if self.requests_completed > 0 else 0,
            'throughput': self.requests_completed / duration if duration > 0 else 0,
            'avg_response_time': sum(self.response_times) / len(self.response_times) if self.response_times else 0,
            'max_response_time': max(self.response_times) if self.response_times else 0,
            'min_response_time': min(self.response_times) if self.response_times else 0,
            'error_rate': self.requests_failed / self.requests_completed if self.requests_completed > 0 else 0,
            'unique_errors': len(set(self.error_messages)),
            'peak_memory_mb': max([m['memory_mb'] for m in self.system_metrics]) if self.system_metrics else 0,
            'peak_cpu_percent': max([m['cpu_percent'] for m in self.system_metrics]) if self.system_metrics else 0,
            'max_threads': max([m['threads'] for m in self.system_metrics]) if self.system_metrics else 0
        }


class TestExtremeLoad:
    """极限负载测试"""
    
    @pytest.fixture
    async def coordinator_with_realistic_mocks(self):
        """创建带真实响应时间的协调器"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    mock_vanna = AsyncMock()
                    mock_router = AsyncMock()
                    mock_display = AsyncMock()
                    
                    mock_vanna_class.return_value = mock_vanna
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = mock_display
                    
                    # 配置真实的响应时间和偶发错误
                    async def realistic_response(*args, **kwargs):
                        import random
                        
                        # 模拟真实的处理时间分布
                        if random.random() < 0.1:  # 10%的慢查询
                            await asyncio.sleep(random.uniform(1.0, 3.0))
                        else:
                            await asyncio.sleep(random.uniform(0.1, 0.5))
                        
                        # 模拟偶发错误
                        if random.random() < 0.05:  # 5%的错误率
                            raise Exception("模拟服务错误")
                        
                        return {
                            "success": True,
                            "message": "处理完成",
                            "data": {
                                "type": "complete_analysis",
                                "raw_data": {"data": MockDataGenerator.generate_creator_data(10)},
                                "analysis": {"summary": "分析完成"},
                                "visualizations": []
                            }
                        }
                    
                    mock_router.process_user_request.side_effect = realistic_response
                    mock_display.create_report.return_value = MockDataGenerator.generate_report()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    yield coordinator
                    
                    await coordinator.close()
    
    @pytest.mark.asyncio
    async def test_maximum_concurrent_users(self, coordinator_with_realistic_mocks):
        """测试最大并发用户数"""
        coordinator = coordinator_with_realistic_mocks
        metrics = StressTestMetrics()
        
        # 逐步增加并发用户数，找到系统极限
        concurrent_levels = [10, 25, 50, 100, 200]
        results = {}
        
        for concurrent_users in concurrent_levels:
            print(f"\n测试 {concurrent_users} 并发用户...")
            
            level_metrics = StressTestMetrics()
            level_metrics.start_time = time.time()
            
            # 启动系统监控
            monitoring_active = True
            
            def system_monitor():
                while monitoring_active:
                    level_metrics.record_system_metrics()
                    time.sleep(0.5)
            
            monitor_thread = threading.Thread(target=system_monitor)
            monitor_thread.start()
            
            async def user_load(user_id: int):
                """单个用户的负载"""
                for request_id in range(5):  # 每个用户5个请求
                    level_metrics.record_request_sent()
                    start_time = time.time()
                    
                    try:
                        response = await coordinator.process_user_request(
                            f"极限测试用户{user_id}请求{request_id}",
                            f"stress_session_{user_id}",
                            f"stress_user_{user_id}"
                        )
                        
                        response_time = time.time() - start_time
                        success = response.get("success", False)
                        level_metrics.record_request_completed(response_time, success)
                        
                    except Exception as e:
                        response_time = time.time() - start_time
                        level_metrics.record_request_completed(response_time, False, str(e))
            
            # 执行并发负载
            try:
                tasks = [user_load(i) for i in range(concurrent_users)]
                await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=60)
                
                level_metrics.end_time = time.time()
                monitoring_active = False
                monitor_thread.join()
                
                summary = level_metrics.get_summary()
                results[concurrent_users] = summary
                
                print(f"成功率: {summary['success_rate']:.2%}")
                print(f"平均响应时间: {summary['avg_response_time']:.3f}s")
                print(f"吞吐量: {summary['throughput']:.2f} req/s")
                print(f"峰值内存: {summary['peak_memory_mb']:.2f}MB")
                
                # 如果成功率低于80%，停止增加负载
                if summary['success_rate'] < 0.8:
                    print(f"在 {concurrent_users} 并发用户时系统开始不稳定")
                    break
                    
            except asyncio.TimeoutError:
                print(f"在 {concurrent_users} 并发用户时发生超时")
                monitoring_active = False
                monitor_thread.join()
                break
        
        # 分析结果
        print(f"\n=== 最大并发用户测试结果 ===")
        for level, summary in results.items():
            print(f"{level} 用户: 成功率 {summary['success_rate']:.2%}, "
                  f"响应时间 {summary['avg_response_time']:.3f}s, "
                  f"吞吐量 {summary['throughput']:.2f} req/s")
        
        # 找到最佳并发级别
        stable_levels = [level for level, summary in results.items() if summary['success_rate'] >= 0.95]
        if stable_levels:
            max_stable_level = max(stable_levels)
            print(f"推荐最大并发用户数: {max_stable_level}")
            assert max_stable_level >= 25  # 系统应该至少支持25个并发用户
    
    @pytest.mark.asyncio
    async def test_resource_exhaustion(self, coordinator_with_realistic_mocks):
        """测试资源耗尽场景"""
        coordinator = coordinator_with_realistic_mocks
        metrics = StressTestMetrics()
        
        print("开始资源耗尽测试...")
        metrics.start_time = time.time()
        
        # 启动系统监控
        monitoring_active = True
        
        def intensive_monitor():
            while monitoring_active:
                metrics.record_system_metrics()
                time.sleep(0.1)  # 更频繁的监控
        
        monitor_thread = threading.Thread(target=intensive_monitor)
        monitor_thread.start()
        
        # 创建大量长时间运行的任务
        async def resource_intensive_task(task_id: int):
            """资源密集型任务"""
            try:
                # 创建大量数据
                large_data = MockDataGenerator.generate_creator_data(1000)
                
                # 执行多个请求
                for i in range(10):
                    metrics.record_request_sent()
                    start_time = time.time()
                    
                    response = await coordinator.process_user_request(
                        f"资源密集任务{task_id}_{i}",
                        f"resource_session_{task_id}_{i}",
                        f"resource_user_{task_id}"
                    )
                    
                    response_time = time.time() - start_time
                    success = response.get("success", False)
                    metrics.record_request_completed(response_time, success)
                    
            except Exception as e:
                metrics.record_request_completed(1.0, False, str(e))
        
        # 启动大量资源密集型任务
        tasks = [resource_intensive_task(i) for i in range(50)]
        
        try:
            await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=120)
        except asyncio.TimeoutError:
            print("资源耗尽测试超时")
        
        metrics.end_time = time.time()
        monitoring_active = False
        monitor_thread.join()
        
        summary = metrics.get_summary()
        
        print(f"\n=== 资源耗尽测试结果 ===")
        print(f"测试时长: {summary['duration']:.2f}s")
        print(f"发送请求: {summary['requests_sent']}")
        print(f"完成请求: {summary['requests_completed']}")
        print(f"失败请求: {summary['requests_failed']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"峰值内存: {summary['peak_memory_mb']:.2f}MB")
        print(f"峰值CPU: {summary['peak_cpu_percent']:.2f}%")
        print(f"最大线程数: {summary['max_threads']}")
        
        # 资源使用断言
        assert summary['peak_memory_mb'] <= 1000  # 峰值内存不超过1GB
        assert summary['success_rate'] >= 0.5  # 即使在资源压力下，成功率也应该 >= 50%
    
    @pytest.mark.asyncio
    async def test_connection_pool_exhaustion(self, coordinator_with_realistic_mocks):
        """测试连接池耗尽"""
        coordinator = coordinator_with_realistic_mocks
        
        # 模拟连接池限制
        connection_semaphore = asyncio.Semaphore(10)  # 限制10个并发连接
        
        async def limited_connection_task(task_id: int):
            """受连接限制的任务"""
            async with connection_semaphore:
                try:
                    response = await coordinator.process_user_request(
                        f"连接池测试{task_id}",
                        f"conn_session_{task_id}",
                        f"conn_user_{task_id}"
                    )
                    return response.get("success", False)
                except Exception:
                    return False
        
        # 创建大量需要连接的任务
        tasks = [limited_connection_task(i) for i in range(100)]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 分析结果
        successful_tasks = sum(1 for result in results if result is True)
        failed_tasks = len(results) - successful_tasks
        
        print(f"\n=== 连接池耗尽测试结果 ===")
        print(f"总任务数: {len(tasks)}")
        print(f"成功任务: {successful_tasks}")
        print(f"失败任务: {failed_tasks}")
        print(f"成功率: {successful_tasks / len(tasks):.2%}")
        print(f"总耗时: {end_time - start_time:.2f}s")
        
        # 连接池管理断言
        assert successful_tasks >= 80  # 至少80%的任务应该成功
    
    @pytest.mark.asyncio
    async def test_memory_pressure_recovery(self, coordinator_with_realistic_mocks):
        """测试内存压力下的恢复能力"""
        coordinator = coordinator_with_realistic_mocks
        
        # 阶段1: 创建内存压力
        print("阶段1: 创建内存压力...")
        
        large_data_tasks = []
        for i in range(20):
            # 创建大数据处理任务
            task = coordinator.process_user_request(
                f"大数据处理{i}",
                f"memory_pressure_session_{i}",
                f"memory_pressure_user_{i}"
            )
            large_data_tasks.append(task)
        
        # 记录内存压力期间的内存使用
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024
        
        await asyncio.gather(*large_data_tasks, return_exceptions=True)
        
        memory_peak = process.memory_info().rss / 1024 / 1024
        
        # 阶段2: 等待系统恢复
        print("阶段2: 等待系统恢复...")
        await asyncio.sleep(5)  # 等待垃圾回收
        
        import gc
        gc.collect()  # 强制垃圾回收
        
        await asyncio.sleep(2)
        
        memory_after_gc = process.memory_info().rss / 1024 / 1024
        
        # 阶段3: 测试恢复后的性能
        print("阶段3: 测试恢复后性能...")
        
        recovery_tasks = []
        start_time = time.time()
        
        for i in range(10):
            task = coordinator.process_user_request(
                f"恢复测试{i}",
                f"recovery_session_{i}",
                f"recovery_user_{i}"
            )
            recovery_tasks.append(task)
        
        recovery_results = await asyncio.gather(*recovery_tasks, return_exceptions=True)
        end_time = time.time()
        
        successful_recovery = sum(1 for result in recovery_results 
                                if not isinstance(result, Exception) and result.get("success", False))
        
        print(f"\n=== 内存压力恢复测试结果 ===")
        print(f"压力前内存: {memory_before:.2f}MB")
        print(f"压力峰值内存: {memory_peak:.2f}MB")
        print(f"恢复后内存: {memory_after_gc:.2f}MB")
        print(f"内存恢复率: {(memory_peak - memory_after_gc) / (memory_peak - memory_before) * 100:.2f}%")
        print(f"恢复后成功率: {successful_recovery / len(recovery_tasks):.2%}")
        print(f"恢复后平均响应时间: {(end_time - start_time) / len(recovery_tasks):.3f}s")
        
        # 恢复能力断言
        assert memory_after_gc <= memory_peak * 0.8  # 内存应该恢复到峰值的80%以下
        assert successful_recovery >= 8  # 恢复后至少80%的请求应该成功


class TestFailureScenarios:
    """故障场景测试"""
    
    @pytest.mark.asyncio
    async def test_cascading_failure_prevention(self):
        """测试级联故障预防"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 配置级联故障场景
                    mock_router = AsyncMock()
                    failure_count = 0
                    
                    async def cascading_failure(*args, **kwargs):
                        nonlocal failure_count
                        failure_count += 1
                        
                        # 前50%的请求失败，模拟服务降级
                        if failure_count <= 25:
                            raise Exception(f"级联故障 {failure_count}")
                        else:
                            # 后续请求成功，模拟恢复
                            return {
                                "success": True,
                                "data": {"type": "chat", "response": "恢复正常"}
                            }
                    
                    mock_router.process_user_input.side_effect = cascading_failure
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 发送大量请求测试级联故障处理
                    tasks = []
                    for i in range(50):
                        task = coordinator.process_user_request(
                            f"级联故障测试{i}",
                            f"cascade_session_{i}",
                            f"cascade_user_{i}"
                        )
                        tasks.append(task)
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # 分析结果
                    successful_results = sum(1 for result in results 
                                           if not isinstance(result, Exception) and result.get("success", False))
                    failed_results = len(results) - successful_results
                    
                    print(f"\n=== 级联故障预防测试结果 ===")
                    print(f"总请求数: {len(results)}")
                    print(f"成功请求: {successful_results}")
                    print(f"失败请求: {failed_results}")
                    print(f"最终成功率: {successful_results / len(results):.2%}")
                    
                    await coordinator.close()
                    
                    # 级联故障预防断言
                    assert successful_results >= 20  # 至少40%的请求应该成功（恢复阶段）
    
    @pytest.mark.asyncio
    async def test_timeout_handling_under_stress(self):
        """测试压力下的超时处理"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 配置超时场景
                    mock_router = AsyncMock()
                    
                    async def timeout_prone_task(*args, **kwargs):
                        import random
                        # 随机超时
                        if random.random() < 0.3:  # 30%概率超时
                            await asyncio.sleep(10)  # 长时间等待
                        else:
                            await asyncio.sleep(0.1)  # 正常响应
                        
                        return {
                            "success": True,
                            "data": {"type": "data_query", "result": "超时测试结果"}
                        }
                    
                    mock_router.process_user_input.side_effect = timeout_prone_task
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 发送请求并设置超时
                    timeout_results = []
                    success_results = []
                    
                    for i in range(30):
                        try:
                            result = await asyncio.wait_for(
                                coordinator.process_user_request(
                                    f"超时测试{i}",
                                    f"timeout_session_{i}",
                                    f"timeout_user_{i}"
                                ),
                                timeout=2.0  # 2秒超时
                            )
                            success_results.append(result)
                        except asyncio.TimeoutError:
                            timeout_results.append(i)
                    
                    print(f"\n=== 超时处理测试结果 ===")
                    print(f"总请求数: 30")
                    print(f"成功请求: {len(success_results)}")
                    print(f"超时请求: {len(timeout_results)}")
                    print(f"超时率: {len(timeout_results) / 30:.2%}")
                    
                    await coordinator.close()
                    
                    # 超时处理断言
                    assert len(success_results) >= 15  # 至少50%的请求应该在超时前完成


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])