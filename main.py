#!/usr/bin/env python3
"""
TikTok AI Agent 主启动文件
支持UI和API两种模式
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))


def run_ui_mode(host: str = "0.0.0.0", port: int = 8501):
    """启动UI模式 (Streamlit)"""
    import subprocess
    
    print(f"🚀 启动UI模式在 http://{host}:{port}")
    print("📱 正在启动Streamlit界面...")
    
    try:
        subprocess.run([
            "streamlit", "run", "src/ui/streamlit_app.py",
            "--server.address", host,
            "--server.port", str(port),
            "--server.headless", "true"
        ])
    except FileNotFoundError:
        print("❌ Streamlit未安装，请运行: pip install streamlit")
    except Exception as e:
        print(f"❌ UI启动失败: {e}")


def run_api_mode(host: str = "0.0.0.0", port: int = 8000):
    """启动API模式 (FastAPI)"""
    import uvicorn
    
    print(f"🚀 启动API模式在 http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"🔍 健康检查: http://{host}:{port}/api/v1/health")
    
    uvicorn.run(
        "api.main:app",
        host=host,
        port=port,
        reload=True
    )


def main():
    parser = argparse.ArgumentParser(
        description="TikTok AI Agent - 基于Vanna和千问模型的智能数据分析系统"
    )
    parser.add_argument(
        "--mode",
        choices=["ui", "api"],
        default="api",
        help="启动模式: ui(界面模式) 或 api(接口模式)"
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="服务主机地址 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="服务端口 (UI模式默认8501, API模式默认8000)"
    )
    
    args = parser.parse_args()
    
    # 设置默认端口
    if args.port is None:
        args.port = 8501 if args.mode == "ui" else 8000
    
    try:
        if args.mode == "ui":
            run_ui_mode(host=args.host, port=args.port)
        elif args.mode == "api":
            run_api_mode(host=args.host, port=args.port)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 解决建议:")
        print("1. 检查 .env 文件是否存在并配置正确")
        print("2. 确保已安装必要的依赖: pip install fastapi uvicorn pydantic python-dotenv")
        print("3. 检查端口是否被占用")
        sys.exit(1)


if __name__ == "__main__":
    main()