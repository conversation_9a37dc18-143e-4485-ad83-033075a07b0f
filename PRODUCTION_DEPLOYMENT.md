# TikTok AI Agent 生产环境部署指南

## 概述

本文档描述如何部署和使用TikTok AI Agent的生产版本，该版本集成了完整的Agent系统，支持实际数据查询和智能分析。

## 🚀 主要更新

### 从演示版本到生产版本的变化

1. **UI界面升级**
   - 从 `simple_streamlit_app.py` 升级到 `streamlit_app.py`
   - 集成完整的Agent协调器系统
   - 支持实际数据库查询和分析
   - 增强的错误处理和用户体验

2. **Agent系统集成**
   - 完整的多Agent协作流程
   - 路由Agent → Vanna核心 → 展示Agent
   - 异步任务处理和进度跟踪
   - 智能分析和可视化生成

3. **数据处理能力**
   - 真实数据库连接和查询
   - 高级数据分析和洞察生成
   - 多种图表类型支持
   - 数据导出功能

## 📋 部署前准备

### 1. 环境要求

```bash
# Python版本
Python >= 3.11

# 核心依赖
streamlit >= 1.28.0
pandas >= 2.0.0
plotly >= 5.15.0
fastapi >= 0.100.0
uvicorn >= 0.23.0
asyncio
```

### 2. 配置文件检查

确保以下配置文件正确设置：

**`.env` 文件:**
```env
# 千问API配置（必需）
QWEN_API_KEY=your_actual_api_key_here

# 数据库配置
DB_HOST=your_database_host
DB_PORT=3306
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password

# 应用配置
APP_NAME=TikTok AI Agent
DEBUG=false
LOG_LEVEL=INFO
```

**`config.yaml` 文件:**
```yaml
app:
  name: TikTok AI Agent
  version: 1.0.0
  debug: false
  host: 0.0.0.0
  port: 8000

qwen:
  api_key: ${QWEN_API_KEY}
  model: qwen-max
  timeout: 30
  max_retries: 3

database:
  host: ${DB_HOST}
  port: ${DB_PORT}
  name: ${DB_NAME}
  user: ${DB_USER}
  password: ${DB_PASSWORD}

vanna:
  db_type: mysql
  model: qwen
  similarity_threshold: 0.7
```

## 🚀 部署步骤

### 1. 安装依赖

```bash
# 安装所有依赖
pip install -r requirements.txt

# 或者安装最小依赖
pip install -r requirements-minimal.txt
```

### 2. 数据库初始化

```bash
# 初始化数据库表结构
python scripts/init_db.py

# 训练知识库（如果需要）
python scripts/train_knowledge_base.py
```

### 3. 启动应用

#### 方式1: 使用启动脚本（推荐）

```bash
# 启动UI界面模式
python start_ui.py

# 或使用主启动脚本
python main.py --mode ui --host 0.0.0.0 --port 8501
```

#### 方式2: 使用Docker

```bash
# 构建镜像
docker build -t tiktok-ai-agent .

# 启动UI模式
docker run -p 8501:8501 -e MODE=ui tiktok-ai-agent

# 使用docker-compose
docker-compose up tiktok-agent-ui
```

### 4. 验证部署

访问以下地址验证部署：

- **UI界面:** http://localhost:8501
- **健康检查:** 在UI中查看系统状态面板

## 🎯 使用指南

### 1. 系统状态检查

启动后，首先检查系统状态面板：

- ✅ 系统配置已加载
- ✅ 千问API已配置
- ✅ 数据库连接正常
- ✅ Agent系统就绪

### 2. 查询功能

#### 支持的查询类型

**数据统计查询:**
```
今天涨粉最多的前10个达人是谁？
各分类达人的平均粉丝数是多少？
最近一周的视频发布量统计
```

**趋势分析查询:**
```
游戏类达人的粉丝增长趋势如何？
美妆视频的互动率变化情况
不同时间段的用户活跃度分析
```

**筛选查询:**
```
找出互动率最高的100个视频
粉丝数超过10万的美妆达人有哪些？
播放量超过100万的游戏视频
```

#### 查询流程

1. **输入问题** - 在查询框中输入自然语言问题
2. **系统处理** - Agent系统自动分析意图并生成SQL
3. **数据查询** - 执行数据库查询获取结果
4. **智能分析** - 生成数据分析和洞察
5. **结果展示** - 显示表格、图表和分析报告

### 3. 结果解读

查询结果包含四个标签页：

- **📋 数据结果** - 原始查询数据和统计信息
- **📈 可视化** - 自动生成的图表和可视化
- **📝 智能分析** - AI生成的分析摘要和洞察
- **🔍 SQL查询** - 生成的SQL语句和执行信息

### 4. 高级功能

- **查询历史** - 查看和重新执行历史查询
- **数据导出** - 下载CSV格式的查询结果
- **会话管理** - 重置会话和清空历史
- **实时进度** - 查看查询处理进度

## 🔧 故障排除

### 常见问题

#### 1. 系统配置加载失败

**症状:** 红色错误提示"系统配置加载失败"

**解决方案:**
```bash
# 检查配置文件
ls -la .env config.yaml

# 验证环境变量
python -c "from src.core.config import get_config; print(get_config())"

# 重新创建配置文件
cp .env.example .env
# 编辑.env文件，填入正确的配置值
```

#### 2. 千问API调用失败

**症状:** 查询时提示API调用错误

**解决方案:**
```bash
# 检查API密钥
echo $QWEN_API_KEY

# 测试API连接
python -c "
from src.core.qwen_text_generator import QwenTextGenerator
gen = QwenTextGenerator()
print(gen.test_connection())
"

# 更新API密钥
# 编辑.env文件，设置正确的QWEN_API_KEY
```

#### 3. 数据库连接失败

**症状:** 查询时提示数据库连接错误

**解决方案:**
```bash
# 测试数据库连接
python test_db_connection.py

# 检查数据库配置
python -c "
from src.core.config import get_config
config = get_config()
print(f'DB: {config.database.host}:{config.database.port}/{config.database.name}')
"

# 重新初始化数据库
python scripts/init_db.py
```

#### 4. Agent系统初始化失败

**症状:** 查询时提示Agent系统错误

**解决方案:**
```bash
# 检查Agent组件
python -c "
from src.agents.agent_coordinator import get_coordinator
import asyncio
asyncio.run(get_coordinator())
print('Agent系统正常')
"

# 重启应用
# 停止当前进程，重新启动
python main.py --mode ui
```

### 性能优化

#### 1. 数据库优化

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_creators_category ON creators(category);
CREATE INDEX idx_creators_follower_count ON creators(follower_count);
CREATE INDEX idx_videos_created_at ON videos(created_at);
```

#### 2. 缓存配置

```yaml
# config.yaml
redis:
  host: localhost
  port: 6379
  db: 0
  timeout: 5
```

#### 3. 并发设置

```bash
# 使用多进程启动
gunicorn -w 4 -k uvicorn.workers.UvicornWorker src.api.main:app
```

## 📊 监控和日志

### 1. 日志配置

```python
# 在config.yaml中设置日志级别
app:
  log_level: INFO  # DEBUG, INFO, WARNING, ERROR
```

### 2. 系统监控

- **系统状态面板** - 实时查看系统运行状态
- **查询历史** - 监控用户查询模式
- **错误日志** - 查看系统错误和异常

### 3. 性能指标

- **查询响应时间** - 平均查询处理时间
- **数据库连接** - 连接池使用情况
- **API调用** - 千问API调用频次和成功率

## 🔒 安全考虑

### 1. API密钥管理

- 使用环境变量存储敏感信息
- 定期轮换API密钥
- 限制API调用频率

### 2. 数据库安全

- 使用专用数据库用户
- 限制数据库访问权限
- 启用SQL注入防护

### 3. 网络安全

- 使用HTTPS协议
- 配置防火墙规则
- 启用访问日志

## 📈 扩展和定制

### 1. 添加新的查询类型

```python
# 在router_agent.py中添加新的意图识别
# 在display_agent.py中添加新的分析逻辑
```

### 2. 自定义可视化

```python
# 在display_agent.py中添加新的图表类型
# 修改可视化代码生成逻辑
```

### 3. 集成外部服务

```python
# 添加新的数据源
# 集成第三方分析服务
```

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. 检查系统状态面板
2. 查看应用日志
3. 验证配置文件
4. 测试网络连接
5. 重启应用服务

**联系方式:**
- 技术文档: 查看项目README.md
- 问题反馈: 创建GitHub Issue
- 紧急支持: 联系系统管理员

---

**部署完成后，您的TikTok AI Agent生产环境就可以为用户提供强大的数据分析服务了！** 🎉