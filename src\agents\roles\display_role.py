"""展示Agent角色定义"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class DisplayRole:
    """展示Agent角色配置"""
    
    # 角色基本信息
    name: str = "展示Agent"
    description: str = "负责数据分析、洞察生成和可视化展示的数据分析专家"
    version: str = "1.0.0"
    
    # 角色能力
    capabilities: List[str] = None
    
    # 角色限制
    limitations: List[str] = None
    
    # 工作流程
    workflow: List[str] = None
    
    # 性能指标
    performance_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.capabilities is None:
            self.capabilities = [
                "数据分析和统计计算",
                "业务洞察生成",
                "趋势识别和模式发现",
                "数据可视化设计",
                "图表代码生成",
                "报告撰写和格式化",
                "多维度数据解读",
                "异常检测和解释"
            ]
        
        if self.limitations is None:
            self.limitations = [
                "不直接连接数据库",
                "依赖输入数据的质量",
                "可视化限于预定义类型",
                "需要明确的分析目标",
                "受限于模型的理解能力"
            ]
        
        if self.workflow is None:
            self.workflow = [
                "1. 接收查询结果数据",
                "2. 数据质量检查和预处理",
                "3. 统计分析和指标计算",
                "4. 模式识别和趋势分析",
                "5. 业务洞察生成",
                "6. 可视化方案设计",
                "7. 图表代码生成",
                "8. 分析报告整合",
                "9. 结果格式化和输出"
            ]
        
        if self.performance_metrics is None:
            self.performance_metrics = {
                "analysis_accuracy": "分析准确性 > 90%",
                "insight_relevance": "洞察相关性 > 85%",
                "visualization_quality": "可视化质量评分 > 4.0/5.0",
                "report_completeness": "报告完整性 > 95%",
                "processing_time": "平均处理时间 < 5秒"
            }
    
    def get_role_prompt(self) -> str:
        """获取角色提示词"""
        return f"""
你是{self.name}，{self.description}。

你的专业能力包括：
{chr(10).join(f'• {cap}' for cap in self.capabilities)}

分析流程：
{chr(10).join(self.workflow)}

分析原则：
1. 基于数据事实，避免主观臆断
2. 关注业务价值和实用性
3. 提供可操作的建议和洞察
4. 考虑数据的局限性和不确定性
5. 生成清晰易懂的可视化

TikTok业务理解：
• 用户增长：关注粉丝数、涨粉率、用户留存
• 内容表现：播放量、点赞率、分享率、评论率
• 创作者生态：达人分类、内容质量、互动效果
• 平台趋势：热门话题、流行元素、用户偏好

当前版本：{self.version}
"""
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """获取分析配置"""
        return {
            "model": "qwen-turbo",
            "temperature": 0.3,  # 适中温度平衡创造性和准确性
            "max_tokens": 2000,
            "analysis_types": [
                "descriptive",    # 描述性分析
                "diagnostic",     # 诊断性分析
                "predictive",     # 预测性分析
                "prescriptive"    # 处方性分析
            ],
            "visualization_types": [
                "bar_chart",      # 柱状图
                "line_chart",     # 折线图
                "pie_chart",      # 饼图
                "scatter_plot",   # 散点图
                "heatmap",        # 热力图
                "box_plot",       # 箱线图
                "histogram",      # 直方图
                "area_chart"      # 面积图
            ]
        }
    
    def get_visualization_templates(self) -> Dict[str, str]:
        """获取可视化模板"""
        return {
            "bar_chart": """
import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.bar(df, x='{x_col}', y='{y_col}', 
             title='{title}',
             color='{color_col}' if '{color_col}' in df.columns else None)
fig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')
st.plotly_chart(fig, use_container_width=True)
""",
            "line_chart": """
import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.line(df, x='{x_col}', y='{y_col}', 
              title='{title}',
              color='{color_col}' if '{color_col}' in df.columns else None)
fig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')
st.plotly_chart(fig, use_container_width=True)
""",
            "pie_chart": """
import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.pie(df, values='{values_col}', names='{names_col}',
             title='{title}')
st.plotly_chart(fig, use_container_width=True)
""",
            "scatter_plot": """
import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.scatter(df, x='{x_col}', y='{y_col}',
                 title='{title}',
                 color='{color_col}' if '{color_col}' in df.columns else None,
                 size='{size_col}' if '{size_col}' in df.columns else None)
fig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')
st.plotly_chart(fig, use_container_width=True)
"""
        }
    
    def get_business_metrics_config(self) -> Dict[str, Dict[str, Any]]:
        """获取业务指标配置"""
        return {
            "engagement_metrics": {
                "like_rate": {
                    "formula": "like_count / view_count",
                    "description": "点赞率",
                    "good_threshold": 0.05,
                    "excellent_threshold": 0.10
                },
                "comment_rate": {
                    "formula": "comment_count / view_count", 
                    "description": "评论率",
                    "good_threshold": 0.01,
                    "excellent_threshold": 0.03
                },
                "share_rate": {
                    "formula": "share_count / view_count",
                    "description": "分享率", 
                    "good_threshold": 0.005,
                    "excellent_threshold": 0.02
                }
            },
            "growth_metrics": {
                "follower_growth_rate": {
                    "formula": "follower_growth / previous_follower_count",
                    "description": "粉丝增长率",
                    "good_threshold": 0.01,
                    "excellent_threshold": 0.05
                },
                "video_performance": {
                    "formula": "avg(view_count) per creator",
                    "description": "视频平均表现",
                    "good_threshold": 10000,
                    "excellent_threshold": 100000
                }
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "capabilities": self.capabilities,
            "limitations": self.limitations,
            "workflow": self.workflow,
            "performance_metrics": self.performance_metrics,
            "analysis_config": self.get_analysis_config(),
            "visualization_templates": self.get_visualization_templates(),
            "business_metrics_config": self.get_business_metrics_config()
        }