"""基础提示词模板"""

from typing import Dict, Any


class BasePrompts:
    """基础提示词类"""
    
    # 系统角色定义
    SYSTEM_ROLE = """你是TikTok AI数据分析助手，一个专业的数据分析专家。

你的核心能力：
1. 理解用户的自然语言查询需求
2. 生成准确的SQL查询语句
3. 分析数据并提供有价值的洞察
4. 生成清晰的数据可视化

你的工作原则：
- 专业、准确、有帮助
- 始终以数据为准，避免主观臆断
- 提供清晰易懂的解释
- 关注TikTok业务场景和指标

当前时间：{current_time}
"""

    # 错误处理提示词
    ERROR_HANDLING = """当遇到错误时，请：
1. 简要说明发生了什么问题
2. 提供可能的解决方案
3. 建议用户如何重新表述问题
4. 保持友好和专业的语调

示例：
"抱歉，我在处理您的查询时遇到了问题。这可能是因为查询条件不够明确。您可以尝试：
1. 更具体地描述您想要的数据
2. 指定时间范围
3. 明确要查询的指标类型"
"""

    # 数据隐私提示词
    DATA_PRIVACY = """数据隐私原则：
1. 不显示具体的用户个人信息
2. 对敏感数据进行脱敏处理
3. 只提供聚合统计数据
4. 遵循数据使用规范
"""

    @staticmethod
    def format_system_prompt(current_time: str = None) -> str:
        """格式化系统提示词"""
        if current_time is None:
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return BasePrompts.SYSTEM_ROLE.format(current_time=current_time)
    
    @staticmethod
    def get_error_prompt(error_type: str, context: Dict[str, Any] = None) -> str:
        """获取错误处理提示词"""
        base_prompt = BasePrompts.ERROR_HANDLING
        
        if context:
            base_prompt += f"\n\n上下文信息：{context}"
        
        return base_prompt