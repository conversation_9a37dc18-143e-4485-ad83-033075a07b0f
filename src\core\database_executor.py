"""
数据库查询执行器
提供安全的数据库连接和查询执行功能
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import time
from contextlib import asynccontextmanager

try:
    import aiomysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

try:
    import asyncpg
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False

from .config import get_config
try:
    from ..models.query import QueryResult
except ImportError:
    from models.query import QueryResult

logger = logging.getLogger(__name__)


class DatabaseExecutor:
    """数据库查询执行器"""
    
    def __init__(self):
        """初始化数据库执行器"""
        self.config = get_config()
        self.db_config = self.config.database
        
        # 查询配置
        self.query_timeout = 30  # 查询超时时间（秒）
        self.max_rows = 10000   # 最大返回行数
        self.connection_pool = None
        
        logger.info("DatabaseExecutor initialized")
    
    async def initialize_pool(self):
        """初始化连接池"""
        try:
            # 检测数据库类型并创建相应的连接池
            db_type = getattr(self.config.vanna, 'db_type', 'mysql').lower()
            
            if db_type == 'mysql' and MYSQL_AVAILABLE:
                # 使用aiomysql创建MySQL连接池
                self.connection_pool = await aiomysql.create_pool(
                    host=self.db_config.host,
                    port=self.db_config.port,
                    user=self.db_config.user,
                    password=self.db_config.password,
                    db=self.db_config.name,
                    minsize=1,
                    maxsize=self.db_config.pool_size,
                    autocommit=True,
                    charset='utf8mb4'
                )
                self.db_type = 'mysql'
                logger.info("MySQL connection pool initialized")
                
            elif db_type == 'postgres' and POSTGRES_AVAILABLE:
                # 使用asyncpg创建PostgreSQL连接池
                self.connection_pool = await asyncpg.create_pool(
                    host=self.db_config.host,
                    port=self.db_config.port,
                    user=self.db_config.user,
                    password=self.db_config.password,
                    database=self.db_config.name,
                    min_size=1,
                    max_size=self.db_config.pool_size,
                    command_timeout=self.query_timeout
                )
                self.db_type = 'postgres'
                logger.info("PostgreSQL connection pool initialized")
                
            else:
                logger.warning(f"Database type {db_type} not supported or driver not available")
                self.connection_pool = None
                self.db_type = None
            
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            self.connection_pool = None
            self.db_type = None
    
    async def execute_query(self, sql: str, params: Optional[List] = None) -> QueryResult:
        """
        执行SQL查询
        
        Args:
            sql: SQL语句
            params: 查询参数
            
        Returns:
            QueryResult: 查询结果
        """
        start_time = time.time()
        
        try:
            logger.info(f"Executing SQL: {sql[:200]}...")
            
            # 如果没有连接池，尝试初始化
            if not self.connection_pool:
                await self.initialize_pool()
            
            # 如果仍然没有连接池，返回模拟数据
            if not self.connection_pool:
                logger.warning("Database pool not available, returning mock data")
                return await self._execute_mock_query(sql, start_time)
            
            # 执行真实查询
            if self.db_type == 'mysql':
                async with self.connection_pool.acquire() as connection:
                    async with connection.cursor(aiomysql.DictCursor) as cursor:
                        # 设置查询超时
                        async with asyncio.timeout(self.query_timeout):
                            # 执行查询
                            if params:
                                await cursor.execute(sql, params)
                            else:
                                await cursor.execute(sql)
                            
                            rows = await cursor.fetchall()
                            
                            # 限制返回行数
                            if len(rows) > self.max_rows:
                                logger.warning(f"Query returned {len(rows)} rows, limiting to {self.max_rows}")
                                rows = rows[:self.max_rows]
                            
                            # 转换结果
                            data = [dict(row) for row in rows]
                            columns = list(rows[0].keys()) if rows else []
                            
                            execution_time = time.time() - start_time
                            
                            logger.info(f"MySQL query executed successfully, returned {len(data)} rows in {execution_time:.3f}s")
                            
                            return QueryResult(
                                data=data,
                                columns=columns,
                                row_count=len(data),
                                execution_time=execution_time,
                                sql_executed=sql,
                                executed_at=datetime.now(),
                                error=None,
                                is_mock=False
                            )
            
            elif self.db_type == 'postgres':
                async with self.connection_pool.acquire() as connection:
                    # 设置查询超时
                    async with asyncio.timeout(self.query_timeout):
                        # 执行查询
                        if params:
                            rows = await connection.fetch(sql, *params)
                        else:
                            rows = await connection.fetch(sql)
                        
                        # 限制返回行数
                        if len(rows) > self.max_rows:
                            logger.warning(f"Query returned {len(rows)} rows, limiting to {self.max_rows}")
                            rows = rows[:self.max_rows]
                        
                        # 转换结果
                        data = [dict(row) for row in rows]
                        columns = list(rows[0].keys()) if rows else []
                        
                        execution_time = time.time() - start_time
                        
                        logger.info(f"PostgreSQL query executed successfully, returned {len(data)} rows in {execution_time:.3f}s")
                        
                        return QueryResult(
                            data=data,
                            columns=columns,
                            row_count=len(data),
                            execution_time=execution_time,
                            sql_executed=sql,
                            executed_at=datetime.now(),
                            error=None,
                            is_mock=False
                        )
            
            else:
                logger.error(f"Unsupported database type: {self.db_type}")
                return await self._execute_mock_query(sql, start_time)
        
        except asyncio.TimeoutError:
            execution_time = time.time() - start_time
            logger.error(f"Query timeout after {execution_time:.3f}s")
            return QueryResult(
                data=[],
                columns=[],
                row_count=0,
                execution_time=execution_time,
                sql_executed=sql,
                error="Query timeout",
                executed_at=datetime.now(),
                is_mock=False
            )
        
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query execution failed: {e}")
            return QueryResult(
                data=[],
                columns=[],
                row_count=0,
                execution_time=execution_time,
                sql_executed=sql,
                error=str(e),
                executed_at=datetime.now(),
                is_mock=False
            )
    
    async def _execute_mock_query(self, sql: str, start_time: float) -> QueryResult:
        """执行模拟查询（当数据库不可用时）"""
        try:
            # 模拟查询延迟
            await asyncio.sleep(0.1)
            
            sql_lower = sql.lower()
            
            # 根据SQL类型返回不同的模拟数据
            if "follower_growth" in sql_lower or "涨粉" in sql_lower:
                data = [
                    {"username": "gaming_master", "display_name": "游戏大师", "follower_growth": 15000},
                    {"username": "beauty_queen", "display_name": "美妆女王", "follower_growth": 12000},
                    {"username": "food_lover", "display_name": "美食达人", "follower_growth": 8500},
                    {"username": "dance_star", "display_name": "舞蹈之星", "follower_growth": 7200},
                    {"username": "tech_guru", "display_name": "科技大咖", "follower_growth": 6800}
                ]
                columns = ["username", "display_name", "follower_growth"]
            
            elif "total_views" in sql_lower or "播放量" in sql_lower:
                data = [
                    {"username": "viral_creator", "display_name": "爆款创作者", "total_views": 50000000},
                    {"username": "trending_star", "display_name": "热门明星", "total_views": 35000000},
                    {"username": "popular_tiktoker", "display_name": "人气网红", "total_views": 28000000},
                    {"username": "content_king", "display_name": "内容之王", "total_views": 22000000},
                    {"username": "viral_queen", "display_name": "病毒女王", "total_views": 18000000}
                ]
                columns = ["username", "display_name", "total_views"]
            
            elif "engagement_rate" in sql_lower or "互动率" in sql_lower:
                data = [
                    {"id": 1, "title": "超火舞蹈挑战", "username": "dance_star", "engagement_rate": 12.5},
                    {"id": 2, "title": "搞笑日常vlog", "username": "funny_guy", "engagement_rate": 11.8},
                    {"id": 3, "title": "美妆教程分享", "username": "beauty_queen", "engagement_rate": 10.9},
                    {"id": 4, "title": "游戏高光时刻", "username": "gaming_master", "engagement_rate": 10.2},
                    {"id": 5, "title": "美食制作过程", "username": "food_lover", "engagement_rate": 9.7}
                ]
                columns = ["id", "title", "username", "engagement_rate"]
            
            elif "category" in sql_lower or "分类" in sql_lower:
                data = [
                    {"category": "gaming", "creator_count": 1250, "avg_followers": 850000, "avg_videos": 145},
                    {"category": "beauty", "creator_count": 980, "avg_followers": 720000, "avg_videos": 128},
                    {"category": "food", "creator_count": 875, "avg_followers": 650000, "avg_videos": 112},
                    {"category": "dance", "creator_count": 750, "avg_followers": 580000, "avg_videos": 98},
                    {"category": "comedy", "creator_count": 680, "avg_followers": 520000, "avg_videos": 156}
                ]
                columns = ["category", "creator_count", "avg_followers", "avg_videos"]
            
            else:
                # 默认创作者数据
                data = [
                    {"username": "top_creator1", "display_name": "顶级创作者1", "category": "gaming", "follower_count": 2500000, "video_count": 245},
                    {"username": "top_creator2", "display_name": "顶级创作者2", "category": "beauty", "follower_count": 2200000, "video_count": 189},
                    {"username": "top_creator3", "display_name": "顶级创作者3", "category": "food", "follower_count": 1980000, "video_count": 156},
                    {"username": "top_creator4", "display_name": "顶级创作者4", "category": "dance", "follower_count": 1750000, "video_count": 134},
                    {"username": "top_creator5", "display_name": "顶级创作者5", "category": "comedy", "follower_count": 1650000, "video_count": 198}
                ]
                columns = ["username", "display_name", "category", "follower_count", "video_count"]
            
            execution_time = time.time() - start_time
            
            return QueryResult(
                data=data,
                columns=columns,
                row_count=len(data),
                execution_time=execution_time,
                sql_executed=sql,
                executed_at=datetime.now(),
                is_mock=True  # 标记为模拟数据
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Mock query execution failed: {e}")
            return QueryResult(
                data=[],
                columns=[],
                row_count=0,
                execution_time=execution_time,
                sql_executed=sql,
                error=f"Mock query failed: {str(e)}",
                executed_at=datetime.now()
            )
    
    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            if not self.connection_pool:
                await self.initialize_pool()
            
            if not self.connection_pool:
                return False
            
            if self.db_type == 'mysql':
                async with self.connection_pool.acquire() as connection:
                    async with connection.cursor() as cursor:
                        await cursor.execute("SELECT 1")
                        await cursor.fetchone()
            
            elif self.db_type == 'postgres':
                async with self.connection_pool.acquire() as connection:
                    await connection.fetchval("SELECT 1")
            
            else:
                return False
            
            logger.info("Database connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    async def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """
        获取表信息
        
        Args:
            table_name: 表名
            
        Returns:
            Dict: 表信息
        """
        try:
            if not self.connection_pool:
                await self.initialize_pool()
            
            if not self.connection_pool:
                return {"error": "Database not available"}
            
            if self.db_type == 'mysql':
                async with self.connection_pool.acquire() as connection:
                    async with connection.cursor(aiomysql.DictCursor) as cursor:
                        # 获取表结构
                        columns_query = """
                        SELECT column_name, data_type, is_nullable, column_default, column_comment
                        FROM information_schema.columns
                        WHERE table_name = %s AND table_schema = %s
                        ORDER BY ordinal_position
                        """
                        
                        await cursor.execute(columns_query, (table_name, self.db_config.name))
                        columns = await cursor.fetchall()
                        
                        # 获取表注释
                        comment_query = """
                        SELECT table_comment
                        FROM information_schema.tables
                        WHERE table_name = %s AND table_schema = %s
                        """
                        
                        await cursor.execute(comment_query, (table_name, self.db_config.name))
                        comment_result = await cursor.fetchone()
                        comment = comment_result['table_comment'] if comment_result else None
                        
                        return {
                            "table_name": table_name,
                            "columns": columns,
                            "comment": comment,
                            "column_count": len(columns)
                        }
            
            elif self.db_type == 'postgres':
                async with self.connection_pool.acquire() as connection:
                    # PostgreSQL查询
                    columns_query = """
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = $1
                    ORDER BY ordinal_position
                    """
                    
                    columns = await connection.fetch(columns_query, table_name)
                    
                    # 获取表注释
                    comment_query = """
                    SELECT obj_description(oid) as comment
                    FROM pg_class
                    WHERE relname = $1
                    """
                    
                    comment_result = await connection.fetchrow(comment_query, table_name)
                    comment = comment_result['comment'] if comment_result else None
                    
                    return {
                        "table_name": table_name,
                        "columns": [dict(col) for col in columns],
                        "comment": comment,
                        "column_count": len(columns)
                    }
            
            else:
                return {"error": f"Unsupported database type: {self.db_type}"}
                
        except Exception as e:
            logger.error(f"Failed to get table info for {table_name}: {e}")
            return {"error": str(e)}
    
    async def get_database_schema(self) -> List[Dict[str, Any]]:
        """获取数据库所有表的Schema信息"""
        try:
            if not self.connection_pool:
                await self.initialize_pool()
            
            if not self.connection_pool:
                return []
            
            if self.db_type == 'mysql':
                async with self.connection_pool.acquire() as connection:
                    async with connection.cursor(aiomysql.DictCursor) as cursor:
                        # 获取所有用户表
                        tables_query = """
                        SELECT table_name, table_type
                        FROM information_schema.tables
                        WHERE table_schema = %s
                        AND table_type = 'BASE TABLE'
                        ORDER BY table_name
                        """
                        
                        await cursor.execute(tables_query, (self.db_config.name,))
                        tables = await cursor.fetchall()
                        
                        schema_info = []
                        for table in tables:
                            table_info = await self.get_table_info(table['table_name'])
                            if 'error' not in table_info:
                                schema_info.append(table_info)
                        
                        return schema_info
            
            elif self.db_type == 'postgres':
                async with self.connection_pool.acquire() as connection:
                    # 获取所有用户表
                    tables_query = """
                    SELECT table_name, table_type
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                    """
                    
                    tables = await connection.fetch(tables_query)
                    
                    schema_info = []
                    for table in tables:
                        table_info = await self.get_table_info(table['table_name'])
                        if 'error' not in table_info:
                            schema_info.append(table_info)
                    
                    return schema_info
            
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get database schema: {e}")
            return []
    
    async def close(self):
        """关闭连接池"""
        if self.connection_pool:
            if self.db_type == 'mysql':
                self.connection_pool.close()
                await self.connection_pool.wait_closed()
            elif self.db_type == 'postgres':
                await self.connection_pool.close()
            logger.info("Database connection pool closed")


class QueryCache:
    """查询结果缓存"""
    
    def __init__(self, max_size: int = 100, ttl_seconds: int = 300):
        """
        初始化查询缓存
        
        Args:
            max_size: 最大缓存大小
            ttl_seconds: 缓存过期时间（秒）
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        
    def _generate_cache_key(self, sql: str, params: Optional[List] = None) -> str:
        """生成缓存键"""
        import hashlib
        key_data = sql
        if params:
            key_data += str(params)
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get(self, sql: str, params: Optional[List] = None) -> Optional[QueryResult]:
        """获取缓存的查询结果"""
        try:
            cache_key = self._generate_cache_key(sql, params)
            
            if cache_key in self.cache:
                cached_item = self.cache[cache_key]
                
                # 检查是否过期
                if datetime.now() - cached_item['timestamp'] < timedelta(seconds=self.ttl_seconds):
                    logger.info("Cache hit for query")
                    return cached_item['result']
                else:
                    # 删除过期项
                    del self.cache[cache_key]
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to get from cache: {e}")
            return None
    
    def put(self, sql: str, result: QueryResult, params: Optional[List] = None):
        """缓存查询结果"""
        try:
            cache_key = self._generate_cache_key(sql, params)
            
            # 如果缓存已满，删除最旧的项
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.cache.keys(), 
                               key=lambda k: self.cache[k]['timestamp'])
                del self.cache[oldest_key]
            
            self.cache[cache_key] = {
                'result': result,
                'timestamp': datetime.now()
            }
            
            logger.info("Query result cached")
            
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        logger.info("Query cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self.cache),
            "max_size": self.max_size,
            "ttl_seconds": self.ttl_seconds
        }


class CachedDatabaseExecutor(DatabaseExecutor):
    """带缓存的数据库执行器"""
    
    def __init__(self, cache_size: int = 100, cache_ttl: int = 300):
        """
        初始化带缓存的数据库执行器
        
        Args:
            cache_size: 缓存大小
            cache_ttl: 缓存过期时间（秒）
        """
        super().__init__()
        self.cache = QueryCache(cache_size, cache_ttl)
        logger.info(f"CachedDatabaseExecutor initialized with cache size {cache_size}")
    
    async def execute_query(self, sql: str, params: Optional[List] = None) -> QueryResult:
        """执行查询（带缓存）"""
        # 检查缓存
        cached_result = self.cache.get(sql, params)
        if cached_result:
            return cached_result
        
        # 执行查询
        result = await super().execute_query(sql, params)
        
        # 缓存结果（只缓存成功的查询）
        if result.error is None and result.row_count > 0:
            self.cache.put(sql, result, params)
        
        return result
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache.get_stats()