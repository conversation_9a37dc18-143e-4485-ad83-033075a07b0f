# TikTok AI Agent 完整配置示例

# ================================
# 千问API配置（必需）
# ================================
QWEN_API_KEY=sk-your-actual-api-key-here
QWEN_MODEL=qwen-turbo
QWEN_TIMEOUT=30
QWEN_MAX_RETRIES=3

# ================================
# 应用配置
# ================================
APP_NAME=TikTok AI Agent
DEBUG=false
LOG_LEVEL=INFO

# ================================
# 数据库配置（可选）
# ================================
# 如果你有PostgreSQL数据库
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tiktok_data
DB_USER=postgres
DB_PASSWORD=your_db_password

# 或者使用远程数据库
# DB_HOST=your-db-host.com
# DB_PORT=5432
# DB_NAME=tiktok_data
# DB_USER=your_username
# DB_PASSWORD=your_password

# ================================
# Redis配置（可选）
# ================================
# 如果你有Redis服务
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD=your_redis_password  # 如果有密码

# ================================
# 高级配置（通常不需要修改）
# ================================
VANNA_MODEL=qwen
VANNA_DB_TYPE=postgres
VANNA_EMBEDDING_DIM=1536
VANNA_SIMILARITY_THRESHOLD=0.7