"""FastAPI主应用"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config import get_config
from models.api import HealthResponse, QueryRequest, ErrorResponse

# 加载配置
try:
    config = get_config()
except Exception as e:
    print(f"❌ 配置加载失败: {e}")
    print("请检查 .env 文件中的配置")
    sys.exit(1)

# 创建FastAPI应用
app = FastAPI(
    title=config.app.name,
    version=config.app.version,
    description="基于Vanna和千问模型的TikTok数据分析AI Agent系统",
    debug=config.app.debug
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", summary="根路径")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {config.app.name}",
        "version": config.app.version,
        "docs": "/docs",
        "health": "/api/v1/health"
    }


@app.get("/api/v1/health", response_model=HealthResponse, summary="健康检查")
async def health_check():
    """健康检查接口"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version=config.app.version,
        dependencies={
            "qwen_api": "configured" if config.qwen.api_key != "your_qwen_api_key_here" else "not_configured",
            "database": "optional",
            "redis": "optional"
        }
    )


@app.post("/api/v1/query", summary="处理查询")
async def process_query(request: QueryRequest):
    """处理自然语言查询"""
    try:
        # 检查千问API配置
        if config.qwen.api_key == "your_qwen_api_key_here":
            raise HTTPException(
                status_code=500,
                detail="千问API密钥未配置，请在.env文件中设置QWEN_API_KEY"
            )
        
        # 这里是临时响应，后续会实现完整的Agent处理流程
        return {
            "message": "查询接收成功",
            "question": request.question,
            "user_id": request.user_id,
            "session_id": request.session_id,
            "status": "received",
            "note": "完整的Agent处理流程正在开发中..."
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/config", summary="获取配置信息")
async def get_config_info():
    """获取配置信息（不包含敏感信息）"""
    return {
        "app": {
            "name": config.app.name,
            "version": config.app.version,
            "debug": config.app.debug
        },
        "qwen": {
            "model": config.qwen.model,
            "api_configured": config.qwen.api_key != "your_qwen_api_key_here"
        },
        "vanna": {
            "model": config.vanna.model,
            "db_type": config.vanna.db_type
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=config.app.host,
        port=config.app.port,
        reload=config.app.debug
    )