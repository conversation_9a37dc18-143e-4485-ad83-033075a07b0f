"""
知识库管理器使用示例
演示如何使用KnowledgeBaseManager进行Schema、业务文档和SQL范例的管理
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.knowledge_base import KnowledgeBaseManager
from src.core.config import load_config


async def main():
    """主函数演示知识库管理器的使用"""
    
    # 设置环境变量（用于测试）
    os.environ.setdefault("QWEN_API_KEY", "test_key")
    
    # 加载配置
    try:
        load_config()
        print("✅ 配置加载成功")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 创建知识库管理器
    kb_manager = KnowledgeBaseManager(db_path="data/example_knowledge_base.db")
    print("✅ 知识库管理器初始化成功")
    
    try:
        # 1. 添加Schema信息
        print("\n📊 添加Schema信息...")
        
        schemas_to_add = [
            {
                "table_name": "creators",
                "ddl": """CREATE TABLE creators (
                    id BIGINT PRIMARY KEY,
                    username VARCHAR(100) NOT NULL,
                    display_name VARCHAR(200),
                    category VARCHAR(50),
                    follower_count BIGINT DEFAULT 0,
                    following_count BIGINT DEFAULT 0,
                    video_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )""",
                "description": "TikTok达人基础信息表，包含用户名、分类、粉丝数等信息"
            },
            {
                "table_name": "videos",
                "ddl": """CREATE TABLE videos (
                    id BIGINT PRIMARY KEY,
                    creator_id BIGINT NOT NULL,
                    title VARCHAR(500),
                    description TEXT,
                    view_count BIGINT DEFAULT 0,
                    like_count BIGINT DEFAULT 0,
                    comment_count BIGINT DEFAULT 0,
                    share_count BIGINT DEFAULT 0,
                    duration_seconds INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES creators(id)
                )""",
                "description": "TikTok视频信息表，包含播放量、点赞数、评论数等指标"
            },
            {
                "table_name": "daily_metrics",
                "ddl": """CREATE TABLE daily_metrics (
                    id BIGINT PRIMARY KEY,
                    creator_id BIGINT NOT NULL,
                    date DATE NOT NULL,
                    follower_count BIGINT,
                    follower_growth INT,
                    total_views BIGINT,
                    total_likes BIGINT,
                    video_count INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES creators(id),
                    UNIQUE(creator_id, date)
                )""",
                "description": "达人每日指标表，用于计算涨粉量、播放量增长等数据"
            }
        ]
        
        for schema in schemas_to_add:
            result = await kb_manager.add_schema(**schema)
            if result:
                print(f"  ✅ 添加Schema: {schema['table_name']}")
            else:
                print(f"  ❌ 添加Schema失败: {schema['table_name']}")
        
        # 2. 添加业务文档
        print("\n📚 添加业务文档...")
        
        business_docs = [
            {
                "title": "涨粉量计算方式",
                "content": """涨粉量的计算方式：
                
涨粉量 = 当前粉丝数 - 前一天粉丝数

计算规则：
1. 使用daily_metrics表中的follower_count字段
2. 比较相邻两天的数据
3. 如果是新达人（没有历史数据），涨粉量等于当前粉丝数
4. 涨粉量可能为负数，表示掉粉

示例SQL：
SELECT 
    c.username,
    dm.follower_count - LAG(dm.follower_count) OVER (PARTITION BY dm.creator_id ORDER BY dm.date) as follower_growth
FROM daily_metrics dm
JOIN creators c ON dm.creator_id = c.id
WHERE dm.date = CURRENT_DATE;""",
                "category": "business_rules"
            },
            {
                "title": "播放量统计规则",
                "content": """播放量统计的相关规则：

1. 总播放量：所有视频的view_count之和
2. 平均播放量：总播放量 / 视频数量
3. 播放量增长：当日总播放量 - 前一日总播放量
4. 热门视频：播放量超过100万的视频

注意事项：
- 播放量数据每小时更新一次
- 删除的视频不计入统计
- 私密视频不计入公开统计""",
                "category": "business_rules"
            },
            {
                "title": "达人分类说明",
                "content": """TikTok达人分类体系：

主要分类：
- gaming: 游戏类达人
- beauty: 美妆类达人  
- food: 美食类达人
- dance: 舞蹈类达人
- comedy: 搞笑类达人
- education: 教育类达人
- lifestyle: 生活方式类达人
- music: 音乐类达人
- sports: 体育类达人
- tech: 科技类达人

分类规则：
1. 基于达人发布内容的主要类型
2. 一个达人只能属于一个主分类
3. 分类由算法自动识别，人工审核确认""",
                "category": "data_dictionary"
            }
        ]
        
        for doc in business_docs:
            result = await kb_manager.add_business_doc(**doc)
            if result:
                print(f"  ✅ 添加文档: {doc['title']}")
            else:
                print(f"  ❌ 添加文档失败: {doc['title']}")
        
        # 3. 添加SQL范例
        print("\n💻 添加SQL范例...")
        
        sql_examples = [
            {
                "question": "过去一周哪个游戏达人播放量最高？",
                "sql": """SELECT 
    c.username,
    c.display_name,
    SUM(v.view_count) as total_views
FROM creators c
JOIN videos v ON c.id = v.creator_id
WHERE c.category = 'gaming'
    AND v.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY c.id, c.username, c.display_name
ORDER BY total_views DESC
LIMIT 1;""",
                "explanation": "查询游戏分类达人在过去7天内的总播放量，按播放量降序排列取第一名",
                "difficulty": "medium"
            },
            {
                "question": "美妆类视频的平均点赞率是多少？",
                "sql": """SELECT 
    AVG(CASE WHEN v.view_count > 0 THEN v.like_count * 1.0 / v.view_count ELSE 0 END) as avg_like_rate
FROM videos v
JOIN creators c ON v.creator_id = c.id
WHERE c.category = 'beauty'
    AND v.view_count > 0;""",
                "explanation": "计算美妆类达人视频的平均点赞率（点赞数/播放量），排除播放量为0的视频",
                "difficulty": "medium"
            },
            {
                "question": "今天涨粉最多的前10个达人是谁？",
                "sql": """SELECT 
    c.username,
    c.display_name,
    c.category,
    dm.follower_growth
FROM daily_metrics dm
JOIN creators c ON dm.creator_id = c.id
WHERE dm.date = CURRENT_DATE
    AND dm.follower_growth > 0
ORDER BY dm.follower_growth DESC
LIMIT 10;""",
                "explanation": "查询今日涨粉量最多的前10个达人，使用daily_metrics表中的follower_growth字段",
                "difficulty": "easy"
            },
            {
                "question": "各个分类达人的平均粉丝数和视频数是多少？",
                "sql": """SELECT 
    category,
    COUNT(*) as creator_count,
    AVG(follower_count) as avg_followers,
    AVG(video_count) as avg_videos,
    SUM(follower_count) as total_followers
FROM creators
WHERE category IS NOT NULL
GROUP BY category
ORDER BY avg_followers DESC;""",
                "explanation": "按达人分类统计平均粉丝数、平均视频数等指标，了解不同分类的整体情况",
                "difficulty": "easy"
            }
        ]
        
        for example in sql_examples:
            result = await kb_manager.add_sql_example(**example)
            if result:
                print(f"  ✅ 添加SQL范例: {example['question'][:30]}...")
            else:
                print(f"  ❌ 添加SQL范例失败: {example['question'][:30]}...")
        
        # 4. 获取统计信息
        print("\n📈 知识库统计信息:")
        stats = await kb_manager.get_stats()
        print(f"  总项目数: {stats['total_items']}")
        print(f"  Schema数量: {stats['schemas']['total']}")
        print(f"  业务文档数量: {stats['business_docs']['total']}")
        print(f"  SQL范例数量: {stats['sql_examples']['total']}")
        
        # 5. 搜索功能演示
        print("\n🔍 搜索功能演示:")
        
        # 搜索Schema
        print("  搜索包含'creator'的Schema:")
        schemas = await kb_manager.search_schemas("creator")
        for schema in schemas:
            print(f"    - {schema.table_name}: {schema.description}")
        
        # 搜索业务文档
        print("  搜索包含'涨粉'的业务文档:")
        docs = await kb_manager.search_business_docs("涨粉")
        for doc in docs:
            print(f"    - {doc.title}")
        
        # 按分类搜索文档
        print("  搜索business_rules分类的文档:")
        docs = await kb_manager.search_business_docs("", category="business_rules")
        for doc in docs:
            print(f"    - {doc.title}")
        
        print("\n✅ 知识库管理器演示完成！")
        print(f"数据库文件位置: {kb_manager.db_path}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭知识库管理器
        kb_manager.close()


if __name__ == "__main__":
    asyncio.run(main())