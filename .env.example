# TikTok AI Agent 环境配置示例
# 复制此文件为 .env 并填入实际配置值

# ================================
# 应用基础配置
# ================================
APP_NAME=TikTok AI Agent
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=info
APP_HOST=0.0.0.0
APP_PORT=8000

# ================================
# 千问API配置（必需）
# ================================
# 从阿里云百炼平台获取: https://bailian.console.aliyun.com/
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo
QWEN_TIMEOUT=30
QWEN_MAX_RETRIES=3

# ================================
# 数据库配置
# ================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tiktok_data
DB_USER=postgres
DB_PASSWORD=postgres123
DB_CONNECTION=
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# ================================
# Redis缓存配置
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_TIMEOUT=5
REDIS_MAX_CONNECTIONS=10

# ================================
# Vanna框架配置
# ================================
VANNA_MODEL=qwen
VANNA_DB_TYPE=postgres
VANNA_EMBEDDING_DIM=1536
VANNA_MAX_CONTEXT=4000
VANNA_SIMILARITY_THRESHOLD=0.7

# ================================
# 部署配置
# ================================
# 是否在启动时初始化知识库
INIT_KNOWLEDGE_BASE=true

# API工作进程数（生产环境建议设置为CPU核心数）
API_WORKERS=1

# 训练数据路径（可选）
TRAINING_DATA_PATH=

# 知识库数据库路径
KNOWLEDGE_BASE_PATH=/app/data/knowledge_base.db