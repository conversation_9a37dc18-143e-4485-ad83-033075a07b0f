"""
千问模型文本生成服务
实现千问模型的文本生成API调用，支持SQL生成和数据分析
"""

import asyncio
import json
import logging
from typing import List, Optional, Dict, Any, Union
import httpx
from datetime import datetime

from .config import get_config

logger = logging.getLogger(__name__)


class QwenTextGenerator:
    """千问模型文本生成服务"""
    
    def __init__(self):
        """初始化千问文本生成服务"""
        self.config = get_config()
        self.qwen_config = self.config.qwen
        
        # 设置HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.qwen_config.timeout),
            headers={
                "Authorization": f"Bearer {self.qwen_config.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        # 文本生成配置
        self.generation_model = "qwen-turbo"  # 千问文本生成模型
        self.max_tokens = 2048
        self.temperature = 0.1  # 较低的温度以获得更确定的结果
        
        logger.info("QwenTextGenerator initialized")
    
    async def generate_sql(self, prompt: str, max_tokens: Optional[int] = None) -> Optional[str]:
        """
        生成SQL语句
        
        Args:
            prompt: 提示词
            max_tokens: 最大token数
            
        Returns:
            str: 生成的SQL语句
        """
        try:
            response = await self._generate_text(
                prompt=prompt,
                max_tokens=max_tokens or 512,
                temperature=0.1,  # SQL生成需要更确定的结果
                system_message="你是一个专业的SQL生成助手，只返回SQL语句，不包含其他解释。"
            )
            
            if response:
                # 提取SQL语句
                sql = self._extract_sql_from_response(response)
                logger.info(f"Generated SQL: {sql[:100]}...")
                return sql
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to generate SQL: {e}")
            return None
    
    async def generate_analysis(self, prompt: str, max_tokens: Optional[int] = None) -> Optional[str]:
        """
        生成数据分析文本
        
        Args:
            prompt: 提示词
            max_tokens: 最大token数
            
        Returns:
            str: 生成的分析文本
        """
        try:
            response = await self._generate_text(
                prompt=prompt,
                max_tokens=max_tokens or 1024,
                temperature=0.3,  # 分析文本可以稍微有创造性
                system_message="你是一个专业的数据分析师，请提供深入的数据洞察和分析。"
            )
            
            if response:
                logger.info(f"Generated analysis: {response[:100]}...")
                return response
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to generate analysis: {e}")
            return None
    
    async def generate_visualization_code(self, prompt: str) -> Optional[str]:
        """
        生成可视化代码
        
        Args:
            prompt: 提示词
            
        Returns:
            str: 生成的可视化代码
        """
        try:
            response = await self._generate_text(
                prompt=prompt,
                max_tokens=1024,
                temperature=0.2,
                system_message="你是一个数据可视化专家，请生成Python代码使用plotly创建图表。只返回代码，不包含其他解释。"
            )
            
            if response:
                # 提取代码
                code = self._extract_code_from_response(response)
                logger.info(f"Generated visualization code: {len(code)} characters")
                return code
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to generate visualization code: {e}")
            return None
    
    async def classify_intent(self, user_input: str) -> Dict[str, Any]:
        """
        分类用户意图
        
        Args:
            user_input: 用户输入
            
        Returns:
            Dict: 意图分类结果
        """
        try:
            prompt = f"""
请分析以下用户输入的意图，并返回JSON格式的结果：

用户输入: {user_input}

请判断用户意图类型：
1. data_query - 数据查询（需要查询数据库）
2. chat - 闲聊对话
3. help - 寻求帮助
4. other - 其他

返回格式：
{{
    "intent": "意图类型",
    "confidence": 0.0-1.0,
    "keywords": ["关键词1", "关键词2"],
    "explanation": "判断理由"
}}
"""
            
            response = await self._generate_text(
                prompt=prompt,
                max_tokens=256,
                temperature=0.1,
                system_message="你是一个意图分类专家，请准确分析用户意图并返回JSON格式结果。"
            )
            
            if response:
                try:
                    # 尝试解析JSON
                    result = json.loads(response)
                    logger.info(f"Classified intent: {result.get('intent', 'unknown')}")
                    return result
                except json.JSONDecodeError:
                    logger.warning("Failed to parse intent classification JSON")
                    return self._fallback_intent_classification(user_input)
            
            return self._fallback_intent_classification(user_input)
            
        except Exception as e:
            logger.error(f"Failed to classify intent: {e}")
            return self._fallback_intent_classification(user_input)
    
    async def _generate_text(self, prompt: str, max_tokens: int = 1024, 
                           temperature: float = 0.3, system_message: Optional[str] = None) -> Optional[str]:
        """
        调用千问API生成文本
        
        Args:
            prompt: 提示词
            max_tokens: 最大token数
            temperature: 温度参数
            system_message: 系统消息
            
        Returns:
            str: 生成的文本
        """
        try:
            # 构建完整的提示词
            full_prompt = prompt
            if system_message:
                full_prompt = f"{system_message}\n\n{prompt}"
            
            # 构建DashScope API请求数据
            request_data = {
                "model": self.generation_model,
                "input": {
                    "prompt": full_prompt
                },
                "parameters": {
                    "max_tokens": max_tokens,
                    "temperature": temperature,
                    "top_p": 0.8
                }
            }
            
            # 发送请求
            response = await self._make_request(request_data)
            
            if response and "output" in response:
                output = response["output"]
                if "text" in output:
                    content = output["text"]
                    return content.strip()
                elif "choices" in output and output["choices"]:
                    # 备选格式
                    content = output["choices"][0].get("message", {}).get("content", "")
                    return content.strip()
            
            logger.error("Invalid response format from Qwen API")
            logger.error(f"Response structure: {response.keys() if response else 'None'}")
            return None
            
        except Exception as e:
            logger.error(f"Failed to generate text: {e}")
            return None
    
    async def _make_request(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求到千问API
        
        Args:
            data: 请求数据
            
        Returns:
            Dict[str, Any]: 响应数据
        """
        # 千问API的正确URL - 阿里云DashScope API
        url = f"{self.qwen_config.api_base}/services/aigc/text-generation/generation"
        
        for attempt in range(self.qwen_config.max_retries):
            try:
                logger.debug(f"Making request to {url}, attempt {attempt + 1}")
                response = await self.client.post(url, json=data)
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        logger.debug(f"API response received: {result.keys() if isinstance(result, dict) else 'invalid'}")
                        return result
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON response: {e}")
                        logger.error(f"Response text: {response.text}")
                        return None
                
                elif response.status_code == 429:
                    # 速率限制，等待后重试
                    wait_time = 2 ** attempt
                    logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                
                elif response.status_code == 404:
                    logger.error(f"API endpoint not found (404): {url}")
                    logger.error(f"Response: {response.text}")
                    return None
                
                else:
                    logger.error(f"HTTP error {response.status_code}: {response.text}")
                    if attempt < self.qwen_config.max_retries - 1:
                        await asyncio.sleep(1)
                        continue
                    return None
                    
            except httpx.TimeoutException:
                logger.warning(f"Request timeout, attempt {attempt + 1}/{self.qwen_config.max_retries}")
                if attempt < self.qwen_config.max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                else:
                    logger.error("Request timeout after all retries")
                    return None
                    
            except Exception as e:
                logger.error(f"Request failed: {e}")
                if attempt < self.qwen_config.max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                else:
                    return None
        
        logger.error("All API request attempts failed")
        return None
    
    def _extract_sql_from_response(self, response: str) -> str:
        """从响应中提取SQL语句"""
        try:
            # 移除可能的markdown代码块标记
            response = response.strip()
            if response.startswith("```sql"):
                response = response[6:]
            elif response.startswith("```"):
                response = response[3:]
            
            if response.endswith("```"):
                response = response[:-3]
            
            # 清理和格式化SQL
            sql = response.strip()
            
            # 确保SQL以分号结尾
            if not sql.endswith(';'):
                sql += ';'
            
            return sql
            
        except Exception as e:
            logger.warning(f"Failed to extract SQL from response: {e}")
            return response.strip()
    
    def _extract_code_from_response(self, response: str) -> str:
        """从响应中提取代码"""
        try:
            # 移除可能的markdown代码块标记
            response = response.strip()
            if response.startswith("```python"):
                response = response[9:]
            elif response.startswith("```"):
                response = response[3:]
            
            if response.endswith("```"):
                response = response[:-3]
            
            return response.strip()
            
        except Exception as e:
            logger.warning(f"Failed to extract code from response: {e}")
            return response.strip()
    
    def _fallback_intent_classification(self, user_input: str) -> Dict[str, Any]:
        """回退的意图分类逻辑"""
        user_input_lower = user_input.lower()
        
        # 数据查询关键词
        data_keywords = [
            "查询", "统计", "分析", "数据", "多少", "哪个", "排行", "top", "最高", "最低",
            "涨粉", "播放量", "点赞", "评论", "分享", "互动率", "粉丝", "视频", "达人"
        ]
        
        # 闲聊关键词
        chat_keywords = [
            "你好", "hello", "hi", "谢谢", "再见", "怎么样", "如何", "什么是", "介绍"
        ]
        
        # 帮助关键词
        help_keywords = [
            "帮助", "help", "使用", "功能", "怎么用", "如何使用", "说明"
        ]
        
        # 检查关键词匹配
        data_score = sum(1 for keyword in data_keywords if keyword in user_input_lower)
        chat_score = sum(1 for keyword in chat_keywords if keyword in user_input_lower)
        help_score = sum(1 for keyword in help_keywords if keyword in user_input_lower)
        
        if data_score > 0:
            return {
                "intent": "data_query",
                "confidence": min(0.8, 0.5 + data_score * 0.1),
                "keywords": [kw for kw in data_keywords if kw in user_input_lower],
                "explanation": "检测到数据查询相关关键词"
            }
        elif help_score > 0:
            return {
                "intent": "help",
                "confidence": 0.7,
                "keywords": [kw for kw in help_keywords if kw in user_input_lower],
                "explanation": "检测到帮助相关关键词"
            }
        elif chat_score > 0:
            return {
                "intent": "chat",
                "confidence": 0.6,
                "keywords": [kw for kw in chat_keywords if kw in user_input_lower],
                "explanation": "检测到闲聊相关关键词"
            }
        else:
            return {
                "intent": "other",
                "confidence": 0.3,
                "keywords": [],
                "explanation": "未能识别明确意图"
            }
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
        logger.info("QwenTextGenerator closed")


class SQLValidator:
    """SQL验证器"""
    
    def __init__(self):
        """初始化SQL验证器"""
        self.dangerous_keywords = [
            'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE',
            'EXEC', 'EXECUTE', 'GRANT', 'REVOKE', 'SHUTDOWN', 'KILL'
        ]
    
    def validate_sql(self, sql: str) -> Dict[str, Any]:
        """
        验证SQL语句的安全性和语法
        
        Args:
            sql: SQL语句
            
        Returns:
            Dict: 验证结果
        """
        try:
            sql_upper = sql.upper().strip()
            
            # 检查是否为SELECT语句
            if not sql_upper.startswith('SELECT'):
                return {
                    "valid": False,
                    "error": "Only SELECT statements are allowed",
                    "error_type": "security"
                }
            
            # 检查危险关键词（更智能的检查，避免误判字段名）
            import re
            for keyword in self.dangerous_keywords:
                # 使用单词边界检查，避免误判字段名如create_time
                pattern = r'\b' + keyword + r'\b'
                if re.search(pattern, sql_upper):
                    # 额外检查：如果是CREATE但在字段名中（如create_time），则允许
                    if keyword == 'CREATE' and ('CREATE_TIME' in sql_upper or 'CREATED_AT' in sql_upper):
                        continue
                    return {
                        "valid": False,
                        "error": f"SQL contains dangerous keyword: {keyword}",
                        "error_type": "security"
                    }
            
            # 基本语法检查
            syntax_check = self._basic_syntax_check(sql)
            if not syntax_check["valid"]:
                return syntax_check
            
            return {
                "valid": True,
                "error": None,
                "error_type": None
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "error_type": "validation"
            }
    
    def _basic_syntax_check(self, sql: str) -> Dict[str, Any]:
        """基本语法检查"""
        try:
            sql_upper = sql.upper().strip()
            
            # 检查括号匹配
            if sql.count('(') != sql.count(')'):
                return {
                    "valid": False,
                    "error": "Unmatched parentheses",
                    "error_type": "syntax"
                }
            
            # 检查引号匹配
            single_quotes = sql.count("'")
            if single_quotes % 2 != 0:
                return {
                    "valid": False,
                    "error": "Unmatched single quotes",
                    "error_type": "syntax"
                }
            
            double_quotes = sql.count('"')
            if double_quotes % 2 != 0:
                return {
                    "valid": False,
                    "error": "Unmatched double quotes",
                    "error_type": "syntax"
                }
            
            # 检查基本SQL结构
            if 'SELECT' in sql_upper and 'FROM' not in sql_upper:
                # 允许没有FROM的SELECT（如SELECT 1）
                pass
            
            return {
                "valid": True,
                "error": None,
                "error_type": None
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Syntax check error: {str(e)}",
                "error_type": "syntax"
            }