#!/bin/bash
set -e

# 初始化函数
init_system() {
    echo "Initializing TikTok AI Agent system..."
    
    # 创建必要的目录
    mkdir -p /app/data /app/logs
    
    # 检查配置文件
    if [ ! -f "/app/.env" ] && [ ! -f "/app/config.yaml" ]; then
        echo "Warning: No configuration file found. Using environment variables."
    fi
    
    # 初始化知识库（如果需要）
    if [ "$INIT_KNOWLEDGE_BASE" = "true" ]; then
        echo "Initializing knowledge base..."
        python -m src.core.knowledge_trainer --create-sample /app/data/sample_training_data.json
        python -m src.core.knowledge_trainer --input /app/data/sample_training_data.json --db-path /app/data/knowledge_base.db
    fi
}

# 启动API服务
start_api() {
    echo "Starting FastAPI server..."
    exec python -m uvicorn src.api.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers ${API_WORKERS:-1} \
        --log-level ${LOG_LEVEL:-info}
}

# 启动Streamlit UI
start_ui() {
    echo "Starting Streamlit UI..."
    exec streamlit run src/ui/streamlit_app.py \
        --server.port 8501 \
        --server.address 0.0.0.0 \
        --server.headless true \
        --server.enableCORS false \
        --server.enableXsrfProtection false
}

# 启动双模式（API + UI）
start_dual() {
    echo "Starting dual mode (API + UI)..."
    
    # 启动API服务（后台）
    python -m uvicorn src.api.main:app \
        --host 0.0.0.0 \
        --port 8000 \
        --workers ${API_WORKERS:-1} \
        --log-level ${LOG_LEVEL:-info} &
    
    # 等待API服务启动
    sleep 5
    
    # 启动Streamlit UI（前台）
    exec streamlit run src/ui/streamlit_app.py \
        --server.port 8501 \
        --server.address 0.0.0.0 \
        --server.headless true \
        --server.enableCORS false \
        --server.enableXsrfProtection false
}

# 训练知识库
train_knowledge_base() {
    echo "Training knowledge base..."
    
    if [ -z "$TRAINING_DATA_PATH" ]; then
        echo "Creating sample training data..."
        python -m src.core.knowledge_trainer --create-sample /app/data/sample_training_data.json
        TRAINING_DATA_PATH="/app/data/sample_training_data.json"
    fi
    
    echo "Loading training data from: $TRAINING_DATA_PATH"
    exec python -m src.core.knowledge_trainer \
        --input "$TRAINING_DATA_PATH" \
        --db-path "${KNOWLEDGE_BASE_PATH:-/app/data/knowledge_base.db}" \
        --generate-embeddings \
        --verbose
}

# 运行健康检查
health_check() {
    echo "Running health check..."
    curl -f http://localhost:8000/api/v1/health || exit 1
    echo "Health check passed!"
}

# 主逻辑
main() {
    # 初始化系统
    init_system
    
    # 根据命令参数选择启动模式
    case "${1:-api}" in
        "api")
            start_api
            ;;
        "ui")
            start_ui
            ;;
        "dual")
            start_dual
            ;;
        "train")
            train_knowledge_base
            ;;
        "health")
            health_check
            ;;
        *)
            echo "Usage: $0 {api|ui|dual|train|health}"
            echo "  api   - Start FastAPI server only"
            echo "  ui    - Start Streamlit UI only"
            echo "  dual  - Start both API and UI"
            echo "  train - Train knowledge base"
            echo "  health - Run health check"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"