"""
千问模型嵌入服务测试
"""

import asyncio
import pytest
import os
from unittest.mock import AsyncMock, patch, MagicMock

from src.core.qwen_embedding import QwenEmbeddingService, CachedQwenEmbeddingService, EmbeddingCache
from src.core.config import load_config


class TestEmbeddingCache:
    """嵌入向量缓存测试"""
    
    def test_cache_basic_operations(self):
        """测试缓存基本操作"""
        cache = EmbeddingCache(max_size=3)
        
        # 测试空缓存
        assert cache.get("test") is None
        assert cache.size() == 0
        
        # 测试存储和获取
        embedding1 = [0.1, 0.2, 0.3]
        cache.put("text1", embedding1)
        assert cache.get("text1") == embedding1
        assert cache.size() == 1
        
        # 测试多个项目
        embedding2 = [0.4, 0.5, 0.6]
        embedding3 = [0.7, 0.8, 0.9]
        cache.put("text2", embedding2)
        cache.put("text3", embedding3)
        assert cache.size() == 3
        
        # 测试缓存满时的LRU淘汰
        embedding4 = [1.0, 1.1, 1.2]
        cache.put("text4", embedding4)
        assert cache.size() == 3
        assert cache.get("text1") is None  # 应该被淘汰
        assert cache.get("text4") == embedding4
    
    def test_cache_clear(self):
        """测试缓存清空"""
        cache = EmbeddingCache()
        cache.put("text1", [0.1, 0.2])
        cache.put("text2", [0.3, 0.4])
        
        assert cache.size() == 2
        cache.clear()
        assert cache.size() == 0
        assert cache.get("text1") is None


class TestQwenEmbeddingService:
    """千问嵌入服务测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        # 设置环境变量
        os.environ.setdefault("QWEN_API_KEY", "test_key")
        try:
            load_config()
        except:
            pass
    
    @pytest.fixture
    def embedding_service(self, mock_config):
        """创建嵌入服务实例"""
        with patch('src.core.qwen_embedding.httpx.AsyncClient'):
            service = QwenEmbeddingService()
            yield service
    
    def test_calculate_similarity(self, embedding_service):
        """测试相似度计算"""
        # 测试相同向量
        vec1 = [1.0, 0.0, 0.0]
        vec2 = [1.0, 0.0, 0.0]
        similarity = embedding_service.calculate_similarity(vec1, vec2)
        assert abs(similarity - 1.0) < 1e-6
        
        # 测试正交向量
        vec3 = [0.0, 1.0, 0.0]
        similarity = embedding_service.calculate_similarity(vec1, vec3)
        assert abs(similarity - 0.0) < 1e-6
        
        # 测试相反向量
        vec4 = [-1.0, 0.0, 0.0]
        similarity = embedding_service.calculate_similarity(vec1, vec4)
        assert abs(similarity - (-1.0)) < 1e-6
    
    def test_find_most_similar(self, embedding_service):
        """测试查找最相似向量"""
        query = [1.0, 0.0, 0.0]
        candidates = [
            [1.0, 0.0, 0.0],    # 完全相同
            [0.8, 0.6, 0.0],    # 部分相似
            [0.0, 1.0, 0.0],    # 正交
            [-1.0, 0.0, 0.0],   # 相反
        ]
        
        results = embedding_service.find_most_similar(query, candidates, top_k=3)
        
        # 检查结果数量
        assert len(results) == 3
        
        # 检查排序（按相似度降序）
        assert results[0][0] == 0  # 第一个候选（完全相同）
        assert results[0][1] == 1.0
        
        assert results[1][0] == 1  # 第二个候选（部分相似）
        assert results[1][1] > 0
        
        assert results[2][0] == 2  # 第三个候选（正交）
        assert abs(results[2][1]) < 1e-6
    
    @pytest.mark.asyncio
    async def test_get_embedding_empty_text(self, embedding_service):
        """测试空文本处理"""
        result = await embedding_service.get_embedding("")
        assert result is None
        
        result = await embedding_service.get_embedding("   ")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_embeddings_batch_empty(self, embedding_service):
        """测试空批次处理"""
        result = await embedding_service.get_embeddings_batch([])
        assert result == []
        
        result = await embedding_service.get_embeddings_batch(["", "   ", None])
        assert len(result) == 3
        assert all(r is None for r in result)
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, embedding_service):
        """测试成功的API请求"""
        # 模拟成功响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status_code": 200,
            "output": {
                "embeddings": [
                    {"embedding": [0.1, 0.2, 0.3]}
                ]
            }
        }
        
        embedding_service.client.post = AsyncMock(return_value=mock_response)
        
        request_data = {
            "model": "text-embedding-v1",
            "input": {"texts": ["test text"]},
            "parameters": {"text_type": "document"}
        }
        
        result = await embedding_service._make_request(request_data)
        assert result is not None
        assert result["status_code"] == 200
        assert "output" in result
    
    @pytest.mark.asyncio
    async def test_make_request_api_error(self, embedding_service):
        """测试API错误响应"""
        # 模拟API错误响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "status_code": 400,
            "message": "Invalid request"
        }
        
        embedding_service.client.post = AsyncMock(return_value=mock_response)
        
        request_data = {"test": "data"}
        result = await embedding_service._make_request(request_data)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_make_request_http_error(self, embedding_service):
        """测试HTTP错误"""
        # 模拟HTTP错误
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        embedding_service.client.post = AsyncMock(return_value=mock_response)
        
        request_data = {"test": "data"}
        result = await embedding_service._make_request(request_data)
        assert result is None


class TestCachedQwenEmbeddingService:
    """带缓存的千问嵌入服务测试"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        os.environ.setdefault("QWEN_API_KEY", "test_key")
        try:
            load_config()
        except:
            pass
    
    @pytest.fixture
    def cached_service(self, mock_config):
        """创建带缓存的嵌入服务实例"""
        with patch('src.core.qwen_embedding.httpx.AsyncClient'):
            service = CachedQwenEmbeddingService(cache_size=3)
            yield service
    
    @pytest.mark.asyncio
    async def test_cache_hit(self, cached_service):
        """测试缓存命中"""
        # 预先在缓存中放入数据
        test_embedding = [0.1, 0.2, 0.3]
        cached_service.cache.put("test text", test_embedding)
        
        # 模拟父类方法不被调用
        with patch.object(QwenEmbeddingService, 'get_embedding') as mock_parent:
            result = await cached_service.get_embedding("test text")
            
            # 验证缓存命中
            assert result == test_embedding
            mock_parent.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, cached_service):
        """测试缓存未命中"""
        test_embedding = [0.4, 0.5, 0.6]
        
        # 模拟父类方法返回嵌入向量
        with patch.object(QwenEmbeddingService, 'get_embedding', return_value=test_embedding) as mock_parent:
            result = await cached_service.get_embedding("new text")
            
            # 验证调用了父类方法
            mock_parent.assert_called_once_with("new text")
            assert result == test_embedding
            
            # 验证结果被缓存
            cached_result = cached_service.cache.get("new text")
            assert cached_result == test_embedding
    
    def test_get_cache_stats(self, cached_service):
        """测试缓存统计信息"""
        stats = cached_service.get_cache_stats()
        assert "cache_size" in stats
        assert "max_size" in stats
        assert stats["max_size"] == 3
        assert stats["cache_size"] == 0
        
        # 添加一些缓存项
        cached_service.cache.put("text1", [0.1, 0.2])
        cached_service.cache.put("text2", [0.3, 0.4])
        
        stats = cached_service.get_cache_stats()
        assert stats["cache_size"] == 2


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])