"""
展示Agent单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agents.display_agent import (
    DisplayAgent, AnalysisResult, VisualizationCode, Report, VisualizationType
)
from models.query import QueryResult


class TestAnalysisResult:
    """分析结果测试"""
    
    def test_analysis_result_creation(self):
        """测试分析结果创建"""
        result = AnalysisResult(
            summary="测试摘要",
            insights=["洞察1", "洞察2"],
            recommendations=["建议1", "建议2"],
            key_metrics={"total": 100, "avg": 50}
        )
        
        assert result.summary == "测试摘要"
        assert result.insights == ["洞察1", "洞察2"]
        assert result.recommendations == ["建议1", "建议2"]
        assert result.key_metrics == {"total": 100, "avg": 50}
        assert isinstance(result.generated_at, datetime)


class TestVisualizationCode:
    """可视化代码测试"""
    
    def test_visualization_code_creation(self):
        """测试可视化代码创建"""
        viz = VisualizationCode(
            chart_type="bar_chart",
            code="import plotly.express as px\nfig = px.bar(df, x='x', y='y')",
            title="测试图表",
            description="这是一个测试图表"
        )
        
        assert viz.chart_type == "bar_chart"
        assert "plotly.express" in viz.code
        assert viz.title == "测试图表"
        assert viz.description == "这是一个测试图表"
        assert isinstance(viz.generated_at, datetime)


class TestReport:
    """报告测试"""
    
    def test_report_creation(self):
        """测试报告创建"""
        analysis = AnalysisResult("摘要", ["洞察"], ["建议"], {"metric": 1})
        visualizations = [VisualizationCode("bar", "code", "title", "desc")]
        query_result = QueryResult(
            data=[{"test": "data"}],
            columns=["test"],
            row_count=1,
            execution_time=0.5
        )
        
        report = Report(analysis, visualizations, query_result)
        
        assert report.analysis == analysis
        assert report.visualizations == visualizations
        assert report.raw_data == query_result
        assert isinstance(report.generated_at, datetime)


class TestDisplayAgent:
    """展示Agent测试"""
    
    @pytest.fixture
    def display_agent(self):
        """创建展示Agent实例"""
        with patch('agents.display_agent.QwenTextGenerator') as mock_generator:
            mock_generator.return_value = AsyncMock()
            agent = DisplayAgent()
            return agent
    
    @pytest.fixture
    def sample_query_result(self):
        """示例查询结果"""
        return QueryResult(
            data=[
                {"creator": "达人A", "fans": 100000, "videos": 50, "engagement_rate": 5.5},
                {"creator": "达人B", "fans": 80000, "videos": 30, "engagement_rate": 6.2},
                {"creator": "达人C", "fans": 120000, "videos": 40, "engagement_rate": 4.8}
            ],
            columns=["creator", "fans", "videos", "engagement_rate"],
            row_count=3,
            execution_time=0.8,
            sql_executed="SELECT creator, fans, videos, engagement_rate FROM creators ORDER BY fans DESC LIMIT 3"
        )
    
    def test_display_agent_initialization(self, display_agent):
        """测试展示Agent初始化"""
        assert display_agent.text_generator is not None
        assert display_agent.max_insights == 5
        assert display_agent.max_recommendations == 3
    
    @pytest.mark.asyncio
    async def test_analyze_data_success(self, display_agent, sample_query_result):
        """测试成功分析数据"""
        # 模拟千问模型返回分析结果
        mock_analysis = """## 数据摘要
查询返回了3个TikTok达人的数据，包括粉丝数、视频数和互动率信息。

## 关键洞察
1. 达人C拥有最多粉丝（120,000），但互动率相对较低（4.8%）
2. 达人B虽然粉丝数较少，但互动率最高（6.2%）
3. 粉丝数与互动率呈现负相关趋势

## 建议
1. 建议达人C提高内容质量以提升互动率
2. 可以学习达人B的内容策略"""
        
        display_agent.text_generator.generate_analysis.return_value = mock_analysis
        
        result = await display_agent.analyze_data(sample_query_result, "粉丝最多的达人")
        
        assert isinstance(result, AnalysisResult)
        assert "3个TikTok达人" in result.summary
        assert len(result.insights) == 3
        assert len(result.recommendations) == 2
        assert "fans_sum" in result.key_metrics
        assert result.key_metrics["total_records"] == 3
    
    @pytest.mark.asyncio
    async def test_analyze_data_empty_result(self, display_agent):
        """测试分析空数据"""
        empty_result = QueryResult(
            data=[],
            columns=[],
            row_count=0,
            execution_time=0.1
        )
        
        result = await display_agent.analyze_data(empty_result)
        
        assert isinstance(result, AnalysisResult)
        assert "查询未返回任何数据" in result.summary
        assert "数据集为空" in result.insights
        assert "请检查查询条件" in result.recommendations
        assert result.key_metrics == {}
    
    @pytest.mark.asyncio
    async def test_analyze_data_api_failure(self, display_agent, sample_query_result):
        """测试API调用失败时的回退分析"""
        # 模拟千问模型调用失败
        display_agent.text_generator.generate_analysis.return_value = None
        
        result = await display_agent.analyze_data(sample_query_result)
        
        assert isinstance(result, AnalysisResult)
        assert "成功查询到 3 条TikTok相关数据记录" in result.summary
        assert len(result.insights) > 0
        assert "fans_sum" in result.key_metrics
    
    def test_parse_analysis_result(self, display_agent):
        """测试解析分析结果"""
        analysis_text = """## 数据摘要
这是数据摘要内容

## 关键洞察
1. 第一个洞察
2. 第二个洞察
3. 第三个洞察

## 建议
1. 第一个建议
2. 第二个建议"""
        
        result = display_agent._parse_analysis_result(analysis_text)
        
        assert result["summary"] == "这是数据摘要内容"
        assert len(result["insights"]) == 3
        assert result["insights"][0] == "第一个洞察"
        assert len(result["recommendations"]) == 2
        assert result["recommendations"][0] == "第一个建议"
    
    def test_generate_basic_analysis(self, display_agent, sample_query_result):
        """测试生成基础分析"""
        result = display_agent._generate_basic_analysis(sample_query_result)
        
        assert "成功查询到 3 条TikTok相关数据记录" in result["summary"]
        assert "查询返回了 3 条记录" in result["insights"]
        assert "数据包含 3 个数值字段" in result["insights"]
        assert len(result["recommendations"]) > 0
    
    def test_calculate_key_metrics(self, display_agent, sample_query_result):
        """测试计算关键指标"""
        metrics = display_agent._calculate_key_metrics(sample_query_result)
        
        assert metrics["total_records"] == 3
        assert metrics["execution_time"] == 0.8
        assert metrics["fans_sum"] == 300000  # 100000 + 80000 + 120000
        assert metrics["fans_avg"] == 100000  # 300000 / 3
        assert metrics["fans_max"] == 120000
        assert metrics["fans_min"] == 80000
        assert metrics["videos_sum"] == 120   # 50 + 30 + 40
    
    def test_auto_select_chart_type(self, display_agent, sample_query_result):
        """测试自动选择图表类型"""
        chart_type = display_agent._auto_select_chart_type(sample_query_result)
        
        # 有分类列（creator）和数值列，应该选择柱状图
        assert chart_type == VisualizationType.BAR_CHART
    
    def test_auto_select_chart_type_empty_data(self, display_agent):
        """测试空数据的图表类型选择"""
        empty_result = QueryResult(data=[], columns=[], row_count=0, execution_time=0)
        
        chart_type = display_agent._auto_select_chart_type(empty_result)
        
        assert chart_type == VisualizationType.TABLE
    
    def test_auto_select_chart_type_pie_chart(self, display_agent):
        """测试饼图类型选择"""
        pie_data = QueryResult(
            data=[
                {"category": "游戏", "count": 10},
                {"category": "美妆", "count": 8},
                {"category": "音乐", "count": 6}
            ],
            columns=["category", "count"],
            row_count=3,
            execution_time=0.5
        )
        
        chart_type = display_agent._auto_select_chart_type(pie_data)
        
        # 少量分类数据，应该选择饼图
        assert chart_type == VisualizationType.PIE_CHART
    
    @pytest.mark.asyncio
    async def test_generate_visualization_success(self, display_agent, sample_query_result):
        """测试成功生成可视化"""
        # 模拟千问模型返回可视化代码
        mock_code = """import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.bar(df, x='creator', y='fans', title='达人粉丝数对比')
fig.show()"""
        
        display_agent.text_generator.generate_visualization_code.return_value = mock_code
        
        visualizations = await display_agent.generate_visualization(sample_query_result)
        
        assert len(visualizations) > 0
        viz = visualizations[0]
        assert isinstance(viz, VisualizationCode)
        assert "plotly.express" in viz.code
        assert "pd.DataFrame(data)" in viz.code
        assert viz.chart_type == VisualizationType.BAR_CHART
    
    @pytest.mark.asyncio
    async def test_generate_visualization_empty_data(self, display_agent):
        """测试空数据的可视化生成"""
        empty_result = QueryResult(data=[], columns=[], row_count=0, execution_time=0)
        
        visualizations = await display_agent.generate_visualization(empty_result)
        
        assert visualizations == []
    
    @pytest.mark.asyncio
    async def test_generate_visualization_api_failure(self, display_agent, sample_query_result):
        """测试API失败时的模板生成"""
        # 模拟千问模型调用失败
        display_agent.text_generator.generate_visualization_code.return_value = None
        
        visualizations = await display_agent.generate_visualization(sample_query_result)
        
        assert len(visualizations) > 0
        viz = visualizations[0]
        assert isinstance(viz, VisualizationCode)
        assert "import plotly.express as px" in viz.code
        assert viz.chart_type == VisualizationType.BAR_CHART
    
    def test_generate_bar_chart_template(self, display_agent):
        """测试生成柱状图模板"""
        columns = ["creator", "fans"]
        code = display_agent._generate_bar_chart_template(columns)
        
        assert "import plotly.express as px" in code
        assert "px.bar(df, x='creator', y='fans'" in code
        assert "TikTok数据分析 - 柱状图" in code
    
    def test_generate_line_chart_template(self, display_agent):
        """测试生成折线图模板"""
        columns = ["date", "views"]
        code = display_agent._generate_line_chart_template(columns)
        
        assert "import plotly.express as px" in code
        assert "px.line(df, x='date', y='views'" in code
        assert "TikTok数据趋势 - 折线图" in code
    
    def test_generate_pie_chart_template(self, display_agent):
        """测试生成饼图模板"""
        columns = ["category", "count"]
        code = display_agent._generate_pie_chart_template(columns)
        
        assert "import plotly.express as px" in code
        assert "px.pie(df, names='category', values='count'" in code
        assert "TikTok数据分布 - 饼图" in code
    
    def test_generate_table_template(self, display_agent):
        """测试生成表格模板"""
        columns = ["name", "value"]
        code = display_agent._generate_table_template(columns)
        
        assert "import plotly.graph_objects as go" in code
        assert "go.Table(" in code
        assert "TikTok数据表格" in code
    
    def test_clean_visualization_code(self, display_agent):
        """测试清理可视化代码"""
        dirty_code = """```python
import plotly.express as px
fig = px.bar(data, x='x', y='y')
```"""
        
        clean_code = display_agent._clean_visualization_code(dirty_code)
        
        assert "```python" not in clean_code
        assert "```" not in clean_code
        assert "import plotly.express as px" in clean_code
        assert "pd.DataFrame(data)" in clean_code
    
    def test_generate_chart_title(self, display_agent, sample_query_result):
        """测试生成图表标题"""
        title = display_agent._generate_chart_title(sample_query_result, VisualizationType.BAR_CHART)
        
        assert title == "TikTok数据分析 - 柱状图"
    
    def test_generate_chart_description(self, display_agent, sample_query_result):
        """测试生成图表描述"""
        desc = display_agent._generate_chart_description(sample_query_result, VisualizationType.BAR_CHART)
        
        assert "展示了 3 条记录的对比分析" in desc
    
    def test_suggest_additional_charts(self, display_agent, sample_query_result):
        """测试建议额外图表"""
        suggestions = display_agent._suggest_additional_charts(
            sample_query_result, VisualizationType.BAR_CHART
        )
        
        assert VisualizationType.TABLE in suggestions
        assert VisualizationType.PIE_CHART in suggestions
        assert len(suggestions) <= 2
    
    @pytest.mark.asyncio
    async def test_create_report_success(self, display_agent, sample_query_result):
        """测试成功创建报告"""
        # 模拟千问模型返回
        mock_analysis = """## 数据摘要
测试摘要

## 关键洞察
1. 洞察1
2. 洞察2

## 建议
1. 建议1"""
        
        mock_code = """import plotly.express as px
df = pd.DataFrame(data)
fig = px.bar(df, x='creator', y='fans')"""
        
        display_agent.text_generator.generate_analysis.return_value = mock_analysis
        display_agent.text_generator.generate_visualization_code.return_value = mock_code
        
        report = await display_agent.create_report(sample_query_result, "测试问题")
        
        assert isinstance(report, Report)
        assert isinstance(report.analysis, AnalysisResult)
        assert len(report.visualizations) > 0
        assert report.raw_data == sample_query_result
        assert isinstance(report.generated_at, datetime)
    
    @pytest.mark.asyncio
    async def test_create_report_error_handling(self, display_agent, sample_query_result):
        """测试报告创建错误处理"""
        # 模拟分析过程抛出异常
        display_agent.text_generator.generate_analysis.side_effect = Exception("分析失败")
        
        report = await display_agent.create_report(sample_query_result)
        
        assert isinstance(report, Report)
        assert "报告生成过程中发生错误" in report.analysis.summary
        assert len(report.visualizations) == 0
    
    def test_build_analysis_prompt(self, display_agent, sample_query_result):
        """测试构建分析提示词"""
        prompt = display_agent._build_analysis_prompt(sample_query_result, "测试问题")
        
        assert "测试问题" in prompt
        assert "数据行数: 3" in prompt
        assert "执行时间: 0.800秒" in prompt
        assert "达人A" in prompt  # 数据样本
        assert "## 数据摘要" in prompt
        assert "## 关键洞察" in prompt
        assert "## 建议" in prompt
    
    def test_build_visualization_prompt(self, display_agent, sample_query_result):
        """测试构建可视化提示词"""
        prompt = display_agent._build_visualization_prompt(sample_query_result, "bar_chart")
        
        assert "bar_chart类型的plotly可视化代码" in prompt
        assert "列名: creator, fans, videos, engagement_rate" in prompt
        assert "数据行数: 3" in prompt
        assert "达人A" in prompt  # 数据样本
        assert "使用plotly.express" in prompt
        assert "使用中文标签" in prompt
    
    @pytest.mark.asyncio
    async def test_close(self, display_agent):
        """测试关闭资源"""
        await display_agent.close()
        
        display_agent.text_generator.close.assert_called_once()


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_analysis_workflow(self):
        """测试完整的分析工作流"""
        with patch('agents.display_agent.QwenTextGenerator') as mock_generator_class:
            # 设置模拟对象
            mock_generator = AsyncMock()
            mock_generator_class.return_value = mock_generator
            
            # 配置模拟返回值
            mock_analysis = """## 数据摘要
分析了3个达人的数据

## 关键洞察
1. 达人A表现最佳
2. 互动率普遍较高

## 建议
1. 继续保持优质内容"""
            
            mock_code = """import plotly.express as px
import pandas as pd
df = pd.DataFrame(data)
fig = px.bar(df, x='creator', y='fans', title='达人粉丝对比')
fig.show()"""
            
            mock_generator.generate_analysis.return_value = mock_analysis
            mock_generator.generate_visualization_code.return_value = mock_code
            
            # 创建展示Agent并测试
            agent = DisplayAgent()
            
            query_result = QueryResult(
                data=[
                    {"creator": "达人A", "fans": 100000},
                    {"creator": "达人B", "fans": 80000}
                ],
                columns=["creator", "fans"],
                row_count=2,
                execution_time=0.5
            )
            
            report = await agent.create_report(query_result, "粉丝最多的达人")
            
            # 验证结果
            assert isinstance(report, Report)
            assert "分析了3个达人的数据" in report.analysis.summary
            assert len(report.analysis.insights) == 2
            assert len(report.analysis.recommendations) == 1
            assert len(report.visualizations) > 0
            assert "px.bar" in report.visualizations[0].code
            
            await agent.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])