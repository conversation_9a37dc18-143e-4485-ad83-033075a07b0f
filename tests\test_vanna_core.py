"""
Vanna核心引擎单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.vanna_core import VannaCore
from models.query import UserQuery, QueryResult
from models.base import APIResponse


class TestVannaCore:
    """Vanna核心引擎测试"""
    
    @pytest.fixture
    def vanna_core(self):
        """创建Vanna核心实例"""
        with patch('core.vanna_core.KnowledgeBaseManager') as mock_kb:
            with patch('core.vanna_core.QwenTextGenerator') as mock_generator:
                with patch('core.vanna_core.DatabaseExecutor') as mock_executor:
                    mock_kb.return_value = AsyncMock()
                    mock_generator.return_value = AsyncMock()
                    mock_executor.return_value = AsyncMock()
                    
                    core = VannaCore()
                    return core
    
    @pytest.fixture
    def sample_user_query(self):
        """示例用户查询"""
        return UserQuery(
            question="今天涨粉最多的达人是谁？",
            user_id="test_user",
            session_id="test_session"
        )
    
    def test_vanna_core_initialization(self, vanna_core):
        """测试Vanna核心初始化"""
        assert vanna_core.knowledge_base is not None
        assert vanna_core.text_generator is not None
        assert vanna_core.database_executor is not None
        assert vanna_core.retrieval_limit == 10
        assert vanna_core.sql_confidence_threshold == 0.7
    
    @pytest.mark.asyncio
    async def test_process_query_success(self, vanna_core, sample_user_query):
        """测试成功处理查询"""
        # 模拟检索上下文
        mock_context = {
            "schemas": [{"table": "creators", "ddl": "CREATE TABLE creators..."}],
            "examples": [{"question": "类似问题", "sql": "SELECT * FROM creators"}],
            "documents": [{"content": "涨粉计算方式"}]
        }
        vanna_core.knowledge_base.retrieve_context.return_value = mock_context
        
        # 模拟SQL生成
        mock_sql_result = {
            "sql": "SELECT creator_name, follower_growth FROM creators ORDER BY follower_growth DESC LIMIT 1",
            "confidence": 0.85,
            "explanation": "查询今日涨粉最多的达人"
        }
        vanna_core.text_generator.generate_sql.return_value = mock_sql_result
        
        # 模拟数据库执行
        mock_db_result = {
            "data": [{"creator_name": "测试达人", "follower_growth": 10000}],
            "columns": ["creator_name", "follower_growth"],
            "row_count": 1,
            "execution_time": 0.5
        }
        vanna_core.database_executor.execute_query.return_value = mock_db_result
        
        result = await vanna_core.process_query(sample_user_query)
        
        assert isinstance(result, QueryResult)
        assert result.data == mock_db_result["data"]
        assert result.columns == mock_db_result["columns"]
        assert result.row_count == 1
        assert result.sql_executed == mock_sql_result["sql"]
        assert result.error is None
        
        # 验证调用链
        vanna_core.knowledge_base.retrieve_context.assert_called_once()
        vanna_core.text_generator.generate_sql.assert_called_once()
        vanna_core.database_executor.execute_query.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_query_retrieval_failure(self, vanna_core, sample_user_query):
        """测试检索失败的处理"""
        # 模拟检索失败
        vanna_core.knowledge_base.retrieve_context.side_effect = Exception("检索失败")
        
        result = await vanna_core.process_query(sample_user_query)
        
        assert isinstance(result, QueryResult)
        assert result.error is not None
        assert "检索相关上下文失败" in result.error
        assert result.data == []
    
    @pytest.mark.asyncio
    async def test_process_query_sql_generation_failure(self, vanna_core, sample_user_query):
        """测试SQL生成失败的处理"""
        # 模拟检索成功
        vanna_core.knowledge_base.retrieve_context.return_value = {"schemas": []}
        
        # 模拟SQL生成失败
        vanna_core.text_generator.generate_sql.side_effect = Exception("SQL生成失败")
        
        result = await vanna_core.process_query(sample_user_query)
        
        assert isinstance(result, QueryResult)
        assert result.error is not None
        assert "SQL生成失败" in result.error
        assert result.data == []
    
    @pytest.mark.asyncio
    async def test_process_query_low_confidence_sql(self, vanna_core, sample_user_query):
        """测试低置信度SQL的处理"""
        # 模拟检索成功
        vanna_core.knowledge_base.retrieve_context.return_value = {"schemas": []}
        
        # 模拟低置信度SQL生成
        mock_sql_result = {
            "sql": "SELECT * FROM unknown_table",
            "confidence": 0.3,  # 低于阈值0.7
            "explanation": "不确定的查询"
        }
        vanna_core.text_generator.generate_sql.return_value = mock_sql_result
        
        result = await vanna_core.process_query(sample_user_query)
        
        assert isinstance(result, QueryResult)
        assert result.error is not None
        assert "SQL置信度过低" in result.error
        assert result.data == []
    
    @pytest.mark.asyncio
    async def test_process_query_database_execution_failure(self, vanna_core, sample_user_query):
        """测试数据库执行失败的处理"""
        # 模拟检索和SQL生成成功
        vanna_core.knowledge_base.retrieve_context.return_value = {"schemas": []}
        vanna_core.text_generator.generate_sql.return_value = {
            "sql": "SELECT * FROM creators",
            "confidence": 0.8,
            "explanation": "查询达人信息"
        }
        
        # 模拟数据库执行失败
        vanna_core.database_executor.execute_query.side_effect = Exception("数据库连接失败")
        
        result = await vanna_core.process_query(sample_user_query)
        
        assert isinstance(result, QueryResult)
        assert result.error is not None
        assert "数据库查询执行失败" in result.error
        assert result.data == []
    
    @pytest.mark.asyncio
    async def test_retrieve_context_success(self, vanna_core):
        """测试成功检索上下文"""
        question = "今天涨粉最多的达人是谁？"
        
        # 模拟知识库返回
        mock_context = {
            "schemas": [
                {"table_name": "creators", "ddl": "CREATE TABLE creators...", "relevance": 0.9}
            ],
            "examples": [
                {"question": "涨粉最多的达人", "sql": "SELECT * FROM creators", "relevance": 0.8}
            ],
            "documents": [
                {"title": "涨粉计算", "content": "涨粉量 = 今日粉丝数 - 昨日粉丝数", "relevance": 0.7}
            ]
        }
        vanna_core.knowledge_base.retrieve_context.return_value = mock_context
        
        result = await vanna_core.retrieve_context(question)
        
        assert result == mock_context
        vanna_core.knowledge_base.retrieve_context.assert_called_once_with(
            question, limit=vanna_core.retrieval_limit
        )
    
    @pytest.mark.asyncio
    async def test_generate_sql_success(self, vanna_core):
        """测试成功生成SQL"""
        question = "今天涨粉最多的达人是谁？"
        context = {
            "schemas": [{"ddl": "CREATE TABLE creators..."}],
            "examples": [{"sql": "SELECT * FROM creators"}]
        }
        
        # 模拟文本生成器返回
        mock_sql_result = {
            "sql": "SELECT creator_name FROM creators ORDER BY follower_growth DESC LIMIT 1",
            "confidence": 0.85,
            "explanation": "查询涨粉最多的达人"
        }
        vanna_core.text_generator.generate_sql.return_value = mock_sql_result
        
        result = await vanna_core.generate_sql(question, context)
        
        assert result == mock_sql_result
        vanna_core.text_generator.generate_sql.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_query_success(self, vanna_core):
        """测试成功执行查询"""
        sql = "SELECT creator_name FROM creators LIMIT 1"
        
        # 模拟数据库执行器返回
        mock_result = {
            "data": [{"creator_name": "测试达人"}],
            "columns": ["creator_name"],
            "row_count": 1,
            "execution_time": 0.3
        }
        vanna_core.database_executor.execute_query.return_value = mock_result
        
        result = await vanna_core.execute_query(sql)
        
        assert result == mock_result
        vanna_core.database_executor.execute_query.assert_called_once_with(sql)
    
    @pytest.mark.asyncio
    async def test_train_knowledge_base_success(self, vanna_core):
        """测试成功训练知识库"""
        training_data = {
            "schemas": [{"table": "creators", "ddl": "CREATE TABLE..."}],
            "documents": [{"title": "文档", "content": "内容"}],
            "examples": [{"question": "问题", "sql": "SQL"}]
        }
        
        # 模拟知识库训练成功
        vanna_core.knowledge_base.train.return_value = True
        
        result = await vanna_core.train_knowledge_base(training_data)
        
        assert result is True
        vanna_core.knowledge_base.train.assert_called_once_with(training_data)
    
    @pytest.mark.asyncio
    async def test_train_knowledge_base_failure(self, vanna_core):
        """测试知识库训练失败"""
        training_data = {"schemas": []}
        
        # 模拟知识库训练失败
        vanna_core.knowledge_base.train.side_effect = Exception("训练失败")
        
        result = await vanna_core.train_knowledge_base(training_data)
        
        assert result is False
    
    def test_build_sql_prompt(self, vanna_core):
        """测试构建SQL提示词"""
        question = "今天涨粉最多的达人是谁？"
        context = {
            "schemas": [
                {"table_name": "creators", "ddl": "CREATE TABLE creators (id INT, name VARCHAR(100))"}
            ],
            "examples": [
                {"question": "粉丝最多的达人", "sql": "SELECT name FROM creators ORDER BY fans DESC LIMIT 1"}
            ],
            "documents": [
                {"title": "涨粉计算", "content": "涨粉量 = 今日粉丝数 - 昨日粉丝数"}
            ]
        }
        
        prompt = vanna_core._build_sql_prompt(question, context)
        
        assert question in prompt
        assert "CREATE TABLE creators" in prompt
        assert "SELECT name FROM creators" in prompt
        assert "涨粉量 = 今日粉丝数 - 昨日粉丝数" in prompt
        assert "请生成SQL查询语句" in prompt
    
    def test_validate_sql_basic(self, vanna_core):
        """测试基础SQL验证"""
        # 有效的SQL
        valid_sql = "SELECT name FROM creators WHERE fans > 1000"
        assert vanna_core._validate_sql(valid_sql) is True
        
        # 包含危险操作的SQL
        dangerous_sqls = [
            "DROP TABLE creators",
            "DELETE FROM creators",
            "UPDATE creators SET name = 'hacked'",
            "INSERT INTO creators VALUES (1, 'test')",
            "ALTER TABLE creators ADD COLUMN test INT"
        ]
        
        for sql in dangerous_sqls:
            assert vanna_core._validate_sql(sql) is False
    
    def test_validate_sql_empty(self, vanna_core):
        """测试空SQL验证"""
        assert vanna_core._validate_sql("") is False
        assert vanna_core._validate_sql("   ") is False
        assert vanna_core._validate_sql(None) is False
    
    def test_format_context_for_prompt(self, vanna_core):
        """测试格式化上下文为提示词"""
        context = {
            "schemas": [
                {"table_name": "creators", "ddl": "CREATE TABLE creators...", "relevance": 0.9}
            ],
            "examples": [
                {"question": "测试问题", "sql": "SELECT * FROM test", "relevance": 0.8}
            ],
            "documents": [
                {"title": "文档标题", "content": "文档内容", "relevance": 0.7}
            ]
        }
        
        formatted = vanna_core._format_context_for_prompt(context)
        
        assert "## 相关数据表结构" in formatted
        assert "CREATE TABLE creators" in formatted
        assert "## 相关SQL示例" in formatted
        assert "SELECT * FROM test" in formatted
        assert "## 相关业务文档" in formatted
        assert "文档内容" in formatted
    
    def test_format_context_empty(self, vanna_core):
        """测试格式化空上下文"""
        empty_context = {"schemas": [], "examples": [], "documents": []}
        
        formatted = vanna_core._format_context_for_prompt(empty_context)
        
        assert "暂无相关数据表结构" in formatted
        assert "暂无相关SQL示例" in formatted
        assert "暂无相关业务文档" in formatted
    
    @pytest.mark.asyncio
    async def test_close(self, vanna_core):
        """测试关闭资源"""
        await vanna_core.close()
        
        # 验证各组件的close方法被调用
        vanna_core.knowledge_base.close.assert_called_once()
        vanna_core.text_generator.close.assert_called_once()
        vanna_core.database_executor.close.assert_called_once()


class TestVannaCoreIntegration:
    """Vanna核心集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_query_workflow(self):
        """测试完整查询工作流"""
        with patch('core.vanna_core.KnowledgeBaseManager') as mock_kb_class:
            with patch('core.vanna_core.QwenTextGenerator') as mock_gen_class:
                with patch('core.vanna_core.DatabaseExecutor') as mock_db_class:
                    # 设置模拟对象
                    mock_kb = AsyncMock()
                    mock_gen = AsyncMock()
                    mock_db = AsyncMock()
                    
                    mock_kb_class.return_value = mock_kb
                    mock_gen_class.return_value = mock_gen
                    mock_db_class.return_value = mock_db
                    
                    # 配置模拟返回值
                    mock_kb.retrieve_context.return_value = {
                        "schemas": [{"table_name": "creators", "ddl": "CREATE TABLE creators..."}],
                        "examples": [{"question": "涨粉问题", "sql": "SELECT * FROM creators"}],
                        "documents": [{"content": "涨粉计算方式"}]
                    }
                    
                    mock_gen.generate_sql.return_value = {
                        "sql": "SELECT creator_name, follower_growth FROM creators ORDER BY follower_growth DESC LIMIT 1",
                        "confidence": 0.85,
                        "explanation": "查询涨粉最多的达人"
                    }
                    
                    mock_db.execute_query.return_value = {
                        "data": [{"creator_name": "顶级达人", "follower_growth": 50000}],
                        "columns": ["creator_name", "follower_growth"],
                        "row_count": 1,
                        "execution_time": 0.8
                    }
                    
                    # 创建Vanna核心并测试
                    vanna_core = VannaCore()
                    
                    user_query = UserQuery(
                        question="今天涨粉最多的达人是谁？",
                        user_id="test_user"
                    )
                    
                    result = await vanna_core.process_query(user_query)
                    
                    # 验证结果
                    assert isinstance(result, QueryResult)
                    assert result.data[0]["creator_name"] == "顶级达人"
                    assert result.data[0]["follower_growth"] == 50000
                    assert result.row_count == 1
                    assert result.error is None
                    
                    # 验证调用链
                    mock_kb.retrieve_context.assert_called_once()
                    mock_gen.generate_sql.assert_called_once()
                    mock_db.execute_query.assert_called_once()
                    
                    await vanna_core.close()
    
    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        with patch('core.vanna_core.KnowledgeBaseManager') as mock_kb_class:
            with patch('core.vanna_core.QwenTextGenerator') as mock_gen_class:
                with patch('core.vanna_core.DatabaseExecutor') as mock_db_class:
                    # 设置模拟对象
                    mock_kb = AsyncMock()
                    mock_gen = AsyncMock()
                    mock_db = AsyncMock()
                    
                    mock_kb_class.return_value = mock_kb
                    mock_gen_class.return_value = mock_gen
                    mock_db_class.return_value = mock_db
                    
                    # 模拟第一次SQL生成失败，第二次成功
                    mock_kb.retrieve_context.return_value = {"schemas": [], "examples": [], "documents": []}
                    mock_gen.generate_sql.side_effect = [
                        Exception("第一次生成失败"),
                        {
                            "sql": "SELECT * FROM creators LIMIT 1",
                            "confidence": 0.8,
                            "explanation": "简单查询"
                        }
                    ]
                    mock_db.execute_query.return_value = {
                        "data": [{"name": "测试达人"}],
                        "columns": ["name"],
                        "row_count": 1,
                        "execution_time": 0.3
                    }
                    
                    # 创建Vanna核心并测试
                    vanna_core = VannaCore()
                    vanna_core.max_retry_attempts = 2  # 设置重试次数
                    
                    user_query = UserQuery(question="测试查询", user_id="test_user")
                    
                    result = await vanna_core.process_query(user_query)
                    
                    # 第一次应该失败，因为我们没有实现重试逻辑
                    assert result.error is not None
                    
                    await vanna_core.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])