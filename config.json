{"qwen": {"api_key": "${QWEN_API_KEY}", "api_base": "https://dashscope.aliyuncs.com/api/v1", "model": "qwen-turbo", "timeout": 30, "max_retries": 3}, "database": {"host": "localhost", "port": 5432, "name": "tiktok_data", "user": "postgres", "password": "${DB_PASSWORD}", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30}, "redis": {"host": "localhost", "port": 6379, "db": 0, "password": "${REDIS_PASSWORD}", "timeout": 5, "max_connections": 10}, "app": {"name": "TikTok AI Agent", "version": "0.1.0", "debug": false, "log_level": "INFO", "host": "0.0.0.0", "port": 8000}, "vanna": {"model": "qwen", "db_type": "postgres", "embedding_dimension": 1536, "max_context_length": 4000, "similarity_threshold": 0.7}}