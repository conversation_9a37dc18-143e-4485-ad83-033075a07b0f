#!/usr/bin/env python3
"""
知识库训练脚本
重新训练知识库以确保使用正确的表结构
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def train_knowledge_base():
    """训练知识库"""
    try:
        from core.knowledge_trainer import KnowledgeTrainer
        from models.knowledge import SchemaInfo, BusinessDoc, SQLExample, TrainingData
        
        print("🔄 开始训练知识库...")
        
        # 创建训练器
        trainer = KnowledgeTrainer()
        
        # 加载训练数据
        training_file = Path("data/training_data.json")
        if not training_file.exists():
            print(f"❌ 训练数据文件不存在: {training_file}")
            return False
            
        with open(training_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 加载训练数据:")
        print(f"  - Schema数量: {len(data.get('schemas', []))}")
        print(f"  - 文档数量: {len(data.get('documents', []))}")
        print(f"  - SQL示例数量: {len(data.get('sql_examples', []))}")
        
        # 转换为训练数据对象
        schemas = [SchemaInfo(**schema) for schema in data.get('schemas', [])]
        documents = [BusinessDoc(**doc) for doc in data.get('documents', [])]
        sql_examples = [SQLExample(**example) for example in data.get('sql_examples', [])]
        
        training_data = TrainingData(
            schemas=schemas,
            documents=documents,
            sql_examples=sql_examples
        )
        
        # 执行训练
        print("🚀 开始训练...")
        success = await trainer.train_knowledge_base(training_data)
        
        if success:
            print("✅ 知识库训练成功！")
            
            # 验证训练结果
            print("🔍 验证训练结果...")
            kb_manager = trainer.kb_manager
            
            # 检查schema数量
            schemas_count = await kb_manager.get_schemas_count()
            print(f"  - 已存储Schema数量: {schemas_count}")
            
            # 测试检索
            test_question = "粉丝数最多的达人"
            print(f"🧪 测试检索: {test_question}")
            
            result = await kb_manager.similarity_search(test_question, top_k=3)
            print(f"  - 检索到 {result.total_results} 个相关项目")
            
            if result.schemas:
                print(f"  - 相关表: {[s.table_name for s in result.schemas]}")
            
        else:
            print("❌ 知识库训练失败")
        
        await trainer.close()
        return success
        
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(train_knowledge_base())
    sys.exit(0 if success else 1)