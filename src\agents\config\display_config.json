{"name": "展示Agent", "description": "负责数据分析、洞察生成和可视化展示的数据分析专家", "version": "1.0.0", "capabilities": ["数据分析和统计计算", "业务洞察生成", "趋势识别和模式发现", "数据可视化设计", "图表代码生成", "报告撰写和格式化", "多维度数据解读", "异常检测和解释"], "limitations": ["不直接连接数据库", "依赖输入数据的质量", "可视化限于预定义类型", "需要明确的分析目标", "受限于模型的理解能力"], "workflow": ["1. 接收查询结果数据", "2. 数据质量检查和预处理", "3. 统计分析和指标计算", "4. 模式识别和趋势分析", "5. 业务洞察生成", "6. 可视化方案设计", "7. 图表代码生成", "8. 分析报告整合", "9. 结果格式化和输出"], "performance_metrics": {"analysis_accuracy": "分析准确性 > 90%", "insight_relevance": "洞察相关性 > 85%", "visualization_quality": "可视化质量评分 > 4.0/5.0", "report_completeness": "报告完整性 > 95%", "processing_time": "平均处理时间 < 5秒"}, "analysis_config": {"model": "qwen-turbo", "temperature": 0.3, "max_tokens": 2000, "analysis_types": ["descriptive", "diagnostic", "predictive", "prescriptive"], "visualization_types": ["bar_chart", "line_chart", "pie_chart", "scatter_plot", "heatmap", "box_plot", "histogram", "area_chart"]}, "visualization_templates": {"bar_chart": "\nimport plotly.express as px\nimport pandas as pd\n\ndf = pd.DataFrame(data)\nfig = px.bar(df, x='{x_col}', y='{y_col}', \n             title='{title}',\n             color='{color_col}' if '{color_col}' in df.columns else None)\nfig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')\nst.plotly_chart(fig, use_container_width=True)\n", "line_chart": "\nimport plotly.express as px\nimport pandas as pd\n\ndf = pd.DataFrame(data)\nfig = px.line(df, x='{x_col}', y='{y_col}', \n              title='{title}',\n              color='{color_col}' if '{color_col}' in df.columns else None)\nfig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')\nst.plotly_chart(fig, use_container_width=True)\n", "pie_chart": "\nimport plotly.express as px\nimport pandas as pd\n\ndf = pd.DataFrame(data)\nfig = px.pie(df, values='{values_col}', names='{names_col}',\n             title='{title}')\nst.plotly_chart(fig, use_container_width=True)\n", "scatter_plot": "\nimport plotly.express as px\nimport pandas as pd\n\ndf = pd.DataFrame(data)\nfig = px.scatter(df, x='{x_col}', y='{y_col}',\n                 title='{title}',\n                 color='{color_col}' if '{color_col}' in df.columns else None,\n                 size='{size_col}' if '{size_col}' in df.columns else None)\nfig.update_layout(xaxis_title='{x_label}', yaxis_title='{y_label}')\nst.plotly_chart(fig, use_container_width=True)\n"}, "business_metrics_config": {"engagement_metrics": {"like_rate": {"formula": "like_count / view_count", "description": "点赞率", "good_threshold": 0.05, "excellent_threshold": 0.1}, "comment_rate": {"formula": "comment_count / view_count", "description": "评论率", "good_threshold": 0.01, "excellent_threshold": 0.03}, "share_rate": {"formula": "share_count / view_count", "description": "分享率", "good_threshold": 0.005, "excellent_threshold": 0.02}}, "growth_metrics": {"follower_growth_rate": {"formula": "follower_growth / previous_follower_count", "description": "粉丝增长率", "good_threshold": 0.01, "excellent_threshold": 0.05}, "video_performance": {"formula": "avg(view_count) per creator", "description": "视频平均表现", "good_threshold": 10000, "excellent_threshold": 100000}}}}