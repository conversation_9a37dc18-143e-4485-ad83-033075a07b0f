# TikTok AI Agent 测试系统

本目录包含了TikTok AI Agent项目的完整测试套件，包括单元测试、集成测试和性能测试。

## 📁 目录结构

```
tests/
├── README.md                           # 测试文档
├── run_all_tests.py                   # 测试运行器
├── test_utils.py                      # 测试工具和模拟数据
├── pytest.ini                        # pytest配置
│
├── 单元测试/
│   ├── test_models.py                 # 数据模型测试
│   ├── test_config.py                 # 配置管理测试
│   ├── test_knowledge_base.py         # 知识库测试
│   ├── test_knowledge_trainer.py      # 知识库训练测试
│   ├── test_qwen_embedding.py         # 千问嵌入测试
│   ├── test_router_agent.py           # 路由Agent测试
│   ├── test_display_agent.py          # 展示Agent测试
│   ├── test_agent_coordinator.py      # Agent协调器测试
│   ├── test_vanna_core.py             # Vanna核心测试
│   └── test_error_handler.py          # 错误处理测试
│
├── integration/                       # 集成测试
│   ├── test_end_to_end_workflow.py    # 端到端工作流测试
│   ├── test_api_integration.py        # API集成测试
│   └── test_ui_integration.py         # UI集成测试
│
└── performance/                       # 性能测试
    ├── test_load_testing.py           # 负载测试
    ├── test_stress_testing.py         # 压力测试
    └── test_optimization.py           # 性能优化测试
```

## 🚀 快速开始

### 安装测试依赖

```bash
pip install pytest pytest-asyncio pytest-mock psutil memory-profiler
```

### 运行所有测试

```bash
# 使用测试运行器
python tests/run_all_tests.py

# 或直接使用pytest
pytest tests/ -v
```

### 运行特定类型的测试

```bash
# 只运行单元测试
python tests/run_all_tests.py --type unit

# 只运行集成测试
python tests/run_all_tests.py --type integration

# 只运行性能测试
python tests/run_all_tests.py --type performance

# 跳过性能测试
python tests/run_all_tests.py --skip-performance
```

### 运行特定测试

```bash
# 运行特定测试文件
pytest tests/test_router_agent.py -v

# 运行特定测试类
pytest tests/test_router_agent.py::TestRouterAgent -v

# 运行特定测试方法
pytest tests/test_router_agent.py::TestRouterAgent::test_process_user_input -v

# 使用模式匹配
python tests/run_all_tests.py --pattern "router"
```

## 📊 测试类型说明

### 1. 单元测试 (Unit Tests)

测试单个组件或函数的功能，使用模拟对象隔离依赖。

**特点:**
- 快速执行
- 高覆盖率
- 独立运行
- 使用Mock对象

**示例:**
```python
@pytest.mark.asyncio
async def test_router_agent_intent_classification():
    # 测试路由Agent的意图分类功能
    agent = RouterAgent()
    result = await agent.classify_intent("今天涨粉最多的达人是谁？")
    assert result["intent"] == "data_query"
```

### 2. 集成测试 (Integration Tests)

测试多个组件之间的交互和协作。

**特点:**
- 测试组件间交互
- 端到端工作流
- 真实场景模拟
- 较长执行时间

**示例:**
```python
@pytest.mark.asyncio
async def test_complete_data_query_workflow():
    # 测试完整的数据查询工作流
    coordinator = AgentCoordinator()
    response = await coordinator.process_user_request("查询数据")
    assert response["success"] is True
```

### 3. 性能测试 (Performance Tests)

测试系统的性能指标，包括响应时间、吞吐量、资源使用等。

**特点:**
- 负载测试
- 压力测试
- 性能基准
- 资源监控

**示例:**
```python
@pytest.mark.asyncio
async def test_concurrent_load():
    # 测试并发负载下的性能
    tasks = [process_request(i) for i in range(100)]
    results = await asyncio.gather(*tasks)
    assert all(r["success"] for r in results)
```

## 🛠️ 测试工具

### MockDataGenerator

生成测试用的模拟数据：

```python
from tests.test_utils import MockDataGenerator

# 生成达人数据
creators = MockDataGenerator.generate_creator_data(10)

# 生成查询结果
query_result = MockDataGenerator.generate_query_result()

# 生成分析报告
report = MockDataGenerator.generate_report()
```

### MockServices

创建模拟服务：

```python
from tests.test_utils import MockServices

# 创建模拟千问服务
mock_qwen = MockServices.create_mock_qwen_text_generator()

# 创建模拟知识库
mock_kb = MockServices.create_mock_knowledge_base()
```

### TestAssertions

测试断言工具：

```python
from tests.test_utils import TestAssertions

# 断言API响应格式
TestAssertions.assert_api_response(response, success=True)

# 断言查询结果格式
TestAssertions.assert_query_result(result, min_rows=1)
```

## 📈 性能测试详解

### 负载测试

测试系统在正常和峰值负载下的表现：

```bash
# 运行负载测试
pytest tests/performance/test_load_testing.py -v -s
```

**测试场景:**
- 并发用户测试 (10-200用户)
- 持续负载测试 (30秒持续请求)
- 突发负载测试 (正常→突发→恢复)

### 压力测试

测试系统的极限和故障恢复能力：

```bash
# 运行压力测试
pytest tests/performance/test_stress_testing.py -v -s
```

**测试场景:**
- 最大并发用户数
- 资源耗尽场景
- 连接池耗尽
- 内存压力恢复

### 性能优化测试

测试各种优化策略的效果：

```bash
# 运行优化测试
pytest tests/performance/test_optimization.py -v -s
```

**优化测试:**
- 响应时间优化
- 内存使用优化
- 吞吐量优化
- 缓存效果测试

## 📋 测试报告

测试运行器会自动生成详细的测试报告：

```bash
# 生成测试报告
python tests/run_all_tests.py --report my_test_report.txt
```

报告包含：
- 测试套件统计
- 详细执行结果
- 性能指标
- 错误分析
- 优化建议

## 🔧 配置说明

### pytest.ini

pytest配置文件，定义了：
- 测试发现规则
- 输出格式
- 标记定义
- 警告过滤
- 超时设置

### 环境变量

测试可能需要的环境变量：

```bash
# 千问API密钥（用于真实API测试）
export QWEN_API_KEY="your_api_key"

# 数据库连接（用于真实数据库测试）
export DB_CONNECTION="postgresql://user:pass@host:port/db"

# 测试模式
export TEST_MODE="mock"  # mock | real
```

## 🎯 测试最佳实践

### 1. 测试命名

```python
# 好的测试命名
def test_router_agent_classifies_data_query_intent_correctly():
    pass

# 避免的命名
def test_router():
    pass
```

### 2. 测试结构

```python
# 使用AAA模式：Arrange, Act, Assert
def test_example():
    # Arrange - 准备测试数据
    agent = RouterAgent()
    user_input = "测试查询"
    
    # Act - 执行被测试的操作
    result = agent.process_input(user_input)
    
    # Assert - 验证结果
    assert result.success is True
```

### 3. 异步测试

```python
# 异步测试使用pytest.mark.asyncio
@pytest.mark.asyncio
async def test_async_function():
    result = await some_async_function()
    assert result is not None
```

### 4. 模拟使用

```python
# 使用patch装饰器模拟依赖
@patch('module.external_service')
def test_with_mock(mock_service):
    mock_service.return_value = "mocked_result"
    # 测试代码
```

## 🐛 调试测试

### 运行单个失败的测试

```bash
# 运行失败的测试
pytest tests/test_router_agent.py::test_specific_function -v -s

# 进入调试模式
pytest tests/test_router_agent.py::test_specific_function --pdb
```

### 查看详细输出

```bash
# 显示print输出
pytest tests/ -s

# 显示详细信息
pytest tests/ -vv

# 显示最慢的10个测试
pytest tests/ --durations=10
```

## 📚 相关资源

- [pytest官方文档](https://docs.pytest.org/)
- [pytest-asyncio文档](https://pytest-asyncio.readthedocs.io/)
- [unittest.mock文档](https://docs.python.org/3/library/unittest.mock.html)
- [性能测试最佳实践](https://martinfowler.com/articles/practical-test-pyramid.html)

## 🤝 贡献指南

### 添加新测试

1. 确定测试类型（单元/集成/性能）
2. 选择合适的测试文件或创建新文件
3. 遵循现有的测试模式和命名约定
4. 添加适当的文档和注释
5. 确保测试可以独立运行

### 测试覆盖率

```bash
# 安装覆盖率工具
pip install pytest-cov

# 运行带覆盖率的测试
pytest tests/ --cov=src --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

## ❓ 常见问题

### Q: 测试运行很慢怎么办？

A: 
- 使用`--maxfail=1`快速失败
- 运行特定测试而不是全部
- 跳过性能测试：`--skip-performance`
- 使用并行测试：`pip install pytest-xdist`，然后`pytest -n auto`

### Q: 如何模拟外部API？

A: 使用`unittest.mock.patch`或测试工具中的`MockServices`：

```python
@patch('module.external_api_call')
def test_with_mocked_api(mock_api):
    mock_api.return_value = {"status": "success"}
    # 测试代码
```

### Q: 异步测试失败怎么办？

A: 
- 确保使用`@pytest.mark.asyncio`装饰器
- 检查是否正确使用`await`
- 验证异步资源是否正确关闭

### Q: 性能测试不稳定怎么办？

A:
- 增加测试运行次数取平均值
- 调整性能阈值
- 在专用测试环境中运行
- 考虑系统负载影响

---

🎉 **祝测试愉快！** 如有问题，请查看测试输出或联系开发团队。