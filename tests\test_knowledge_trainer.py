"""
知识库训练器测试
"""

import asyncio
import pytest
import tempfile
import shutil
import json
import yaml
from pathlib import Path
from unittest.mock import AsyncMock, patch

from src.core.knowledge_trainer import KnowledgeTrainer, create_sample_training_data
from src.core.knowledge_base import KnowledgeBaseManager
from src.core.config import load_config


class TestKnowledgeTrainer:
    """知识库训练器测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        try:
            shutil.rmtree(temp_dir)
        except:
            pass
    
    @pytest.fixture
    def mock_kb_manager(self):
        """模拟知识库管理器"""
        manager = AsyncMock(spec=KnowledgeBaseManager)
        manager.add_schema = AsyncMock(return_value=True)
        manager.add_business_doc = AsyncMock(return_value=True)
        manager.add_sql_example = AsyncMock(return_value=True)
        manager.generate_embeddings_for_all = AsyncMock(return_value={
            "schemas": 2, "docs": 2, "examples": 2
        })
        manager.clear_all = AsyncMock(return_value=True)
        return manager
    
    @pytest.fixture
    def trainer(self, mock_kb_manager):
        """创建训练器实例"""
        return KnowledgeTrainer(mock_kb_manager)
    
    @pytest.fixture
    def sample_training_data(self):
        """示例训练数据"""
        return {
            "schemas": [
                {
                    "table_name": "test_table",
                    "ddl": "CREATE TABLE test_table (id INT PRIMARY KEY)",
                    "description": "测试表"
                }
            ],
            "documents": [
                {
                    "title": "测试文档",
                    "content": "这是一个测试文档",
                    "category": "test"
                }
            ],
            "sql_examples": [
                {
                    "question": "如何查询测试表？",
                    "sql": "SELECT * FROM test_table",
                    "explanation": "查询测试表的所有数据",
                    "difficulty": "easy"
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_process_training_data(self, trainer, sample_training_data):
        """测试处理训练数据"""
        result = await trainer._process_training_data(sample_training_data)
        
        assert result is True
        assert trainer.stats["schemas_added"] == 1
        assert trainer.stats["docs_added"] == 1
        assert trainer.stats["examples_added"] == 1
        
        # 验证调用了正确的方法
        trainer.kb_manager.add_schema.assert_called_once()
        trainer.kb_manager.add_business_doc.assert_called_once()
        trainer.kb_manager.add_sql_example.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_from_json(self, trainer, sample_training_data, temp_dir):
        """测试从JSON文件加载"""
        # 创建JSON文件
        json_file = Path(temp_dir) / "test_data.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(sample_training_data, f)
        
        # 加载数据
        result = await trainer.load_from_json(json_file)
        
        assert result is True
        assert trainer.stats["schemas_added"] == 1
        assert trainer.stats["docs_added"] == 1
        assert trainer.stats["examples_added"] == 1
    
    @pytest.mark.asyncio
    async def test_load_from_yaml(self, trainer, sample_training_data, temp_dir):
        """测试从YAML文件加载"""
        # 创建YAML文件
        yaml_file = Path(temp_dir) / "test_data.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump(sample_training_data, f)
        
        # 加载数据
        result = await trainer.load_from_yaml(yaml_file)
        
        assert result is True
        assert trainer.stats["schemas_added"] == 1
        assert trainer.stats["docs_added"] == 1
        assert trainer.stats["examples_added"] == 1
    
    @pytest.mark.asyncio
    async def test_load_from_directory(self, trainer, sample_training_data, temp_dir):
        """测试从目录批量加载"""
        temp_path = Path(temp_dir)
        
        # 创建多个文件
        json_file = temp_path / "data1.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({"schemas": sample_training_data["schemas"]}, f)
        
        yaml_file = temp_path / "data2.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump({"documents": sample_training_data["documents"]}, f)
        
        yml_file = temp_path / "data3.yml"
        with open(yml_file, 'w', encoding='utf-8') as f:
            yaml.dump({"sql_examples": sample_training_data["sql_examples"]}, f)
        
        # 加载目录
        result = await trainer.load_from_directory(temp_path)
        
        assert result is True
        assert trainer.stats["schemas_added"] == 1
        assert trainer.stats["docs_added"] == 1
        assert trainer.stats["examples_added"] == 1
    
    @pytest.mark.asyncio
    async def test_load_nonexistent_file(self, trainer):
        """测试加载不存在的文件"""
        result = await trainer.load_from_json("nonexistent.json")
        assert result is False
        
        result = await trainer.load_from_yaml("nonexistent.yaml")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_add_schema_missing_fields(self, trainer):
        """测试添加缺少必需字段的Schema"""
        # 缺少table_name
        result = await trainer._add_schema_from_data({"ddl": "CREATE TABLE test (id INT)"})
        assert result is False
        
        # 缺少ddl
        result = await trainer._add_schema_from_data({"table_name": "test"})
        assert result is False
    
    @pytest.mark.asyncio
    async def test_add_document_missing_fields(self, trainer):
        """测试添加缺少必需字段的文档"""
        # 缺少title
        result = await trainer._add_document_from_data({"content": "test content"})
        assert result is False
        
        # 缺少content
        result = await trainer._add_document_from_data({"title": "test title"})
        assert result is False
    
    @pytest.mark.asyncio
    async def test_add_sql_example_missing_fields(self, trainer):
        """测试添加缺少必需字段的SQL范例"""
        # 缺少question
        result = await trainer._add_sql_example_from_data({"sql": "SELECT * FROM test"})
        assert result is False
        
        # 缺少sql
        result = await trainer._add_sql_example_from_data({"question": "test question"})
        assert result is False
    
    @pytest.mark.asyncio
    async def test_generate_embeddings(self, trainer):
        """测试生成嵌入向量"""
        result = await trainer.generate_embeddings()
        
        assert result is True
        assert trainer.stats["embeddings_generated"] == 6  # 2+2+2
        trainer.kb_manager.generate_embeddings_for_all.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rebuild_knowledge_base(self, trainer):
        """测试重建知识库"""
        # 先添加一些数据
        trainer.stats["schemas_added"] = 5
        trainer.stats["docs_added"] = 3
        
        result = await trainer.rebuild_knowledge_base()
        
        assert result is True
        trainer.kb_manager.clear_all.assert_called_once()
        
        # 验证统计信息被重置
        assert trainer.stats["schemas_added"] == 0
        assert trainer.stats["docs_added"] == 0
        assert trainer.stats["examples_added"] == 0
        assert trainer.stats["embeddings_generated"] == 0
        assert trainer.stats["errors"] == []
    
    def test_get_training_stats(self, trainer):
        """测试获取训练统计信息"""
        # 设置一些统计数据
        trainer.stats["schemas_added"] = 2
        trainer.stats["docs_added"] = 3
        trainer.stats["examples_added"] = 1
        trainer.stats["embeddings_generated"] = 6
        trainer.stats["errors"] = ["error1"]
        
        stats = trainer.get_training_stats()
        
        assert stats["schemas_added"] == 2
        assert stats["docs_added"] == 3
        assert stats["examples_added"] == 1
        assert stats["embeddings_generated"] == 6
        assert stats["total_items_added"] == 6
        assert stats["success_rate"] == 6/7  # 6 successful, 1 error
        assert "timestamp" in stats
    
    def test_calculate_success_rate(self, trainer):
        """测试成功率计算"""
        # 没有任何操作
        assert trainer._calculate_success_rate() == 0.0
        
        # 全部成功
        trainer.stats["schemas_added"] = 2
        trainer.stats["docs_added"] = 3
        assert trainer._calculate_success_rate() == 1.0
        
        # 部分失败
        trainer.stats["errors"] = ["error1", "error2"]
        assert trainer._calculate_success_rate() == 5/7
    
    @pytest.mark.asyncio
    async def test_kb_manager_failure(self, trainer):
        """测试知识库管理器操作失败的情况"""
        # 模拟添加Schema失败
        trainer.kb_manager.add_schema = AsyncMock(return_value=False)
        
        result = await trainer._add_schema_from_data({
            "table_name": "test",
            "ddl": "CREATE TABLE test (id INT)"
        })
        
        assert result is False
        assert trainer.stats["schemas_added"] == 0


class TestCreateSampleTrainingData:
    """测试创建示例训练数据"""
    
    @pytest.mark.asyncio
    async def test_create_sample_data(self, tmp_path):
        """测试创建示例数据文件"""
        output_file = tmp_path / "sample_data.json"
        
        await create_sample_training_data(output_file)
        
        # 验证文件存在
        assert output_file.exists()
        
        # 验证文件内容
        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert "schemas" in data
        assert "documents" in data
        assert "sql_examples" in data
        
        # 验证数据结构
        assert isinstance(data["schemas"], list)
        assert len(data["schemas"]) > 0
        
        # 验证Schema结构
        schema = data["schemas"][0]
        assert "table_name" in schema
        assert "ddl" in schema
        assert "description" in schema
        
        # 验证文档结构
        doc = data["documents"][0]
        assert "title" in doc
        assert "content" in doc
        assert "category" in doc
        
        # 验证SQL范例结构
        example = data["sql_examples"][0]
        assert "question" in example
        assert "sql" in example
        assert "explanation" in example
        assert "difficulty" in example


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])