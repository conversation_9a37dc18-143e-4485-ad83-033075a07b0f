"""知识库相关数据模型"""

from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .base import BaseEntity


class SchemaInfo(BaseEntity):
    """数据库Schema信息模型"""
    table_name: str = Field(..., description="表名")
    ddl: str = Field(..., description="DDL语句")
    description: Optional[str] = Field(None, description="表描述")
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "table_name": "creators",
                "ddl": "CREATE TABLE creators (id BIGINT PRIMARY KEY, ...)",
                "description": "TikTok达人基础信息表"
            }
        }


class BusinessDoc(BaseEntity):
    """业务文档模型"""
    title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文档内容")
    category: Optional[str] = Field(None, description="文档分类")
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "涨粉量计算方式",
                "content": "涨粉量 = 当前粉丝数 - 前一天粉丝数",
                "category": "business_rules"
            }
        }


class SQLExample(BaseEntity):
    """SQL范例模型"""
    question: str = Field(..., description="问题")
    sql: str = Field(..., description="SQL语句")
    explanation: Optional[str] = Field(None, description="解释说明")
    difficulty: Optional[str] = Field(None, description="难度等级")
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    
    class Config:
        json_schema_extra = {
            "example": {
                "question": "过去一周哪个游戏达人播放量最高？",
                "sql": "SELECT c.username, SUM(v.view_count) FROM creators c...",
                "explanation": "查询游戏分类达人的总播放量",
                "difficulty": "medium"
            }
        }


class TrainingData(BaseModel):
    """训练数据模型"""
    schemas: List[SchemaInfo] = Field(default_factory=list)
    documents: List[BusinessDoc] = Field(default_factory=list)
    sql_examples: List[SQLExample] = Field(default_factory=list)


class RetrievalResult(BaseModel):
    """检索结果模型"""
    schemas: List[SchemaInfo] = Field(default_factory=list)
    documents: List[BusinessDoc] = Field(default_factory=list)
    sql_examples: List[SQLExample] = Field(default_factory=list)
    similarity_scores: List[float] = Field(default_factory=list)
    total_results: int = 0