"""
Agent协调器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import uuid

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agents.agent_coordinator import (
    AgentCoordinator, TaskStatus, TaskType, AgentMessage, Task, MessageBus,
    get_coordinator, close_coordinator
)
from agents.display_agent import Report, AnalysisResult, VisualizationCode
from models.query import QueryResult
from models.base import APIResponse


class TestAgentMessage:
    """Agent消息测试"""
    
    def test_message_creation(self):
        """测试消息创建"""
        payload = {"data": "test"}
        message = AgentMessage("sender", "receiver", "test_type", payload)
        
        assert message.sender == "sender"
        assert message.receiver == "receiver"
        assert message.message_type == "test_type"
        assert message.payload == payload
        assert message.correlation_id == message.id
        assert isinstance(message.timestamp, datetime)
        assert message.processed is False
    
    def test_message_with_correlation_id(self):
        """测试带关联ID的消息"""
        correlation_id = "test_correlation"
        message = AgentMessage("sender", "receiver", "test_type", {}, correlation_id)
        
        assert message.correlation_id == correlation_id


class TestTask:
    """任务测试"""
    
    def test_task_creation(self):
        """测试任务创建"""
        task_id = "test_task"
        payload = {"data": "test"}
        task = Task(task_id, TaskType.USER_QUERY, payload)
        
        assert task.task_id == task_id
        assert task.task_type == TaskType.USER_QUERY
        assert task.payload == payload
        assert task.status == TaskStatus.PENDING
        assert isinstance(task.created_at, datetime)
        assert task.started_at is None
        assert task.completed_at is None
        assert task.result is None
        assert task.error is None
        assert task.progress == 0.0
    
    def test_task_start(self):
        """测试任务开始"""
        task = Task("test", TaskType.USER_QUERY, {})
        
        task.start()
        
        assert task.status == TaskStatus.RUNNING
        assert isinstance(task.started_at, datetime)
        assert task.progress == 0.1
    
    def test_task_complete(self):
        """测试任务完成"""
        task = Task("test", TaskType.USER_QUERY, {})
        result = {"success": True}
        
        task.complete(result)
        
        assert task.status == TaskStatus.COMPLETED
        assert isinstance(task.completed_at, datetime)
        assert task.result == result
        assert task.progress == 1.0
    
    def test_task_fail(self):
        """测试任务失败"""
        task = Task("test", TaskType.USER_QUERY, {})
        error = "Task failed"
        
        task.fail(error)
        
        assert task.status == TaskStatus.FAILED
        assert isinstance(task.completed_at, datetime)
        assert task.error == error
        assert task.progress == 0.0
    
    def test_update_progress(self):
        """测试更新进度"""
        task = Task("test", TaskType.USER_QUERY, {})
        
        task.update_progress(0.5)
        assert task.progress == 0.5
        
        # 测试边界值
        task.update_progress(-0.1)
        assert task.progress == 0.0
        
        task.update_progress(1.5)
        assert task.progress == 1.0


class TestMessageBus:
    """消息总线测试"""
    
    @pytest.fixture
    def message_bus(self):
        """创建消息总线实例"""
        return MessageBus()
    
    def test_message_bus_initialization(self, message_bus):
        """测试消息总线初始化"""
        assert message_bus.subscribers == {}
        assert message_bus.message_queue == []
        assert message_bus.processing is False
    
    def test_subscribe(self, message_bus):
        """测试订阅消息"""
        handler = Mock()
        
        message_bus.subscribe("test_type", handler)
        
        assert "test_type" in message_bus.subscribers
        assert handler in message_bus.subscribers["test_type"]
    
    def test_subscribe_multiple_handlers(self, message_bus):
        """测试订阅多个处理器"""
        handler1 = Mock()
        handler2 = Mock()
        
        message_bus.subscribe("test_type", handler1)
        message_bus.subscribe("test_type", handler2)
        
        assert len(message_bus.subscribers["test_type"]) == 2
        assert handler1 in message_bus.subscribers["test_type"]
        assert handler2 in message_bus.subscribers["test_type"]
    
    @pytest.mark.asyncio
    async def test_publish_message(self, message_bus):
        """测试发布消息"""
        handler = AsyncMock()
        message_bus.subscribe("test_type", handler)
        
        message = AgentMessage("sender", "receiver", "test_type", {"data": "test"})
        await message_bus.publish(message)
        
        # 等待消息处理完成
        await asyncio.sleep(0.1)
        
        assert message.processed is True
        handler.assert_called_once_with(message)
    
    @pytest.mark.asyncio
    async def test_publish_no_subscribers(self, message_bus):
        """测试发布无订阅者的消息"""
        message = AgentMessage("sender", "receiver", "unknown_type", {"data": "test"})
        await message_bus.publish(message)
        
        # 等待消息处理完成
        await asyncio.sleep(0.1)
        
        assert message.processed is True
    
    @pytest.mark.asyncio
    async def test_handler_exception(self, message_bus):
        """测试处理器异常"""
        handler = AsyncMock(side_effect=Exception("Handler failed"))
        message_bus.subscribe("test_type", handler)
        
        message = AgentMessage("sender", "receiver", "test_type", {"data": "test"})
        await message_bus.publish(message)
        
        # 等待消息处理完成
        await asyncio.sleep(0.1)
        
        # 消息应该仍然被标记为已处理
        assert message.processed is True


class TestAgentCoordinator:
    """Agent协调器测试"""
    
    @pytest.fixture
    def coordinator(self):
        """创建协调器实例"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display:
                    mock_vanna.return_value = AsyncMock()
                    mock_router.return_value = AsyncMock()
                    mock_display.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    return coordinator
    
    def test_coordinator_initialization(self, coordinator):
        """测试协调器初始化"""
        assert coordinator.vanna_core is not None
        assert coordinator.router_agent is not None
        assert coordinator.display_agent is not None
        assert coordinator.message_bus is not None
        assert coordinator.tasks == {}
        assert coordinator.active_sessions == {}
        assert coordinator._task_processor_running is False
    
    @pytest.mark.asyncio
    async def test_start_coordinator(self, coordinator):
        """测试启动协调器"""
        await coordinator.start()
        
        assert coordinator._task_processor_running is True
    
    @pytest.mark.asyncio
    async def test_process_user_request_non_data_query(self, coordinator):
        """测试处理非数据查询用户请求"""
        # 模拟路由Agent返回非数据查询结果
        mock_response = APIResponse(
            success=True,
            message="聊天回复",
            data={"type": "chat", "response": "你好！"}
        )
        coordinator.router_agent.process_user_input.return_value = mock_response
        
        response = await coordinator.process_user_request("你好", "session1", "user1")
        
        assert response.success is True
        assert response.data["type"] == "chat"
        coordinator.router_agent.process_user_input.assert_called_once_with(
            "你好", "session1", "user1"
        )
    
    @pytest.mark.asyncio
    async def test_process_user_request_data_query(self, coordinator):
        """测试处理数据查询用户请求"""
        # 模拟路由Agent返回数据查询结果
        router_response = APIResponse(
            success=True,
            message="数据查询完成",
            data={
                "type": "data_query",
                "sql": "SELECT * FROM creators",
                "data": [{"name": "达人A", "fans": 100000}],
                "columns": ["name", "fans"],
                "row_count": 1,
                "execution_time": 0.5
            }
        )
        coordinator.router_agent.process_user_input.return_value = router_response
        
        # 模拟DisplayAgent返回报告
        mock_analysis = AnalysisResult(
            summary="分析摘要",
            insights=["洞察1"],
            recommendations=["建议1"],
            key_metrics={"total": 1}
        )
        mock_viz = VisualizationCode("bar", "code", "title", "desc")
        mock_report = Report(mock_analysis, [mock_viz], None)
        coordinator.display_agent.create_report.return_value = mock_report
        
        response = await coordinator.process_user_request("粉丝最多的达人", "session1", "user1")
        
        assert response.success is True
        assert response.data["type"] == "complete_analysis"
        assert "analysis" in response.data
        assert "visualizations" in response.data
        assert "raw_data" in response.data
        
        coordinator.display_agent.create_report.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_user_request_router_failure(self, coordinator):
        """测试路由Agent失败的处理"""
        # 模拟路由Agent返回失败结果
        mock_response = APIResponse(
            success=False,
            message="路由失败",
            error="意图识别失败"
        )
        coordinator.router_agent.process_user_input.return_value = mock_response
        
        response = await coordinator.process_user_request("测试", "session1")
        
        assert response.success is False
        assert response.message == "路由失败"
    
    @pytest.mark.asyncio
    async def test_process_user_request_exception(self, coordinator):
        """测试处理用户请求异常"""
        # 模拟路由Agent抛出异常
        coordinator.router_agent.process_user_input.side_effect = Exception("处理失败")
        
        response = await coordinator.process_user_request("测试", "session1")
        
        assert response.success is False
        assert "处理用户请求时发生错误" in response.message
        assert response.error is not None
    
    @pytest.mark.asyncio
    async def test_handle_data_analysis_workflow(self, coordinator):
        """测试数据分析工作流处理"""
        query_data = {
            "data": [{"creator": "达人A", "fans": 100000}],
            "columns": ["creator", "fans"],
            "row_count": 1,
            "execution_time": 0.5,
            "sql": "SELECT creator, fans FROM creators"
        }
        
        # 模拟DisplayAgent返回报告
        mock_analysis = AnalysisResult(
            summary="测试分析",
            insights=["洞察1", "洞察2"],
            recommendations=["建议1"],
            key_metrics={"fans_sum": 100000}
        )
        mock_viz = VisualizationCode("bar", "test_code", "测试图表", "图表描述")
        mock_report = Report(mock_analysis, [mock_viz], None)
        coordinator.display_agent.create_report.return_value = mock_report
        
        response = await coordinator._handle_data_analysis_workflow(
            query_data, "task123", "session1", "测试问题"
        )
        
        assert response.success is True
        assert response.data["type"] == "complete_analysis"
        assert response.data["task_id"] == "task123"
        assert response.data["query"] == "测试问题"
        assert response.data["analysis"]["summary"] == "测试分析"
        assert len(response.data["analysis"]["insights"]) == 2
        assert len(response.data["visualizations"]) == 1
        assert response.data["visualizations"][0]["chart_type"] == "bar"
    
    @pytest.mark.asyncio
    async def test_handle_data_analysis_workflow_error(self, coordinator):
        """测试数据分析工作流错误处理"""
        query_data = {"data": [], "columns": [], "row_count": 0}
        
        # 模拟DisplayAgent抛出异常
        coordinator.display_agent.create_report.side_effect = Exception("分析失败")
        
        response = await coordinator._handle_data_analysis_workflow(
            query_data, "task123", "session1", "测试问题"
        )
        
        assert response.success is False
        assert "数据分析工作流处理失败" in response.message
    
    @pytest.mark.asyncio
    async def test_create_task(self, coordinator):
        """测试创建任务"""
        payload = {"data": "test"}
        
        task_id = await coordinator.create_task(TaskType.USER_QUERY, payload)
        
        assert task_id in coordinator.tasks
        task = coordinator.tasks[task_id]
        assert task.task_type == TaskType.USER_QUERY
        assert task.payload == payload
        assert task.status == TaskStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, coordinator):
        """测试获取任务状态"""
        task_id = await coordinator.create_task(TaskType.USER_QUERY, {"data": "test"})
        
        status = await coordinator.get_task_status(task_id)
        
        assert status is not None
        assert status["task_id"] == task_id
        assert status["task_type"] == "user_query"
        assert status["status"] == "pending"
        assert status["progress"] == 0.0
    
    @pytest.mark.asyncio
    async def test_get_task_status_not_found(self, coordinator):
        """测试获取不存在任务的状态"""
        status = await coordinator.get_task_status("nonexistent")
        
        assert status is None
    
    @pytest.mark.asyncio
    async def test_cancel_task(self, coordinator):
        """测试取消任务"""
        task_id = await coordinator.create_task(TaskType.USER_QUERY, {"data": "test"})
        
        result = await coordinator.cancel_task(task_id)
        
        assert result is True
        task = coordinator.tasks[task_id]
        assert task.status == TaskStatus.CANCELLED
        assert task.completed_at is not None
    
    @pytest.mark.asyncio
    async def test_cancel_completed_task(self, coordinator):
        """测试取消已完成的任务"""
        task_id = await coordinator.create_task(TaskType.USER_QUERY, {"data": "test"})
        task = coordinator.tasks[task_id]
        task.complete({"result": "done"})
        
        result = await coordinator.cancel_task(task_id)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cancel_nonexistent_task(self, coordinator):
        """测试取消不存在的任务"""
        result = await coordinator.cancel_task("nonexistent")
        
        assert result is False
    
    def test_get_system_status(self, coordinator):
        """测试获取系统状态"""
        # 创建一些任务
        task1 = Task("task1", TaskType.USER_QUERY, {})
        task2 = Task("task2", TaskType.DATA_ANALYSIS, {})
        task3 = Task("task3", TaskType.VISUALIZATION, {})
        
        task1.start()
        task2.complete({"result": "done"})
        task3.fail("error")
        
        coordinator.tasks = {"task1": task1, "task2": task2, "task3": task3}
        
        status = coordinator.get_system_status()
        
        assert status["active_tasks"] == 1
        assert status["pending_tasks"] == 0
        assert status["completed_tasks"] == 1
        assert status["failed_tasks"] == 1
        assert status["active_sessions"] == 0
        assert status["message_queue_size"] == 0
    
    @pytest.mark.asyncio
    async def test_cleanup_old_tasks(self, coordinator):
        """测试清理旧任务"""
        # 创建旧任务
        old_task = Task("old_task", TaskType.USER_QUERY, {})
        old_task.created_at = datetime(2020, 1, 1)  # 很久以前
        old_task.complete({"result": "done"})
        
        # 创建新任务
        new_task = Task("new_task", TaskType.USER_QUERY, {})
        
        coordinator.tasks = {"old_task": old_task, "new_task": new_task}
        
        await coordinator.cleanup_old_tasks(max_age_hours=1)
        
        # 旧任务应该被清理，新任务保留
        assert "old_task" not in coordinator.tasks
        assert "new_task" in coordinator.tasks
    
    @pytest.mark.asyncio
    async def test_cleanup_old_tasks_keep_active(self, coordinator):
        """测试清理旧任务时保留活跃任务"""
        # 创建旧的但仍在运行的任务
        old_running_task = Task("old_running", TaskType.USER_QUERY, {})
        old_running_task.created_at = datetime(2020, 1, 1)
        old_running_task.start()
        
        coordinator.tasks = {"old_running": old_running_task}
        
        await coordinator.cleanup_old_tasks(max_age_hours=1)
        
        # 运行中的任务不应该被清理
        assert "old_running" in coordinator.tasks
    
    @pytest.mark.asyncio
    async def test_close_coordinator(self, coordinator):
        """测试关闭协调器"""
        # 创建一些任务
        task1 = Task("task1", TaskType.USER_QUERY, {})
        task2 = Task("task2", TaskType.DATA_ANALYSIS, {})
        task1.start()
        
        coordinator.tasks = {"task1": task1, "task2": task2}
        
        await coordinator.close()
        
        # 验证Agent的close方法被调用
        coordinator.router_agent.close.assert_called_once()
        coordinator.display_agent.close.assert_called_once()
        coordinator.vanna_core.close.assert_called_once()
        
        # 验证任务被取消
        assert task1.status == TaskStatus.CANCELLED
        assert task2.status == TaskStatus.CANCELLED


class TestGlobalCoordinator:
    """全局协调器测试"""
    
    @pytest.mark.asyncio
    async def test_get_coordinator_singleton(self):
        """测试获取单例协调器"""
        with patch('agents.agent_coordinator.AgentCoordinator') as mock_coordinator_class:
            mock_coordinator = AsyncMock()
            mock_coordinator_class.return_value = mock_coordinator
            
            # 第一次获取
            coordinator1 = await get_coordinator()
            
            # 第二次获取应该返回同一个实例
            coordinator2 = await get_coordinator()
            
            assert coordinator1 is coordinator2
            mock_coordinator_class.assert_called_once()
            mock_coordinator.start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_coordinator_global(self):
        """测试关闭全局协调器"""
        with patch('agents.agent_coordinator.AgentCoordinator') as mock_coordinator_class:
            mock_coordinator = AsyncMock()
            mock_coordinator_class.return_value = mock_coordinator
            
            # 获取协调器
            coordinator = await get_coordinator()
            
            # 关闭协调器
            await close_coordinator()
            
            mock_coordinator.close.assert_called_once()


class TestTaskProcessing:
    """任务处理测试"""
    
    @pytest.fixture
    def coordinator_with_mocks(self):
        """创建带模拟的协调器"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display:
                    mock_vanna.return_value = AsyncMock()
                    mock_router.return_value = AsyncMock()
                    mock_display.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    return coordinator
    
    @pytest.mark.asyncio
    async def test_process_user_query_task(self, coordinator_with_mocks):
        """测试处理用户查询任务"""
        coordinator = coordinator_with_mocks
        
        # 模拟路由Agent返回
        mock_response = APIResponse(success=True, message="成功", data={"result": "test"})
        coordinator.router_agent.process_user_input.return_value = mock_response
        
        # 创建任务
        task = Task("test_task", TaskType.USER_QUERY, {
            "user_input": "测试问题",
            "session_id": "session1",
            "user_id": "user1"
        })
        
        result = await coordinator._process_user_query_task(task)
        
        assert result == mock_response
        coordinator.router_agent.process_user_input.assert_called_once_with(
            "测试问题", "session1", "user1"
        )
    
    @pytest.mark.asyncio
    async def test_process_data_analysis_task(self, coordinator_with_mocks):
        """测试处理数据分析任务"""
        coordinator = coordinator_with_mocks
        
        # 模拟DisplayAgent返回
        mock_analysis = AnalysisResult("摘要", ["洞察"], ["建议"], {"metric": 1})
        coordinator.display_agent.analyze_data.return_value = mock_analysis
        
        # 创建任务
        query_result = QueryResult(data=[{"test": "data"}], columns=["test"], row_count=1, execution_time=0.5)
        task = Task("test_task", TaskType.DATA_ANALYSIS, {
            "query_result": query_result,
            "original_question": "测试问题"
        })
        
        result = await coordinator._process_data_analysis_task(task)
        
        assert result == mock_analysis
        coordinator.display_agent.analyze_data.assert_called_once_with(
            query_result, "测试问题"
        )
    
    @pytest.mark.asyncio
    async def test_process_visualization_task(self, coordinator_with_mocks):
        """测试处理可视化任务"""
        coordinator = coordinator_with_mocks
        
        # 模拟DisplayAgent返回
        mock_viz = [VisualizationCode("bar", "code", "title", "desc")]
        coordinator.display_agent.generate_visualization.return_value = mock_viz
        
        # 创建任务
        query_result = QueryResult(data=[{"test": "data"}], columns=["test"], row_count=1, execution_time=0.5)
        task = Task("test_task", TaskType.VISUALIZATION, {
            "query_result": query_result,
            "chart_type": "bar_chart"
        })
        
        result = await coordinator._process_visualization_task(task)
        
        assert result == mock_viz
        coordinator.display_agent.generate_visualization.assert_called_once_with(
            query_result, "bar_chart"
        )
    
    @pytest.mark.asyncio
    async def test_process_report_generation_task(self, coordinator_with_mocks):
        """测试处理报告生成任务"""
        coordinator = coordinator_with_mocks
        
        # 模拟DisplayAgent返回
        mock_report = Report(
            AnalysisResult("摘要", ["洞察"], ["建议"], {}),
            [VisualizationCode("bar", "code", "title", "desc")],
            None
        )
        coordinator.display_agent.create_report.return_value = mock_report
        
        # 创建任务
        query_result = QueryResult(data=[{"test": "data"}], columns=["test"], row_count=1, execution_time=0.5)
        task = Task("test_task", TaskType.REPORT_GENERATION, {
            "query_result": query_result,
            "original_question": "测试问题"
        })
        
        result = await coordinator._process_report_generation_task(task)
        
        assert result == mock_report
        coordinator.display_agent.create_report.assert_called_once_with(
            query_result, "测试问题"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])