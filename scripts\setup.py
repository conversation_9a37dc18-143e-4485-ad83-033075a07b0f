#!/usr/bin/env python3
"""
TikTok AI Agent 快速设置脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 11):
        print("❌ Python版本需要3.11或更高")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True


def check_env_file():
    """检查环境配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 创建环境配置文件...")
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ 已创建 .env 文件")
            print("⚠️  请编辑 .env 文件，设置千问API密钥")
            return False
        else:
            print("❌ 找不到 .env.example 文件")
            return False
    
    # 检查关键配置
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    if "your_qwen_api_key_here" in content:
        print("⚠️  请在 .env 文件中设置千问API密钥")
        return False
        
    print("✅ 环境配置文件已就绪")
    return True


def install_dependencies():
    """安装依赖"""
    print("📦 安装Python依赖...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("尝试使用国内镜像:")
        print("pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    try:
        subprocess.run([sys.executable, "tests/test_models.py"], 
                      check=True, capture_output=True)
        print("✅ 基本功能测试通过")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False


def show_startup_commands():
    """显示启动命令"""
    print("\n🚀 启动命令:")
    print("=" * 50)
    print("UI界面模式:")
    print("  python main.py --mode ui --port 8501")
    print("  然后访问: http://localhost:8501")
    print()
    print("API接口模式:")
    print("  python main.py --mode api --port 8000")
    print("  然后访问: http://localhost:8000/docs")
    print()
    print("Docker启动:")
    print("  docker-compose up -d")
    print("=" * 50)


def main():
    """主函数"""
    print("🎯 TikTok AI Agent 环境设置")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    # 检查环境配置
    env_ready = check_env_file()
    
    # 安装依赖
    if not install_dependencies():
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        print("⚠️  基本功能测试失败，但可以尝试启动服务")
    
    # 显示启动命令
    show_startup_commands()
    
    if not env_ready:
        print("\n⚠️  重要提醒:")
        print("1. 请编辑 .env 文件，设置千问API密钥")
        print("2. 获取API密钥: https://bailian.console.aliyun.com/")
        print("3. 设置完成后即可启动服务")
    else:
        print("\n✅ 环境设置完成，可以启动服务了！")


if __name__ == "__main__":
    main()