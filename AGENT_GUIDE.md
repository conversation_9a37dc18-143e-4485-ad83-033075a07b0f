# 🤖 TikTok AI Agent 角色和提示词指南

## 📁 文件结构

```
src/agents/
├── roles/                    # Agent角色定义
│   ├── __init__.py
│   ├── router_role.py       # 路由Agent角色配置
│   ├── display_role.py      # 展示Agent角色配置
│   └── vanna_role.py        # Vanna核心角色配置
├── prompts/                 # 提示词管理
│   ├── __init__.py
│   ├── base_prompts.py      # 基础提示词模板
│   ├── router_prompts.py    # 路由Agent提示词
│   ├── display_prompts.py   # 展示Agent提示词
│   └── vanna_prompts.py     # Vanna核心提示词
├── config/                  # 配置文件目录
│   ├── router_config.json   # 路由Agent配置
│   ├── display_config.json  # 展示Agent配置
│   └── vanna_config.json    # Vanna核心配置
├── config_manager.py        # 配置管理器
├── router_agent.py          # 路由Agent实现
├── display_agent.py         # 展示Agent实现
└── agent_coordinator.py     # Agent协调器
```

## 🎭 Agent角色定义

### 1. 路由Agent (Router Agent)
**文件位置:** `src/agents/roles/router_role.py`

**角色描述:** 负责用户交互入口、意图识别和任务分发的智能助手

**核心能力:**
- 自然语言理解和意图识别
- 基于千问模型的Function Calling
- 任务路由和分发
- 会话状态管理
- 用户交互优化
- 多轮对话支持
- 上下文理解和维护
- 错误处理和恢复

**工作流程:**
1. 接收用户输入
2. 预处理和清理输入文本
3. 调用千问模型进行意图识别
4. 提取关键信息和实体
5. 根据意图类型路由到相应处理器
6. 更新会话状态
7. 返回处理结果或转发请求

### 2. 展示Agent (Display Agent)
**文件位置:** `src/agents/roles/display_role.py`

**角色描述:** 负责数据分析、洞察生成和可视化展示的数据分析专家

**核心能力:**
- 数据分析和统计计算
- 业务洞察生成
- 趋势识别和模式发现
- 数据可视化设计
- 图表代码生成
- 报告撰写和格式化
- 多维度数据解读
- 异常检测和解释

**工作流程:**
1. 接收查询结果数据
2. 数据质量检查和预处理
3. 统计分析和指标计算
4. 模式识别和趋势分析
5. 业务洞察生成
6. 可视化方案设计
7. 图表代码生成
8. 分析报告整合
9. 结果格式化和输出

### 3. Vanna核心 (Vanna Core)
**文件位置:** `src/agents/roles/vanna_role.py`

**角色描述:** 负责Text-to-SQL转换和数据查询执行的专业数据库专家

**核心能力:**
- 自然语言到SQL的智能转换
- 基于RAG的上下文检索
- SQL语法验证和优化
- 安全的数据库查询执行
- 查询结果格式化
- 错误检测和自动修复
- 性能优化建议
- 多数据库方言支持

**工作流程:**
1. 接收自然语言查询请求
2. 问题预处理和关键词提取
3. RAG检索相关Schema和范例
4. 构建上下文增强的提示词
5. 调用千问模型生成SQL
6. SQL语法验证和安全检查
7. 执行数据库查询
8. 结果后处理和格式化
9. 返回结构化查询结果

## 📝 提示词管理

### 1. 基础提示词 (Base Prompts)
**文件位置:** `src/agents/prompts/base_prompts.py`

**包含内容:**
- 系统角色定义
- 错误处理提示词
- 数据隐私原则
- 通用格式化方法

### 2. 路由Agent提示词
**文件位置:** `src/agents/prompts/router_prompts.py`

**主要提示词:**
- **意图识别提示词** - 分析用户输入并分类意图
- **Function Calling定义** - 千问模型函数调用配置
- **闲聊回复提示词** - 处理日常对话
- **帮助信息提示词** - 提供系统使用指导

**使用示例:**
```python
from src.agents.prompts.router_prompts import RouterPrompts

# 获取意图识别提示词
prompt = RouterPrompts.get_intent_prompt("今天涨粉最多的达人是谁？")

# 获取Function Calling配置
functions = RouterPrompts.get_function_definitions()
```

### 3. Vanna核心提示词
**文件位置:** `src/agents/prompts/vanna_prompts.py`

**主要提示词:**
- **SQL生成系统提示词** - 指导SQL生成的系统角色
- **SQL生成用户提示词** - 具体的SQL生成请求
- **SQL优化提示词** - SQL性能优化指导
- **错误修复提示词** - SQL错误诊断和修复
- **结果解释提示词** - 查询结果的业务解释

**使用示例:**
```python
from src.agents.prompts.vanna_prompts import VannaPrompts

# 获取SQL生成提示词
prompts = VannaPrompts.get_sql_generation_prompt(
    question="过去一周游戏达人播放量排行",
    schema_info="CREATE TABLE creators...",
    business_rules="涨粉量 = 当前粉丝数 - 前一天粉丝数"
)
```

### 4. 展示Agent提示词
**文件位置:** `src/agents/prompts/display_prompts.py`

**主要提示词:**
- **数据分析系统提示词** - 数据分析师角色定义
- **数据分析用户提示词** - 具体的分析请求
- **可视化生成提示词** - 图表生成指导
- **报告生成提示词** - 完整报告撰写
- **趋势分析提示词** - 时间序列分析

**使用示例:**
```python
from src.agents.prompts.display_prompts import DisplayPrompts

# 获取数据分析提示词
prompts = DisplayPrompts.get_data_analysis_prompt(
    original_question="今天涨粉最多的达人",
    sql="SELECT * FROM creators...",
    query_results=[{"username": "gamer1", "growth": 1000}]
)
```

## ⚙️ 配置管理

### 配置管理器
**文件位置:** `src/agents/config_manager.py`

**主要功能:**
- 统一管理所有Agent配置
- 导出/导入配置文件
- 配置验证和检查
- 系统概览生成

**使用方法:**
```python
from src.agents.config_manager import config_manager

# 获取Agent配置
router_config = config_manager.get_agent_config("router")

# 获取提示词管理器
router_prompts = config_manager.get_prompt_manager("router")

# 导出所有配置
config_manager.export_all_configs("json")
```

### 配置文件
**位置:** `src/agents/config/`

生成的配置文件包含：
- **router_config.json** - 路由Agent完整配置
- **display_config.json** - 展示Agent完整配置  
- **vanna_config.json** - Vanna核心完整配置

## 🔧 如何修改Agent角色和提示词

### 1. 修改Agent角色
编辑对应的角色文件：
```python
# src/agents/roles/router_role.py
@dataclass
class RouterRole:
    name: str = "新的角色名称"
    description: str = "新的角色描述"
    capabilities: List[str] = None  # 在__post_init__中定义
```

### 2. 修改提示词
编辑对应的提示词文件：
```python
# src/agents/prompts/router_prompts.py
class RouterPrompts:
    INTENT_CLASSIFICATION = """
    你的新提示词内容...
    """
```

### 3. 重新生成配置
```bash
python src/agents/config_manager.py
```

## 🧪 测试和验证

### 测试配置管理器
```bash
python src/agents/config_manager.py
```

### 验证Agent配置
```python
from src.agents.config_manager import config_manager

# 验证特定Agent配置
validation = config_manager.validate_config("router")
print(validation)
```

### 查看系统概览
```python
overview = config_manager.get_system_overview()
print(overview)
```

## 📚 最佳实践

1. **提示词设计原则:**
   - 明确具体，避免模糊表达
   - 包含充分的上下文信息
   - 提供清晰的输出格式要求
   - 考虑边界情况和错误处理

2. **角色配置原则:**
   - 职责单一，避免功能重叠
   - 能力清晰，便于理解和维护
   - 工作流程明确，步骤可追踪
   - 性能指标可量化，便于评估

3. **配置管理原则:**
   - 版本控制，记录变更历史
   - 环境隔离，区分开发和生产
   - 定期备份，防止配置丢失
   - 文档同步，保持文档更新

这个指南涵盖了TikTok AI Agent系统中所有Agent角色和提示词的存放位置、使用方法和修改指南。你可以根据需要调整和扩展这些配置。