# TikTok AI Agent

基于Vanna和千问模型的TikTok数据分析AI Agent系统，通过自然语言查询实现智能数据分析和可视化。

## 🌟 功能特点

- **🤖 智能对话**: 支持自然语言查询，无需编写SQL
- **📊 数据分析**: 自动生成数据洞察和分析报告
- **📈 可视化**: 智能生成图表和可视化展示
- **🔄 多Agent协作**: 路由Agent、Vanna核心、展示Agent协同工作
- **⚡ 高性能**: 支持缓存、并发处理和异步操作
- **🐳 容器化**: 完整的Docker部署方案
- **📱 双界面**: FastAPI + Streamlit双模式界面

## 🚀 快速开始

### 方式一：Docker部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd tiktok-ai-agent
```

2. **配置环境**
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，设置千问API密钥
nano .env
```

3. **启动服务**
```bash
# 使用部署脚本（推荐）
./scripts/deploy.sh start

# 或直接使用docker-compose
docker-compose up -d
```

4. **访问服务**
- 🌐 Web界面: http://localhost:8501
- 🔗 API接口: http://localhost:8000
- 📚 API文档: http://localhost:8000/docs

### 方式二：本地开发

1. **安装依赖**
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

2. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要配置
```

3. **初始化知识库**
```bash
# 创建示例训练数据
python -m src.core.knowledge_trainer --create-sample data/sample_training_data.json

# 训练知识库
python -m src.core.knowledge_trainer --input data/sample_training_data.json --db-path data/knowledge_base.db --verbose
```

4. **启动服务**
```bash
# 启动API服务
python -m uvicorn src.api.main:app --reload --host 0.0.0.0 --port 8000

# 启动UI界面（新终端）
streamlit run src/ui/streamlit_app.py --server.port 8501
```

## 📁 项目结构

```
tiktok-ai-agent/
├── src/                           # 源代码目录
│   ├── agents/                    # Agent实现
│   │   ├── router_agent.py        # 路由Agent
│   │   ├── display_agent.py       # 展示Agent
│   │   └── agent_coordinator.py   # Agent协调器
│   ├── api/                       # FastAPI接口层
│   │   ├── main.py               # API主应用
│   │   └── routers/              # API路由
│   ├── core/                      # 核心功能模块
│   │   ├── vanna_core.py         # Vanna核心引擎
│   │   ├── knowledge_base.py     # 知识库管理
│   │   ├── qwen_embedding.py     # 千问嵌入服务
│   │   ├── qwen_text_generator.py # 千问文本生成
│   │   ├── database_executor.py  # 数据库执行器
│   │   ├── error_handler.py      # 错误处理
│   │   ├── monitoring.py         # 监控系统
│   │   └── config.py             # 配置管理
│   ├── models/                    # 数据模型
│   │   ├── base.py               # 基础模型
│   │   ├── knowledge.py          # 知识库模型
│   │   └── query.py              # 查询模型
│   └── ui/                        # 用户界面
│       └── streamlit_app.py      # Streamlit应用
├── data/                          # 数据文件
├── tests/                         # 测试文件
├── scripts/                       # 脚本文件
│   ├── deploy.sh                 # 部署脚本
│   └── train_knowledge_base.py   # 知识库训练脚本
├── docker-compose.yml            # Docker编排文件
├── Dockerfile                    # Docker镜像文件
└── README.md                     # 项目文档
```

## ⚙️ 配置说明

### 环境变量配置

在 `.env` 文件中设置以下配置：

```env
# 应用配置
APP_NAME=TikTok AI Agent
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=info

# 千问API配置（必需）
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo
QWEN_TIMEOUT=30
QWEN_MAX_RETRIES=3

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tiktok_data
DB_USER=postgres
DB_PASSWORD=your_password
DB_POOL_SIZE=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Vanna配置
VANNA_MODEL=qwen
VANNA_DB_TYPE=postgres
VANNA_EMBEDDING_DIM=1536
VANNA_MAX_CONTEXT=4000
VANNA_SIMILARITY_THRESHOLD=0.7
```

### 千问API密钥获取

1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册并创建应用
3. 获取API密钥
4. 在 `.env` 文件中设置 `QWEN_API_KEY`

## 📖 使用指南

### Web界面使用

1. **访问界面**: 打开 http://localhost:8501
2. **输入问题**: 在文本框中输入自然语言问题
3. **查看结果**: 系统会返回SQL查询、数据结果、分析和可视化

**示例查询**:
- "今天涨粉最多的前10个达人是谁？"
- "过去一周哪个游戏达人播放量最高？"
- "美妆类视频的平均点赞率是多少？"
- "各个分类达人的平均粉丝数和视频数是多少？"

### API接口使用

#### 基础查询

```python
import requests

# 发送查询请求
response = requests.post("http://localhost:8000/api/v1/query", json={
    "question": "今天涨粉最多的前10个达人是谁？",
    "user_id": "test_user",
    "session_id": "session_123"
})

result = response.json()
print(result)
```

#### 批量查询

```python
queries = [
    {"question": "今天涨粉最多的达人是谁？"},
    {"question": "美妆类视频的平均互动率？"},
    {"question": "游戏类达人的总数？"}
]

response = requests.post("http://localhost:8000/api/v1/query/batch", json=queries)
results = response.json()
```

#### 健康检查

```python
response = requests.get("http://localhost:8000/api/v1/health")
health_status = response.json()
```

### 知识库管理

#### 训练知识库

```bash
# 从JSON文件训练
python -m src.core.knowledge_trainer --input data/training_data.json --generate-embeddings

# 从目录批量训练
python -m src.core.knowledge_trainer --input data/training_files/ --generate-embeddings

# 重建知识库
python -m src.core.knowledge_trainer --input data/training_data.json --rebuild --generate-embeddings
```

#### 添加自定义数据

创建训练数据文件 `custom_data.json`:

```json
{
  "schemas": [
    {
      "table_name": "custom_table",
      "ddl": "CREATE TABLE custom_table (id INT, name VARCHAR(100))",
      "description": "自定义表描述"
    }
  ],
  "documents": [
    {
      "title": "业务规则",
      "content": "具体的业务规则描述",
      "category": "business_rules"
    }
  ],
  "sql_examples": [
    {
      "question": "如何查询自定义数据？",
      "sql": "SELECT * FROM custom_table",
      "explanation": "查询自定义表的所有数据",
      "difficulty": "easy"
    }
  ]
}
```

然后训练知识库：
```bash
python -m src.core.knowledge_trainer --input custom_data.json --generate-embeddings
```

## 🔧 开发指南

### 添加新的Agent

1. **创建Agent类**
```python
# src/agents/my_agent.py
class MyAgent:
    def __init__(self):
        self.name = "MyAgent"
    
    async def process(self, input_data):
        # 处理逻辑
        return result
```

2. **注册到协调器**
```python
# 在 agent_coordinator.py 中添加
self.my_agent = MyAgent()
```

### 扩展数据模型

```python
# src/models/my_model.py
from pydantic import BaseModel
from .base import BaseEntity

class MyModel(BaseEntity):
    name: str
    value: int
    description: Optional[str] = None
```

### 添加API端点

```python
# src/api/routers/my_router.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/my-endpoint")
async def my_endpoint():
    return {"message": "Hello World"}
```

### 自定义错误处理

```python
from src.core.error_handler import handle_error, retry_on_error

try:
    result = await some_operation()
except Exception as e:
    error_info = handle_error(e, {"context": "my_operation"})
    # 处理错误
```

## 🐳 部署指南

### Docker部署

#### 单容器部署

```bash
# 构建镜像
docker build -t tiktok-ai-agent .

# 运行容器
docker run -d \
  --name tiktok-ai-agent \
  -p 8000:8000 \
  -p 8501:8501 \
  -e QWEN_API_KEY=your_api_key \
  -v $(pwd)/data:/app/data \
  tiktok-ai-agent
```

#### Docker Compose部署

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f tiktok-ai-agent

# 停止服务
docker-compose down
```

#### 生产环境部署

```bash
# 使用生产配置启动
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# 启用Nginx反向代理
docker-compose --profile production up -d
```

### 部署脚本使用

```bash
# 启动服务（双模式）
./scripts/deploy.sh start dual

# 启动API服务
./scripts/deploy.sh start api

# 启动UI服务
./scripts/deploy.sh start ui

# 查看日志
./scripts/deploy.sh logs

# 健康检查
./scripts/deploy.sh health

# 备份数据
./scripts/deploy.sh backup

# 更新服务
./scripts/deploy.sh update

# 清理环境
./scripts/deploy.sh cleanup
```

## 🔍 监控和维护

### 健康检查

```bash
# API健康检查
curl http://localhost:8000/api/v1/health

# 系统指标
curl http://localhost:8000/api/v1/metrics

# 管理员状态
curl http://localhost:8000/api/v1/admin/status
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs -f tiktok-ai-agent

# 查看数据库日志
docker-compose logs -f postgres

# 查看Redis日志
docker-compose logs -f redis
```

### 性能优化

1. **调整工作进程数**
```env
API_WORKERS=4  # 根据CPU核心数调整
```

2. **配置缓存**
```env
REDIS_HOST=redis
REDIS_PORT=6379
```

3. **数据库连接池**
```env
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
```

## 🐛 故障排除

### 常见问题

#### 1. API密钥错误
```
错误: Invalid API-key provided
解决: 检查 QWEN_API_KEY 是否正确设置
```

#### 2. 数据库连接失败
```
错误: connection to server failed
解决: 
- 检查数据库服务是否启动
- 验证数据库连接参数
- 确认网络连接正常
```

#### 3. 知识库为空
```
错误: No items with embeddings found
解决:
- 运行知识库训练: python -m src.core.knowledge_trainer --input data/sample_training_data.json --generate-embeddings
- 检查训练数据格式
```

#### 4. 内存不足
```
错误: Out of memory
解决:
- 增加Docker内存限制
- 减少并发工作进程数
- 优化查询复杂度
```

#### 5. 端口冲突
```
错误: Port already in use
解决:
- 修改 docker-compose.yml 中的端口映射
- 或停止占用端口的其他服务
```

### 调试模式

```bash
# 启用调试模式
export DEBUG=true
export LOG_LEVEL=debug

# 重启服务
docker-compose restart tiktok-ai-agent
```

### 性能分析

```python
# 查看系统指标
import requests
response = requests.get("http://localhost:8000/api/v1/admin/status")
print(response.json())
```

## 🤝 贡献指南

1. **Fork项目**
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **提交更改**: `git commit -m 'Add amazing feature'`
4. **推送分支**: `git push origin feature/amazing-feature`
5. **发起Pull Request**

### 开发规范

- 遵循PEP 8代码规范
- 添加适当的类型注解
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Vanna](https://github.com/vanna-ai/vanna) - SQL生成框架
- [千问](https://tongyi.aliyun.com/) - 阿里云大语言模型
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [Streamlit](https://streamlit.io/) - 数据应用框架

## 📞 支持

如有问题或建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 参与 [讨论](https://github.com/your-repo/discussions)

---

**⭐ 如果这个项目对你有帮助，请给个星标支持！**