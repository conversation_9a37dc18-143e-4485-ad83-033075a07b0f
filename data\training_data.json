{"schemas": [{"table_name": "at_tiktok_author_pool", "ddl": "CREATE TABLE `at_tiktok_author_pool` (`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',`author_id` varchar(64) NOT NULL COMMENT '发布账号ID',`unique_id` varchar(128) NOT NULL COMMENT '用户名',`author_name` varchar(255) DEFAULT NULL COMMENT '作者昵称',`author_avatar` varchar(512) DEFAULT NULL COMMENT '作者头像URL',`sec_uid` varchar(255) DEFAULT NULL COMMENT '作者账号加密ID(secUid)',`author_url` varchar(512) DEFAULT NULL COMMENT '发布者主页地址',`desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '个人简介',`register_time` int DEFAULT NULL COMMENT '账号创建时间戳',`is_verified` tinyint(1) DEFAULT '0' COMMENT '是否认证(1:是,0:否)',`region` varchar(32) DEFAULT NULL COMMENT '用户注册地区(如US)',`language` varchar(32) DEFAULT NULL COMMENT '用户界面语言(如en)',`private_account` tinyint(1) DEFAULT '0' COMMENT '是否私密账号(1:是,0:否)',`commerce_user` tinyint(1) DEFAULT '0' COMMENT '是否开通电商功能(1:是,0:否)',`tt_seller` tinyint(1) DEFAULT '0' COMMENT '是否TikTok小店卖家(1:是,0:否)',`commerce_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开通电商的类目',`follower_count` int DEFAULT '0' COMMENT '粉丝数',`following_count` int DEFAULT '0' COMMENT '关注数',`heart_count` int DEFAULT '0' COMMENT '总获赞数',`video_count` int DEFAULT '0' COMMENT '发布视频数',`friend_count` int DEFAULT '0' COMMENT '好友数',`update_status` int DEFAULT NULL COMMENT '更新状态',`creator` bigint DEFAULT NULL COMMENT '创建者',`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',`updater` bigint DEFAULT NULL COMMENT '数据更新人',`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',`is_del` tinyint DEFAULT '0' COMMENT '是否删除（0未删除，1已删除）',`status` tinyint DEFAULT NULL COMMENT 'status，0=未监控；1=监控中',PRIMARY KEY (`id`),UNIQUE KEY `uk_author_id` (`author_id`),UNIQUE KEY `idx_unique_id` (`unique_id`) USING BTREE) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TikTok达人信息表'", "description": "TikTok达人信息表，包含达人的基本信息、粉丝数据、认证状态等核心字段"}, {"table_name": "at_tiktok_author_work_record", "ddl": "CREATE TABLE `at_tiktok_author_work_record` (`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',`work_id` varchar(64) NOT NULL COMMENT '作品唯一标识',`work_uuid` varchar(64) DEFAULT NULL COMMENT '三方生成唯一ID',`author_id` varchar(64) NOT NULL COMMENT '发布账号ID',`unique_id` varchar(128) NOT NULL COMMENT '作者唯一用户名',`sec_uid` varchar(255) DEFAULT NULL COMMENT '作者账号加密ID',`url` varchar(2048) DEFAULT NULL COMMENT '作品链接',`category_type` int DEFAULT NULL COMMENT '分类类型(对应TikTok分类体系)',`thumbnail_link` varchar(2048) DEFAULT NULL COMMENT '封面图链接',`is_ad` tinyint DEFAULT '0' COMMENT '是否广告(1:是,0:否)',`title` text COMMENT '作品标题',`content` text COMMENT '作品内容描述',`hashtags` varchar(1024) DEFAULT NULL COMMENT '话题标签数组，格式: [\"#tag1\",\"#tag2\"]',`images` text COMMENT '图片URL数组，格式: [\"url1\",\"url2\"]',`publish_time` int DEFAULT NULL COMMENT '发布时间戳',`text_language` varchar(10) DEFAULT 'en' COMMENT '文本语言(ISO 639-1)',`location_ip` varchar(100) DEFAULT '' COMMENT '发布地理位置',`play_count` int DEFAULT '0' COMMENT '播放量',`like_count` int DEFAULT '0' COMMENT '点赞数',`comment_count` int DEFAULT '0' COMMENT '评论数',`share_count` int DEFAULT '0' COMMENT '转发数',`collect_count` int DEFAULT '0' COMMENT '收藏数',`video_id` varchar(128) DEFAULT NULL COMMENT '视频ID',`music_id` varchar(128) DEFAULT NULL COMMENT '音乐ID',`product_id` varchar(128) DEFAULT NULL COMMENT '商品ID',`creator` bigint DEFAULT NULL COMMENT '创建者',`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',`updater` bigint DEFAULT NULL COMMENT '数据更新人',`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',PRIMARY KEY (`id`),UNIQUE KEY `uk_work_id` (`work_id`),KEY `idx_author_id` (`author_id`),KEY `idx_publish_time` (`publish_time`)) ENGINE=InnoDB AUTO_INCREMENT=376 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TikTok作品主表(JSON格式)'", "description": "TikTok作品记录表，包含作品的播放数据、互动数据、发布信息等详细字段"}], "documents": [{"title": "TikTok数据表关系说明", "content": "at_tiktok_author_pool表存储达人基本信息，at_tiktok_author_work_record表存储作品数据。两表通过author_id字段关联。达人表包含粉丝数、获赞数等汇总数据，作品表包含单个作品的播放量、点赞数等详细数据。", "category": "table_relationships"}, {"title": "粉丝增长计算方式", "content": "粉丝增长 = 当前follower_count - 历史follower_count。由于表中只有当前粉丝数，需要通过时间序列数据计算增长。可以按日期分组统计粉丝数变化。", "category": "business_rules"}, {"title": "互动率计算公式", "content": "互动率 = (like_count + comment_count + share_count + collect_count) / play_count * 100%。这是衡量作品质量的重要指标，反映用户对内容的参与度。", "category": "business_rules"}, {"title": "点赞率计算公式", "content": "点赞率 = like_count / play_count * 100%。点赞率是最直观的内容质量指标，通常优质内容的点赞率在3-10%之间。", "category": "business_rules"}, {"title": "达人分类说明", "content": "达人可以通过content字段的内容、hashtags标签、commerce_category电商类目等进行分类。常见分类包括：美妆、游戏、舞蹈、音乐、美食、教育、时尚等。", "category": "category_info"}, {"title": "时间字段处理", "content": "publish_time和register_time是Unix时间戳格式，需要使用FROM_UNIXTIME()函数转换。create_time和update_time是datetime格式，可直接使用。查询'今天'用DATE(FROM_UNIXTIME(publish_time)) = CURDATE()。", "category": "technical_info"}], "sql_examples": [{"question": "粉丝数最多的前10个达人是谁？", "sql": "SELECT unique_id, author_name, follower_count, heart_count, video_count FROM at_tiktok_author_pool WHERE is_del = 0 ORDER BY follower_count DESC LIMIT 10", "explanation": "查询未删除的达人，按粉丝数降序排列，取前10名", "difficulty": "easy"}, {"question": "今天发布的作品中播放量最高的是哪个？", "sql": "SELECT w.work_id, w.title, w.play_count, w.like_count, a.unique_id, a.author_name FROM at_tiktok_author_work_record w LEFT JOIN at_tiktok_author_pool a ON w.author_id = a.author_id WHERE DATE(FROM_UNIXTIME(w.publish_time)) = CURDATE() ORDER BY w.play_count DESC LIMIT 1", "explanation": "关联达人表和作品表，筛选今天发布的作品，按播放量降序取第一名", "difficulty": "medium"}, {"question": "过去7天内发布作品最多的达人排行榜", "sql": "SELECT a.unique_id, a.author_name, COUNT(w.work_id) as work_count, AVG(w.play_count) as avg_play_count FROM at_tiktok_author_pool a LEFT JOIN at_tiktok_author_work_record w ON a.author_id = w.author_id WHERE w.publish_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY)) GROUP BY a.author_id, a.unique_id, a.author_name ORDER BY work_count DESC LIMIT 20", "explanation": "统计过去7天每个达人的发布作品数量和平均播放量，按作品数量排序", "difficulty": "medium"}, {"question": "互动率最高的100个作品", "sql": "SELECT w.work_id, w.title, a.unique_id, a.author_name, w.play_count, w.like_count, w.comment_count, w.share_count, w.collect_count, ROUND((w.like_count + w.comment_count + w.share_count + w.collect_count) / NULLIF(w.play_count, 0) * 100, 2) as engagement_rate FROM at_tiktok_author_work_record w LEFT JOIN at_tiktok_author_pool a ON w.author_id = a.author_id WHERE w.play_count > 0 ORDER BY engagement_rate DESC LIMIT 100", "explanation": "计算每个作品的互动率，使用NULLIF避免除零错误，按互动率降序排列", "difficulty": "hard"}, {"question": "认证达人和非认证达人的平均数据对比", "sql": "SELECT is_verified, COUNT(*) as author_count, AVG(follower_count) as avg_followers, AVG(heart_count) as avg_hearts, AVG(video_count) as avg_videos FROM at_tiktok_author_pool WHERE is_del = 0 GROUP BY is_verified", "explanation": "按认证状态分组统计达人数量和各项平均数据", "difficulty": "easy"}, {"question": "开通电商功能的达人中，哪个类目的达人最多？", "sql": "SELECT commerce_category, COUNT(*) as author_count, AVG(follower_count) as avg_followers FROM at_tiktok_author_pool WHERE commerce_user = 1 AND commerce_category IS NOT NULL AND is_del = 0 GROUP BY commerce_category ORDER BY author_count DESC", "explanation": "筛选开通电商功能的达人，按电商类目分组统计数量和平均粉丝数", "difficulty": "medium"}, {"question": "本月播放量超过100万的作品有多少个？", "sql": "SELECT COUNT(*) as high_play_works, AVG(play_count) as avg_play_count FROM at_tiktok_author_work_record WHERE play_count > 1000000 AND FROM_UNIXTIME(publish_time) >= DATE_FORMAT(NOW(), '%Y-%m-01')", "explanation": "统计本月播放量超过100万的作品数量和平均播放量", "difficulty": "medium"}, {"question": "点赞率最高的达人TOP20（要求至少发布5个作品）", "sql": "SELECT a.unique_id, a.author_name, COUNT(w.work_id) as work_count, SUM(w.like_count) as total_likes, SUM(w.play_count) as total_plays, ROUND(SUM(w.like_count) / NULLIF(SUM(w.play_count), 0) * 100, 2) as like_rate FROM at_tiktok_author_pool a LEFT JOIN at_tiktok_author_work_record w ON a.author_id = w.author_id WHERE a.is_del = 0 GROUP BY a.author_id, a.unique_id, a.author_name HAVING work_count >= 5 AND total_plays > 0 ORDER BY like_rate DESC LIMIT 20", "explanation": "计算每个达人的总点赞率，要求至少发布5个作品，按点赞率排序", "difficulty": "hard"}]}