"""
错误处理器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
import logging

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.error_handler import (
    ErrorHandler, ErrorType, ErrorSeverity, ErrorContext, 
    RetryConfig, CircuitBreaker, ErrorMetrics
)


class TestErrorContext:
    """错误上下文测试"""
    
    def test_error_context_creation(self):
        """测试错误上下文创建"""
        context = ErrorContext(
            component="test_component",
            operation="test_operation",
            user_id="test_user",
            session_id="test_session",
            additional_info={"key": "value"}
        )
        
        assert context.component == "test_component"
        assert context.operation == "test_operation"
        assert context.user_id == "test_user"
        assert context.session_id == "test_session"
        assert context.additional_info == {"key": "value"}
        assert isinstance(context.timestamp, datetime)
    
    def test_error_context_to_dict(self):
        """测试错误上下文转字典"""
        context = ErrorContext(
            component="test_component",
            operation="test_operation"
        )
        
        context_dict = context.to_dict()
        
        assert context_dict["component"] == "test_component"
        assert context_dict["operation"] == "test_operation"
        assert "timestamp" in context_dict


class TestRetryConfig:
    """重试配置测试"""
    
    def test_retry_config_creation(self):
        """测试重试配置创建"""
        config = RetryConfig(
            max_attempts=5,
            base_delay=2.0,
            max_delay=60.0,
            backoff_multiplier=2.5
        )
        
        assert config.max_attempts == 5
        assert config.base_delay == 2.0
        assert config.max_delay == 60.0
        assert config.backoff_multiplier == 2.5
    
    def test_retry_config_defaults(self):
        """测试重试配置默认值"""
        config = RetryConfig()
        
        assert config.max_attempts == 3
        assert config.base_delay == 1.0
        assert config.max_delay == 30.0
        assert config.backoff_multiplier == 2.0
    
    def test_calculate_delay(self):
        """测试计算延迟时间"""
        config = RetryConfig(base_delay=1.0, backoff_multiplier=2.0, max_delay=10.0)
        
        # 第一次重试
        delay1 = config.calculate_delay(1)
        assert delay1 == 1.0
        
        # 第二次重试
        delay2 = config.calculate_delay(2)
        assert delay2 == 2.0
        
        # 第三次重试
        delay3 = config.calculate_delay(3)
        assert delay3 == 4.0
        
        # 超过最大延迟
        delay_max = config.calculate_delay(10)
        assert delay_max == 10.0


class TestCircuitBreaker:
    """熔断器测试"""
    
    def test_circuit_breaker_creation(self):
        """测试熔断器创建"""
        breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60.0,
            success_threshold=3
        )
        
        assert breaker.failure_threshold == 5
        assert breaker.recovery_timeout == 60.0
        assert breaker.success_threshold == 3
        assert breaker.failure_count == 0
        assert breaker.success_count == 0
        assert breaker.state == "closed"
        assert breaker.last_failure_time is None
    
    def test_circuit_breaker_record_success(self):
        """测试记录成功"""
        breaker = CircuitBreaker()
        
        breaker.record_success()
        
        assert breaker.failure_count == 0
        assert breaker.success_count == 1
    
    def test_circuit_breaker_record_failure(self):
        """测试记录失败"""
        breaker = CircuitBreaker()
        
        breaker.record_failure()
        
        assert breaker.failure_count == 1
        assert breaker.success_count == 0
        assert isinstance(breaker.last_failure_time, datetime)
    
    def test_circuit_breaker_open_on_threshold(self):
        """测试达到阈值时熔断器打开"""
        breaker = CircuitBreaker(failure_threshold=3)
        
        # 记录失败次数达到阈值
        for _ in range(3):
            breaker.record_failure()
        
        assert breaker.state == "open"
        assert not breaker.can_execute()
    
    def test_circuit_breaker_half_open_after_timeout(self):
        """测试超时后熔断器半开"""
        breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=0.1)
        
        # 触发熔断
        breaker.record_failure()
        breaker.record_failure()
        assert breaker.state == "open"
        
        # 等待恢复超时
        import time
        time.sleep(0.2)
        
        # 检查是否可以执行（应该进入半开状态）
        can_execute = breaker.can_execute()
        assert can_execute is True
        assert breaker.state == "half_open"
    
    def test_circuit_breaker_close_after_success(self):
        """测试成功后熔断器关闭"""
        breaker = CircuitBreaker(failure_threshold=2, success_threshold=2)
        
        # 触发熔断
        breaker.record_failure()
        breaker.record_failure()
        assert breaker.state == "open"
        
        # 进入半开状态
        breaker.state = "half_open"
        
        # 记录成功次数达到阈值
        breaker.record_success()
        breaker.record_success()
        
        assert breaker.state == "closed"
        assert breaker.failure_count == 0


class TestErrorMetrics:
    """错误指标测试"""
    
    def test_error_metrics_creation(self):
        """测试错误指标创建"""
        metrics = ErrorMetrics()
        
        assert metrics.total_errors == 0
        assert metrics.error_by_type == {}
        assert metrics.error_by_component == {}
        assert metrics.error_by_severity == {}
    
    def test_record_error(self):
        """测试记录错误"""
        metrics = ErrorMetrics()
        
        metrics.record_error(
            error_type=ErrorType.DATABASE_ERROR,
            component="vanna_core",
            severity=ErrorSeverity.HIGH
        )
        
        assert metrics.total_errors == 1
        assert metrics.error_by_type[ErrorType.DATABASE_ERROR] == 1
        assert metrics.error_by_component["vanna_core"] == 1
        assert metrics.error_by_severity[ErrorSeverity.HIGH] == 1
    
    def test_record_multiple_errors(self):
        """测试记录多个错误"""
        metrics = ErrorMetrics()
        
        # 记录不同类型的错误
        metrics.record_error(ErrorType.API_ERROR, "router_agent", ErrorSeverity.MEDIUM)
        metrics.record_error(ErrorType.API_ERROR, "router_agent", ErrorSeverity.HIGH)
        metrics.record_error(ErrorType.DATABASE_ERROR, "vanna_core", ErrorSeverity.HIGH)
        
        assert metrics.total_errors == 3
        assert metrics.error_by_type[ErrorType.API_ERROR] == 2
        assert metrics.error_by_type[ErrorType.DATABASE_ERROR] == 1
        assert metrics.error_by_component["router_agent"] == 2
        assert metrics.error_by_component["vanna_core"] == 1
        assert metrics.error_by_severity[ErrorSeverity.HIGH] == 2
        assert metrics.error_by_severity[ErrorSeverity.MEDIUM] == 1
    
    def test_get_error_rate(self):
        """测试获取错误率"""
        metrics = ErrorMetrics()
        
        # 记录一些错误和成功
        for _ in range(10):
            metrics.record_error(ErrorType.API_ERROR, "test", ErrorSeverity.LOW)
        
        for _ in range(90):
            metrics.record_success()
        
        error_rate = metrics.get_error_rate()
        assert error_rate == 0.1  # 10/(10+90) = 0.1
    
    def test_get_error_rate_no_requests(self):
        """测试无请求时的错误率"""
        metrics = ErrorMetrics()
        
        error_rate = metrics.get_error_rate()
        assert error_rate == 0.0
    
    def test_get_stats(self):
        """测试获取统计信息"""
        metrics = ErrorMetrics()
        
        metrics.record_error(ErrorType.API_ERROR, "test", ErrorSeverity.HIGH)
        metrics.record_success()
        
        stats = metrics.get_stats()
        
        assert stats["total_errors"] == 1
        assert stats["total_requests"] == 2
        assert stats["error_rate"] == 0.5
        assert ErrorType.API_ERROR in stats["error_by_type"]
        assert "test" in stats["error_by_component"]
        assert ErrorSeverity.HIGH in stats["error_by_severity"]


class TestErrorHandler:
    """错误处理器测试"""
    
    @pytest.fixture
    def error_handler(self):
        """创建错误处理器实例"""
        return ErrorHandler()
    
    def test_error_handler_initialization(self, error_handler):
        """测试错误处理器初始化"""
        assert error_handler.circuit_breakers == {}
        assert error_handler.retry_configs == {}
        assert isinstance(error_handler.metrics, ErrorMetrics)
        assert error_handler.logger is not None
    
    def test_classify_error_api_error(self, error_handler):
        """测试API错误分类"""
        api_errors = [
            Exception("API rate limit exceeded"),
            Exception("API timeout"),
            Exception("Invalid API key"),
            ConnectionError("Connection failed")
        ]
        
        for error in api_errors:
            error_type, severity = error_handler.classify_error(error)
            assert error_type == ErrorType.API_ERROR
    
    def test_classify_error_database_error(self, error_handler):
        """测试数据库错误分类"""
        db_errors = [
            Exception("Connection to database failed"),
            Exception("SQL syntax error"),
            Exception("Table does not exist"),
            Exception("Database timeout")
        ]
        
        for error in db_errors:
            error_type, severity = error_handler.classify_error(error)
            assert error_type == ErrorType.DATABASE_ERROR
    
    def test_classify_error_validation_error(self, error_handler):
        """测试验证错误分类"""
        validation_errors = [
            ValueError("Invalid input"),
            Exception("Validation failed"),
            Exception("Invalid parameter")
        ]
        
        for error in validation_errors:
            error_type, severity = error_handler.classify_error(error)
            assert error_type == ErrorType.VALIDATION_ERROR
    
    def test_classify_error_unknown(self, error_handler):
        """测试未知错误分类"""
        unknown_error = Exception("Some random error")
        
        error_type, severity = error_handler.classify_error(unknown_error)
        
        assert error_type == ErrorType.UNKNOWN_ERROR
        assert severity == ErrorSeverity.MEDIUM
    
    @pytest.mark.asyncio
    async def test_handle_error_basic(self, error_handler):
        """测试基本错误处理"""
        error = Exception("Test error")
        context = ErrorContext(component="test", operation="test_op")
        
        result = await error_handler.handle_error(error, context)
        
        assert result["success"] is False
        assert result["error_type"] == ErrorType.UNKNOWN_ERROR
        assert result["error_message"] == "Test error"
        assert result["context"] == context.to_dict()
        assert result["retry_suggested"] is False
    
    @pytest.mark.asyncio
    async def test_handle_error_with_retry(self, error_handler):
        """测试带重试的错误处理"""
        # 配置重试
        error_handler.configure_retry("test", RetryConfig(max_attempts=3))
        
        error = Exception("API timeout")
        context = ErrorContext(component="test", operation="api_call")
        
        result = await error_handler.handle_error(error, context)
        
        assert result["retry_suggested"] is True
        assert result["max_retry_attempts"] == 3
    
    @pytest.mark.asyncio
    async def test_handle_error_circuit_breaker_open(self, error_handler):
        """测试熔断器打开时的错误处理"""
        # 配置熔断器
        error_handler.configure_circuit_breaker("test", CircuitBreaker(failure_threshold=1))
        
        # 触发熔断
        error = Exception("Service unavailable")
        context = ErrorContext(component="test", operation="service_call")
        
        # 第一次调用触发熔断
        await error_handler.handle_error(error, context)
        
        # 第二次调用应该被熔断器阻止
        result = await error_handler.handle_error(error, context)
        
        assert result["circuit_breaker_open"] is True
        assert "熔断器已打开" in result["error_message"]
    
    def test_configure_retry(self, error_handler):
        """测试配置重试"""
        config = RetryConfig(max_attempts=5)
        
        error_handler.configure_retry("test_component", config)
        
        assert "test_component" in error_handler.retry_configs
        assert error_handler.retry_configs["test_component"] == config
    
    def test_configure_circuit_breaker(self, error_handler):
        """测试配置熔断器"""
        breaker = CircuitBreaker(failure_threshold=10)
        
        error_handler.configure_circuit_breaker("test_component", breaker)
        
        assert "test_component" in error_handler.circuit_breakers
        assert error_handler.circuit_breakers["test_component"] == breaker
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_success(self, error_handler):
        """测试重试执行成功"""
        # 配置重试
        error_handler.configure_retry("test", RetryConfig(max_attempts=3, base_delay=0.01))
        
        # 模拟函数：前两次失败，第三次成功
        call_count = 0
        async def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        context = ErrorContext(component="test", operation="test_function")
        result = await error_handler.execute_with_retry(test_function, context)
        
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_execute_with_retry_max_attempts(self, error_handler):
        """测试重试达到最大次数"""
        # 配置重试
        error_handler.configure_retry("test", RetryConfig(max_attempts=2, base_delay=0.01))
        
        # 模拟总是失败的函数
        async def failing_function():
            raise Exception("Always fails")
        
        context = ErrorContext(component="test", operation="failing_function")
        
        with pytest.raises(Exception, match="Always fails"):
            await error_handler.execute_with_retry(failing_function, context)
    
    @pytest.mark.asyncio
    async def test_execute_with_circuit_breaker_success(self, error_handler):
        """测试熔断器执行成功"""
        # 配置熔断器
        error_handler.configure_circuit_breaker("test", CircuitBreaker())
        
        async def test_function():
            return "success"
        
        context = ErrorContext(component="test", operation="test_function")
        result = await error_handler.execute_with_circuit_breaker(test_function, context)
        
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_execute_with_circuit_breaker_failure(self, error_handler):
        """测试熔断器执行失败"""
        # 配置熔断器
        error_handler.configure_circuit_breaker("test", CircuitBreaker(failure_threshold=1))
        
        async def failing_function():
            raise Exception("Function failed")
        
        context = ErrorContext(component="test", operation="failing_function")
        
        # 第一次调用应该失败并触发熔断
        with pytest.raises(Exception, match="Function failed"):
            await error_handler.execute_with_circuit_breaker(failing_function, context)
        
        # 第二次调用应该被熔断器阻止
        with pytest.raises(Exception, match="熔断器已打开"):
            await error_handler.execute_with_circuit_breaker(failing_function, context)
    
    def test_get_error_stats(self, error_handler):
        """测试获取错误统计"""
        # 记录一些错误
        error_handler.metrics.record_error(ErrorType.API_ERROR, "test", ErrorSeverity.HIGH)
        error_handler.metrics.record_error(ErrorType.DATABASE_ERROR, "test", ErrorSeverity.MEDIUM)
        
        stats = error_handler.get_error_stats()
        
        assert stats["total_errors"] == 2
        assert ErrorType.API_ERROR in stats["error_by_type"]
        assert ErrorType.DATABASE_ERROR in stats["error_by_type"]
    
    def test_reset_metrics(self, error_handler):
        """测试重置指标"""
        # 记录一些错误
        error_handler.metrics.record_error(ErrorType.API_ERROR, "test", ErrorSeverity.HIGH)
        
        assert error_handler.metrics.total_errors == 1
        
        error_handler.reset_metrics()
        
        assert error_handler.metrics.total_errors == 0
        assert error_handler.metrics.error_by_type == {}


class TestErrorHandlerIntegration:
    """错误处理器集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_error_handling_workflow(self):
        """测试完整错误处理工作流"""
        error_handler = ErrorHandler()
        
        # 配置重试和熔断器
        error_handler.configure_retry("api_service", RetryConfig(max_attempts=3, base_delay=0.01))
        error_handler.configure_circuit_breaker("api_service", CircuitBreaker(failure_threshold=2))
        
        # 模拟API服务：前两次失败，第三次成功
        call_count = 0
        async def api_call():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("API temporarily unavailable")
            return {"status": "success", "data": "test_data"}
        
        context = ErrorContext(
            component="api_service",
            operation="fetch_data",
            user_id="test_user"
        )
        
        # 执行带重试的API调用
        result = await error_handler.execute_with_retry(api_call, context)
        
        assert result["status"] == "success"
        assert call_count == 3
        
        # 检查指标
        stats = error_handler.get_error_stats()
        assert stats["total_errors"] == 2  # 前两次失败
        assert stats["total_requests"] == 3  # 总共3次请求（包括成功的）


if __name__ == "__main__":
    pytest.main([__file__, "-v"])