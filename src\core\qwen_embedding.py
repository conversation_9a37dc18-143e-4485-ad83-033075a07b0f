"""
千问模型嵌入服务
实现千问模型API调用封装，提供文本嵌入向量生成功能
"""

import asyncio
import json
import logging
from typing import List, Optional, Dict, Any
import httpx
import numpy as np
from datetime import datetime

from .config import get_config

logger = logging.getLogger(__name__)


class QwenEmbeddingService:
    """千问模型嵌入服务"""
    
    def __init__(self):
        """初始化千问嵌入服务"""
        self.config = get_config()
        self.qwen_config = self.config.qwen
        
        # 设置HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.qwen_config.timeout),
            headers={
                "Authorization": f"Bearer {self.qwen_config.api_key}",
                "Content-Type": "application/json"
            }
        )
        
        # 嵌入模型配置
        self.embedding_model = "text-embedding-v1"  # 千问嵌入模型
        self.max_batch_size = 25  # 批处理最大大小
        self.max_text_length = 2048  # 最大文本长度
        
        logger.info("QwenEmbeddingService initialized")
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """
        获取单个文本的嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            List[float]: 嵌入向量，失败时返回None
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for embedding")
            return None
        
        # 截断过长的文本
        if len(text) > self.max_text_length:
            text = text[:self.max_text_length]
            logger.warning(f"Text truncated to {self.max_text_length} characters")
        
        try:
            # 构建请求数据
            request_data = {
                "model": self.embedding_model,
                "input": {
                    "texts": [text]
                },
                "parameters": {
                    "text_type": "document"  # 文档类型嵌入
                }
            }
            
            # 发送请求
            response = await self._make_request(request_data)
            
            if response and "output" in response and "embeddings" in response["output"]:
                embeddings = response["output"]["embeddings"]
                if embeddings and len(embeddings) > 0:
                    return embeddings[0]["embedding"]
            
            logger.error("Invalid response format from Qwen API")
            return None
            
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            return None
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        批量获取文本嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            List[Optional[List[float]]]: 嵌入向量列表，失败的项为None
        """
        if not texts:
            return []
        
        # 过滤和预处理文本
        processed_texts = []
        for text in texts:
            if text and text.strip():
                # 截断过长的文本
                if len(text) > self.max_text_length:
                    text = text[:self.max_text_length]
                processed_texts.append(text)
            else:
                processed_texts.append("")
        
        results = []
        
        # 分批处理
        for i in range(0, len(processed_texts), self.max_batch_size):
            batch = processed_texts[i:i + self.max_batch_size]
            batch_results = await self._process_batch(batch)
            results.extend(batch_results)
        
        return results
    
    async def _process_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        处理单个批次的文本
        
        Args:
            texts: 文本批次
            
        Returns:
            List[Optional[List[float]]]: 批次结果
        """
        # 过滤空文本
        valid_texts = [text for text in texts if text.strip()]
        if not valid_texts:
            return [None] * len(texts)
        
        try:
            # 构建请求数据
            request_data = {
                "model": self.embedding_model,
                "input": {
                    "texts": valid_texts
                },
                "parameters": {
                    "text_type": "document"
                }
            }
            
            # 发送请求
            response = await self._make_request(request_data)
            
            if response and "output" in response and "embeddings" in response["output"]:
                embeddings = response["output"]["embeddings"]
                
                # 构建结果映射
                embedding_map = {}
                for i, embedding_data in enumerate(embeddings):
                    if i < len(valid_texts):
                        embedding_map[valid_texts[i]] = embedding_data["embedding"]
                
                # 按原始顺序返回结果
                results = []
                for text in texts:
                    if text.strip() and text in embedding_map:
                        results.append(embedding_map[text])
                    else:
                        results.append(None)
                
                return results
            
            logger.error("Invalid response format from Qwen API")
            return [None] * len(texts)
            
        except Exception as e:
            logger.error(f"Failed to process batch: {e}")
            return [None] * len(texts)
    
    async def _make_request(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求到千问API
        
        Args:
            data: 请求数据
            
        Returns:
            Dict[str, Any]: 响应数据，失败时返回None
        """
        url = f"{self.qwen_config.api_base}/services/embeddings/text-embedding/text-embedding"
        
        for attempt in range(self.qwen_config.max_retries):
            try:
                response = await self.client.post(url, json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 检查API响应状态 - 阿里云DashScope API的响应格式
                    if "output" in result:
                        return result
                    elif result.get("status_code") == 200:
                        return result
                    else:
                        logger.error(f"API error: {result.get('message', 'Unknown error')}")
                        logger.error(f"Response structure: {result.keys() if isinstance(result, dict) else 'invalid'}")
                        return None
                
                elif response.status_code == 429:
                    # 速率限制，等待后重试
                    wait_time = 2 ** attempt
                    logger.warning(f"Rate limited, waiting {wait_time}s before retry {attempt + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                
                else:
                    logger.error(f"HTTP error {response.status_code}: {response.text}")
                    return None
                    
            except httpx.TimeoutException:
                logger.warning(f"Request timeout, attempt {attempt + 1}/{self.qwen_config.max_retries}")
                if attempt < self.qwen_config.max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                else:
                    logger.error("Request timeout after all retries")
                    return None
                    
            except Exception as e:
                logger.error(f"Request failed: {e}")
                if attempt < self.qwen_config.max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                else:
                    return None
        
        return None
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        计算两个嵌入向量的余弦相似度
        
        Args:
            embedding1: 第一个嵌入向量
            embedding2: 第二个嵌入向量
            
        Returns:
            float: 余弦相似度 (-1 到 1)
        """
        try:
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Failed to calculate similarity: {e}")
            return 0.0
    
    def find_most_similar(self, query_embedding: List[float], 
                         candidate_embeddings: List[List[float]], 
                         top_k: int = 5) -> List[tuple]:
        """
        找到最相似的嵌入向量
        
        Args:
            query_embedding: 查询嵌入向量
            candidate_embeddings: 候选嵌入向量列表
            top_k: 返回前k个最相似的结果
            
        Returns:
            List[tuple]: (索引, 相似度分数) 的列表，按相似度降序排列
        """
        similarities = []
        
        for i, candidate in enumerate(candidate_embeddings):
            similarity = self.calculate_similarity(query_embedding, candidate)
            similarities.append((i, similarity))
        
        # 按相似度降序排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()
        logger.info("QwenEmbeddingService closed")


class EmbeddingCache:
    """嵌入向量缓存"""
    
    def __init__(self, max_size: int = 1000):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存大小
        """
        self.cache: Dict[str, List[float]] = {}
        self.access_times: Dict[str, datetime] = {}
        self.max_size = max_size
    
    def get(self, text: str) -> Optional[List[float]]:
        """获取缓存的嵌入向量"""
        if text in self.cache:
            self.access_times[text] = datetime.now()
            return self.cache[text]
        return None
    
    def put(self, text: str, embedding: List[float]) -> None:
        """存储嵌入向量到缓存"""
        # 如果缓存已满，删除最久未访问的项
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[text] = embedding
        self.access_times[text] = datetime.now()
    
    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


class CachedQwenEmbeddingService(QwenEmbeddingService):
    """带缓存的千问嵌入服务"""
    
    def __init__(self, cache_size: int = 1000):
        """
        初始化带缓存的嵌入服务
        
        Args:
            cache_size: 缓存大小
        """
        super().__init__()
        self.cache = EmbeddingCache(cache_size)
        logger.info(f"CachedQwenEmbeddingService initialized with cache size {cache_size}")
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取嵌入向量（带缓存）"""
        if not text or not text.strip():
            return None
        
        # 检查缓存
        cached_embedding = self.cache.get(text)
        if cached_embedding is not None:
            logger.debug("Cache hit for embedding")
            return cached_embedding
        
        # 缓存未命中，调用API
        embedding = await super().get_embedding(text)
        
        # 存储到缓存
        if embedding is not None:
            self.cache.put(text, embedding)
        
        return embedding
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": self.cache.size(),
            "max_size": self.cache.max_size
        }