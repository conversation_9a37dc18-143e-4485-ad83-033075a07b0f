#!/usr/bin/env python3
"""
生产版本UI验证脚本
验证生产版本UI是否能正常启动和运行
"""

import sys
import subprocess
import time
import requests
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        'streamlit',
        'pandas',
        'plotly',
        'asyncio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖已安装")
    return True


def check_config():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_files = [
        '.env',
        'config.yaml'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"  ✅ {config_file}")
        else:
            print(f"  ⚠️ {config_file} 不存在")
    
    # 检查环境变量
    try:
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        from core.config import get_config
        config = get_config()
        print("  ✅ 配置加载成功")
        
        # 检查关键配置
        if hasattr(config, 'qwen') and config.qwen.api_key:
            if config.qwen.api_key == "your_qwen_api_key_here":
                print("  ⚠️ 千问API密钥未配置")
            else:
                print("  ✅ 千问API密钥已配置")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return False


def check_ui_files():
    """检查UI文件"""
    print("\n🔍 检查UI文件...")
    
    ui_files = [
        'src/ui/streamlit_app.py',
        'src/ui/simple_streamlit_app.py',
        'start_ui.py',
        'main.py'
    ]
    
    for ui_file in ui_files:
        if Path(ui_file).exists():
            print(f"  ✅ {ui_file}")
        else:
            print(f"  ❌ {ui_file} 不存在")
            return False
    
    return True


def test_ui_import():
    """测试UI模块导入"""
    print("\n🔍 测试UI模块导入...")
    
    try:
        # 测试基本导入
        import streamlit as st
        import pandas as pd
        import plotly.express as px
        print("  ✅ 基础UI模块导入成功")
        
        # 测试项目模块导入
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        try:
            from core.config import get_config
            print("  ✅ 配置模块导入成功")
        except Exception as e:
            print(f"  ⚠️ 配置模块导入失败: {e}")
        
        try:
            from agents.agent_coordinator import AgentCoordinator
            print("  ✅ Agent模块导入成功")
        except Exception as e:
            print(f"  ⚠️ Agent模块导入失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False


def test_ui_startup():
    """测试UI启动"""
    print("\n🚀 测试UI启动...")
    
    try:
        # 启动Streamlit应用（后台模式）
        process = subprocess.Popen([
            'streamlit', 'run', 'src/ui/streamlit_app.py',
            '--server.port', '8502',  # 使用不同端口避免冲突
            '--server.headless', 'true',
            '--server.runOnSave', 'false'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待启动
        print("  ⏳ 等待应用启动...")
        time.sleep(10)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("  ✅ Streamlit应用启动成功")
            
            # 尝试访问健康检查
            try:
                response = requests.get('http://localhost:8502', timeout=5)
                if response.status_code == 200:
                    print("  ✅ UI界面可访问")
                else:
                    print(f"  ⚠️ UI界面响应异常: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"  ⚠️ 无法访问UI界面: {e}")
            
            # 停止进程
            process.terminate()
            process.wait()
            print("  ✅ 应用已停止")
            return True
        else:
            # 获取错误信息
            stdout, stderr = process.communicate()
            print(f"  ❌ 应用启动失败")
            if stderr:
                print(f"  错误信息: {stderr.decode()}")
            return False
            
    except FileNotFoundError:
        print("  ❌ Streamlit未安装")
        return False
    except Exception as e:
        print(f"  ❌ 启动测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🎵 TikTok AI Agent 生产版本UI验证")
    print("=" * 50)
    
    # 执行检查
    checks = [
        ("依赖检查", check_dependencies),
        ("配置检查", check_config),
        ("文件检查", check_ui_files),
        ("导入测试", test_ui_import),
        ("启动测试", test_ui_startup)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            results.append((check_name, False))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 生产版本UI验证完全通过！")
        print("可以使用以下命令启动应用:")
        print("  python start_ui.py")
        print("  或")
        print("  python main.py --mode ui")
        return True
    else:
        print("\n⚠️ 部分检查未通过，请根据上述信息进行修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)