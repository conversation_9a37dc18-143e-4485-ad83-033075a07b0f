# TikTok AI Agent 最小配置模板
# 复制此文件为 .env 并填入实际配置

# ================================
# 必需配置（必须填写）
# ================================

# 千问API密钥（从阿里云百炼平台获取）
# 获取地址: https://bailian.console.aliyun.com/
QWEN_API_KEY=your_qwen_api_key_here

# ================================
# 可选配置（可以使用默认值）
# ================================

# 应用配置
DEBUG=true
LOG_LEVEL=info

# 千问模型配置
QWEN_MODEL=qwen-turbo
QWEN_TIMEOUT=30

# 如果不配置数据库，系统将使用SQLite
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=tiktok_data
# DB_USER=postgres
# DB_PASSWORD=your_password

# 如果不配置Redis，系统将使用内存缓存
# REDIS_HOST=localhost
# REDIS_PORT=6379