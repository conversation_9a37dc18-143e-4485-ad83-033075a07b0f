# TikTok AI Agent 环境配置指南

## 🚀 快速开始

### 1. 环境要求

- **Python**: 3.12+
- **数据库**: PostgreSQL 15+ (可选，用于生产环境)
- **缓存**: Redis 7+ (可选，用于生产环境)
- **千问API**: 阿里云百炼平台API密钥

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者使用开发模式安装
pip install -e .
```

### 3. 环境配置

#### 3.1 复制配置文件
```bash
cp .env.example .env
```

#### 3.2 必需配置项

**千问API配置（必需）:**
```env
QWEN_API_KEY=sk-xxxxxxxxxxxxxxxxxx  # 从阿里云百炼平台获取
```

**获取千问API密钥步骤:**
1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册/登录阿里云账号
3. 开通百炼服务
4. 创建应用并获取API Key
5. 将API Key填入 `.env` 文件的 `QWEN_API_KEY`

#### 3.3 可选配置项

**数据库配置（可选）:**
```env
# 如果不配置，系统将使用SQLite
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tiktok_data
DB_USER=postgres
DB_PASSWORD=your_password
```

**Redis配置（可选）:**
```env
# 如果不配置，系统将使用内存缓存
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 4. 启动方式

#### 方式一：直接启动（推荐用于开发）

**启动UI界面模式:**
```bash
python main.py --mode ui --port 8501
```
然后访问: http://localhost:8501

**启动API接口模式:**
```bash
python main.py --mode api --port 8000
```
然后访问: http://localhost:8000/docs

#### 方式二：Docker启动（推荐用于生产）

**启动所有服务:**
```bash
# 确保.env文件已配置
docker-compose up -d
```

**只启动UI服务:**
```bash
docker-compose up -d tiktok-agent-ui
```

**只启动API服务:**
```bash
docker-compose up -d tiktok-agent-api
```

### 5. 验证安装

#### 5.1 检查API服务
```bash
curl http://localhost:8000/api/v1/health
```

#### 5.2 测试查询功能
```bash
curl -X POST http://localhost:8000/api/v1/query \
  -H "Content-Type: application/json" \
  -d '{"question": "测试查询", "user_id": "test"}'
```

## 🔧 详细配置说明

### 千问模型配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| QWEN_API_KEY | 千问API密钥（必需） | - |
| QWEN_API_BASE | API基础URL | https://dashscope.aliyuncs.com/api/v1 |
| QWEN_MODEL | 使用的模型名称 | qwen-turbo |
| QWEN_TIMEOUT | 请求超时时间(秒) | 30 |
| QWEN_MAX_RETRIES | 最大重试次数 | 3 |

### 数据库配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 5432 |
| DB_NAME | 数据库名称 | tiktok_data |
| DB_USER | 数据库用户 | postgres |
| DB_PASSWORD | 数据库密码 | - |
| DB_POOL_SIZE | 连接池大小 | 10 |

### Redis配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| REDIS_DB | Redis数据库编号 | 0 |
| REDIS_PASSWORD | Redis密码 | - |

## 🐛 常见问题

### 1. 千问API密钥错误
```
错误: Invalid API-key provided
解决: 检查QWEN_API_KEY是否正确设置
```

### 2. 端口被占用
```
错误: Port already in use
解决: 修改启动端口或停止占用端口的服务
```

### 3. 依赖安装失败
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. 数据库连接失败
```
错误: connection to server failed
解决: 
1. 检查数据库服务是否启动
2. 验证数据库连接参数
3. 或者不配置数据库，使用默认SQLite
```

## 📝 开发模式

### 启用调试模式
```env
DEBUG=true
LOG_LEVEL=debug
```

### 代码格式化
```bash
# 安装开发依赖
pip install -e ".[dev]"

# 格式化代码
black src/
isort src/

# 类型检查
mypy src/

# 代码检查
flake8 src/
```

### 运行测试
```bash
python tests/test_models.py
```

## 🚀 生产部署

### 1. 使用Docker Compose
```bash
# 生产环境配置
cp .env.example .env.prod
# 编辑.env.prod，设置生产环境配置

# 启动生产服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 2. 性能优化配置
```env
# 增加工作进程数
API_WORKERS=4

# 数据库连接池优化
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30

# Redis连接优化
REDIS_MAX_CONNECTIONS=20
```

### 3. 监控和日志
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f tiktok-agent-api

# 健康检查
curl http://localhost:8000/api/v1/health
```