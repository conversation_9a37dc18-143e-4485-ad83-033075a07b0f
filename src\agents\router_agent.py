"""
路由Agent实现
负责用户交互入口、意图识别和任务分发
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

try:
    from ..core.qwen_text_generator import QwenTextGenerator
    from ..core.vanna_core import Vanna<PERSON>ore
    from ..models.query import UserQuery, QueryResult
    from ..models.base import APIResponse
except ImportError:
    # 绝对导入作为备选
    from core.qwen_text_generator import QwenTextGenerator
    from core.vanna_core import VannaCore
    from models.query import UserQuery, QueryResult
    from models.base import APIResponse

logger = logging.getLogger(__name__)


class IntentType(Enum):
    """意图类型枚举"""
    DATA_QUERY = "data_query"      # 数据查询
    CHAT = "chat"                  # 闲聊对话
    HELP = "help"                  # 寻求帮助
    OTHER = "other"                # 其他


class SessionState:
    """会话状态管理"""
    
    def __init__(self, session_id: str, user_id: Optional[str] = None):
        """
        初始化会话状态
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
        """
        self.session_id = session_id
        self.user_id = user_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.context: Dict[str, Any] = {}
        self.query_history: List[Dict[str, Any]] = []
        
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = datetime.now()
    
    def add_query(self, query: str, result: Any):
        """添加查询历史"""
        self.query_history.append({
            "query": query,
            "result": result,
            "timestamp": datetime.now()
        })
        self.update_activity()
    
    def get_recent_queries(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的查询历史"""
        return self.query_history[-limit:] if self.query_history else []


class RouterAgent:
    """路由Agent - 用户交互入口和任务分发"""
    
    def __init__(self, vanna_core: Optional[VannaCore] = None):
        """
        初始化路由Agent
        
        Args:
            vanna_core: Vanna核心引擎实例
        """
        self.text_generator = QwenTextGenerator()
        self.vanna_core = vanna_core or VannaCore()
        
        # 会话管理
        self.sessions: Dict[str, SessionState] = {}
        self.session_timeout = 3600  # 会话超时时间（秒）
        
        # 意图分类配置
        self.intent_confidence_threshold = 0.6
        
        logger.info("RouterAgent initialized")
    
    async def process_user_input(self, user_input: str, session_id: str, 
                               user_id: Optional[str] = None) -> APIResponse:
        """
        处理用户输入
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            APIResponse: 处理结果
        """
        try:
            logger.info(f"Processing user input: {user_input[:100]}...")
            
            # 获取或创建会话状态
            session = self._get_or_create_session(session_id, user_id)
            
            # 分类用户意图
            intent_result = await self.classify_intent(user_input)
            intent_type = IntentType(intent_result.get("intent", "other"))
            confidence = intent_result.get("confidence", 0.0)
            
            logger.info(f"Classified intent: {intent_type.value} (confidence: {confidence:.2f})")
            
            # 根据意图路由到不同的处理器
            if intent_type == IntentType.DATA_QUERY and confidence >= self.intent_confidence_threshold:
                response = await self._handle_data_query(user_input, session)
            elif intent_type == IntentType.HELP:
                response = await self._handle_help_request(user_input, session)
            elif intent_type == IntentType.CHAT:
                response = await self._handle_chat(user_input, session)
            else:
                response = await self._handle_unknown_intent(user_input, session)
            
            # 更新会话状态
            session.add_query(user_input, response.data)
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to process user input: {e}")
            return APIResponse(
                success=False,
                message="处理用户输入时发生错误",
                error=str(e)
            )
    
    async def classify_intent(self, user_input: str) -> Dict[str, Any]:
        """
        分类用户意图
        
        Args:
            user_input: 用户输入
            
        Returns:
            Dict: 意图分类结果
        """
        try:
            # 使用千问模型进行意图分类
            result = await self.text_generator.classify_intent(user_input)
            return result
            
        except Exception as e:
            logger.error(f"Failed to classify intent: {e}")
            # 回退到简单的关键词匹配
            return self._fallback_intent_classification(user_input)
    
    def _fallback_intent_classification(self, user_input: str) -> Dict[str, Any]:
        """回退的意图分类逻辑"""
        user_input_lower = user_input.lower()
        
        # 数据查询关键词
        data_keywords = [
            "查询", "统计", "分析", "数据", "多少", "哪个", "排行", "top", "最高", "最低",
            "涨粉", "播放量", "点赞", "评论", "分享", "互动率", "粉丝", "视频", "达人"
        ]
        
        # 闲聊关键词
        chat_keywords = [
            "你好", "hello", "hi", "谢谢", "再见", "怎么样", "如何", "什么是"
        ]
        
        # 帮助关键词
        help_keywords = [
            "帮助", "help", "使用", "功能", "怎么用", "如何使用", "说明"
        ]
        
        # 检查关键词匹配
        data_score = sum(1 for keyword in data_keywords if keyword in user_input_lower)
        chat_score = sum(1 for keyword in chat_keywords if keyword in user_input_lower)
        help_score = sum(1 for keyword in help_keywords if keyword in user_input_lower)
        
        if data_score > 0:
            return {
                "intent": "data_query",
                "confidence": min(0.8, 0.5 + data_score * 0.1),
                "keywords": [kw for kw in data_keywords if kw in user_input_lower],
                "explanation": "检测到数据查询相关关键词"
            }
        elif help_score > 0:
            return {
                "intent": "help",
                "confidence": 0.7,
                "keywords": [kw for kw in help_keywords if kw in user_input_lower],
                "explanation": "检测到帮助相关关键词"
            }
        elif chat_score > 0:
            return {
                "intent": "chat",
                "confidence": 0.6,
                "keywords": [kw for kw in chat_keywords if kw in user_input_lower],
                "explanation": "检测到闲聊相关关键词"
            }
        else:
            return {
                "intent": "other",
                "confidence": 0.3,
                "keywords": [],
                "explanation": "未能识别明确意图"
            }
    
    async def _handle_data_query(self, user_input: str, session: SessionState) -> APIResponse:
        """处理数据查询请求"""
        try:
            logger.info("Handling data query request")
            
            # 创建用户查询对象
            user_query = UserQuery(
                question=user_input,
                user_id=session.user_id,
                session_id=session.session_id
            )
            
            # 使用Vanna核心处理查询
            query_result = await self.vanna_core.process_query(user_query)
            
            if query_result.error:
                return APIResponse(
                    success=False,
                    message="数据查询失败",
                    error=query_result.error
                )
            
            # 格式化查询结果
            formatted_result = self._format_query_result(query_result)
            
            return APIResponse(
                success=True,
                message="数据查询完成",
                data={
                    "type": "data_query",
                    "query": user_input,
                    "sql": query_result.sql_executed,
                    "data": query_result.data,
                    "columns": query_result.columns,
                    "row_count": query_result.row_count,
                    "execution_time": query_result.execution_time,
                    "formatted_result": formatted_result,
                    "is_mock": getattr(query_result, 'is_mock', False)
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to handle data query: {e}")
            return APIResponse(
                success=False,
                message="处理数据查询时发生错误",
                error=str(e)
            )
    
    async def _handle_help_request(self, user_input: str, session: SessionState) -> APIResponse:
        """处理帮助请求"""
        try:
            logger.info("Handling help request")
            
            help_content = {
                "system_info": {
                    "name": "TikTok AI数据分析助手",
                    "version": "1.0.0",
                    "description": "基于AI的TikTok数据查询和分析系统"
                },
                "capabilities": [
                    "自然语言数据查询",
                    "TikTok达人数据分析",
                    "视频播放量统计",
                    "粉丝增长趋势分析",
                    "互动率计算",
                    "分类数据统计"
                ],
                "example_queries": [
                    "今天涨粉最多的前10个达人是谁？",
                    "过去一周哪个游戏达人播放量最高？",
                    "美妆类视频的平均点赞率是多少？",
                    "各个分类达人的平均粉丝数和视频数是多少？",
                    "找出互动率最高的100个视频"
                ],
                "usage_tips": [
                    "使用自然语言描述你想查询的数据",
                    "可以询问关于达人、视频、粉丝、播放量等相关问题",
                    "支持时间范围查询，如'过去一周'、'今天'等",
                    "支持排序查询，如'最高'、'前10名'等"
                ]
            }
            
            return APIResponse(
                success=True,
                message="帮助信息",
                data={
                    "type": "help",
                    "content": help_content
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to handle help request: {e}")
            return APIResponse(
                success=False,
                message="获取帮助信息时发生错误",
                error=str(e)
            )
    
    async def _handle_chat(self, user_input: str, session: SessionState) -> APIResponse:
        """处理闲聊对话"""
        try:
            logger.info("Handling chat request")
            
            # 生成友好的回复
            chat_responses = {
                "greeting": [
                    "你好！我是TikTok数据分析助手，可以帮你查询和分析TikTok相关数据。",
                    "欢迎使用TikTok AI助手！有什么数据问题需要我帮忙分析吗？",
                    "Hi！我可以帮你查询TikTok达人数据、视频统计等信息。"
                ],
                "thanks": [
                    "不客气！很高兴能帮到你。还有其他数据问题吗？",
                    "不用谢！如果需要查询更多TikTok数据，随时告诉我。",
                    "很乐意为你服务！有任何数据分析需求都可以问我。"
                ],
                "goodbye": [
                    "再见！期待下次为你提供数据分析服务。",
                    "拜拜！有数据问题随时回来找我。",
                    "再见！祝你工作顺利！"
                ],
                "default": [
                    "我是专门处理TikTok数据查询的AI助手。你可以问我关于达人、视频、粉丝等数据问题。",
                    "我主要帮助分析TikTok数据。比如你可以问'今天涨粉最多的达人是谁？'",
                    "作为TikTok数据分析助手，我可以帮你查询各种相关统计信息。试试问我一些数据问题吧！"
                ]
            }
            
            user_input_lower = user_input.lower()
            
            if any(word in user_input_lower for word in ["你好", "hello", "hi"]):
                response_list = chat_responses["greeting"]
            elif any(word in user_input_lower for word in ["谢谢", "thanks", "thank you"]):
                response_list = chat_responses["thanks"]
            elif any(word in user_input_lower for word in ["再见", "bye", "goodbye"]):
                response_list = chat_responses["goodbye"]
            else:
                response_list = chat_responses["default"]
            
            import random
            response_text = random.choice(response_list)
            
            return APIResponse(
                success=True,
                message="对话回复",
                data={
                    "type": "chat",
                    "response": response_text,
                    "suggestions": [
                        "查询今日涨粉排行榜",
                        "分析热门视频数据",
                        "统计各分类达人情况",
                        "获取使用帮助"
                    ]
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to handle chat: {e}")
            return APIResponse(
                success=False,
                message="处理对话时发生错误",
                error=str(e)
            )
    
    async def _handle_unknown_intent(self, user_input: str, session: SessionState) -> APIResponse:
        """处理未知意图"""
        try:
            logger.info("Handling unknown intent")
            
            return APIResponse(
                success=True,
                message="未能理解您的问题",
                data={
                    "type": "unknown",
                    "response": "抱歉，我没有完全理解您的问题。我是TikTok数据分析助手，主要帮助查询和分析TikTok相关数据。",
                    "suggestions": [
                        "试试问我：'今天涨粉最多的达人是谁？'",
                        "或者：'过去一周播放量最高的视频'",
                        "也可以问：'美妆类达人的平均粉丝数'",
                        "输入'帮助'获取更多使用说明"
                    ],
                    "help_hint": "您可以用自然语言询问关于TikTok达人、视频、粉丝等数据问题。"
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to handle unknown intent: {e}")
            return APIResponse(
                success=False,
                message="处理请求时发生错误",
                error=str(e)
            )
    
    def _format_query_result(self, query_result: QueryResult) -> str:
        """格式化查询结果为可读文本"""
        try:
            if not query_result.data:
                return "查询未返回任何数据。"
            
            # 根据数据类型生成不同的格式化文本
            data = query_result.data
            
            if len(data) == 1:
                # 单行结果
                row = data[0]
                parts = []
                for key, value in row.items():
                    if isinstance(value, (int, float)):
                        if key in ['follower_count', 'total_views', 'view_count']:
                            parts.append(f"{key}: {value:,}")
                        elif key in ['engagement_rate']:
                            parts.append(f"{key}: {value:.2f}%")
                        else:
                            parts.append(f"{key}: {value}")
                    else:
                        parts.append(f"{key}: {value}")
                return "查询结果：" + "，".join(parts)
            
            else:
                # 多行结果
                result_lines = [f"查询返回 {len(data)} 条结果："]
                
                for i, row in enumerate(data[:10], 1):  # 最多显示前10条
                    line_parts = []
                    for key, value in row.items():
                        if isinstance(value, (int, float)):
                            if key in ['follower_count', 'total_views', 'view_count']:
                                line_parts.append(f"{value:,}")
                            elif key in ['engagement_rate']:
                                line_parts.append(f"{value:.2f}%")
                            else:
                                line_parts.append(str(value))
                        else:
                            line_parts.append(str(value))
                    
                    result_lines.append(f"{i}. " + " | ".join(line_parts))
                
                if len(data) > 10:
                    result_lines.append(f"... 还有 {len(data) - 10} 条结果")
                
                return "\n".join(result_lines)
                
        except Exception as e:
            logger.warning(f"Failed to format query result: {e}")
            return f"查询完成，返回 {query_result.row_count} 条结果。"
    
    def _get_or_create_session(self, session_id: str, user_id: Optional[str] = None) -> SessionState:
        """获取或创建会话状态"""
        # 清理过期会话
        self._cleanup_expired_sessions()
        
        if session_id not in self.sessions:
            self.sessions[session_id] = SessionState(session_id, user_id)
            logger.info(f"Created new session: {session_id}")
        else:
            self.sessions[session_id].update_activity()
        
        return self.sessions[session_id]
    
    def _cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            current_time = datetime.now()
            expired_sessions = []
            
            for session_id, session in self.sessions.items():
                if (current_time - session.last_activity).total_seconds() > self.session_timeout:
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                del self.sessions[session_id]
                logger.info(f"Cleaned up expired session: {session_id}")
                
        except Exception as e:
            logger.warning(f"Failed to cleanup expired sessions: {e}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        return {
            "active_sessions": len(self.sessions),
            "session_timeout": self.session_timeout
        }
    
    async def close(self):
        """关闭资源"""
        if self.text_generator:
            await self.text_generator.close()
        if self.vanna_core:
            await self.vanna_core.close()
        logger.info("RouterAgent closed")