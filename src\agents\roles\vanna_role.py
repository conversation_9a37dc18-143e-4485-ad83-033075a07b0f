"""Vanna核心角色定义"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class VannaRole:
    """Vanna核心角色配置"""
    
    # 角色基本信息
    name: str = "Vanna核心"
    description: str = "负责Text-to-SQL转换和数据查询执行的专业数据库专家"
    version: str = "1.0.0"
    
    # 角色能力
    capabilities: List[str] = None
    
    # 角色限制
    limitations: List[str] = None
    
    # 工作流程
    workflow: List[str] = None
    
    # 性能指标
    performance_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.capabilities is None:
            self.capabilities = [
                "自然语言到SQL的智能转换",
                "基于RAG的上下文检索",
                "SQL语法验证和优化",
                "安全的数据库查询执行",
                "查询结果格式化",
                "错误检测和自动修复",
                "性能优化建议",
                "多数据库方言支持"
            ]
        
        if self.limitations is None:
            self.limitations = [
                "依赖预训练的知识库质量",
                "复杂业务逻辑需要明确指导",
                "不支持数据修改操作",
                "受限于数据库权限设置",
                "需要明确的查询意图"
            ]
        
        if self.workflow is None:
            self.workflow = [
                "1. 接收自然语言查询请求",
                "2. 问题预处理和关键词提取",
                "3. RAG检索相关Schema和范例",
                "4. 构建上下文增强的提示词",
                "5. 调用千问模型生成SQL",
                "6. SQL语法验证和安全检查",
                "7. 执行数据库查询",
                "8. 结果后处理和格式化",
                "9. 返回结构化查询结果"
            ]
        
        if self.performance_metrics is None:
            self.performance_metrics = {
                "sql_accuracy": "SQL生成准确率 > 85%",
                "execution_success": "查询执行成功率 > 95%",
                "response_time": "平均响应时间 < 10秒",
                "context_relevance": "上下文相关性 > 80%",
                "safety_compliance": "安全合规率 100%"
            }
    
    def get_role_prompt(self) -> str:
        """获取角色提示词"""
        return f"""
你是{self.name}，{self.description}。

你的专业技能包括：
{chr(10).join(f'• {cap}' for cap in self.capabilities)}

工作流程：
{chr(10).join(self.workflow)}

SQL生成原则：
1. 准确性：确保SQL语法正确，逻辑清晰
2. 安全性：避免危险操作，防止SQL注入
3. 性能：优化查询性能，合理使用索引
4. 可读性：生成清晰易懂的SQL代码
5. 业务理解：正确理解TikTok业务概念

支持的数据库类型：
• PostgreSQL (主要)
• MySQL
• SQLite
• SQL Server

安全限制：
• 只允许SELECT查询
• 禁止DROP、DELETE、UPDATE等修改操作
• 限制查询结果数量
• 防止无限循环和资源耗尽

当前版本：{self.version}
"""
    
    def get_sql_generation_config(self) -> Dict[str, Any]:
        """获取SQL生成配置"""
        return {
            "model": "qwen-turbo",
            "temperature": 0.1,  # 低温度确保SQL准确性
            "max_tokens": 1500,
            "allowed_operations": ["SELECT"],
            "forbidden_keywords": [
                "DROP", "DELETE", "UPDATE", "INSERT", 
                "ALTER", "TRUNCATE", "CREATE", "GRANT", "REVOKE"
            ],
            "max_result_rows": 1000,
            "query_timeout": 30,  # 秒
            "safety_checks": [
                "sql_injection_prevention",
                "operation_whitelist",
                "result_size_limit",
                "execution_timeout"
            ]
        }
    
    def get_rag_config(self) -> Dict[str, Any]:
        """获取RAG检索配置"""
        return {
            "embedding_model": "qwen-embedding",
            "similarity_threshold": 0.7,
            "max_context_items": 10,
            "context_types": [
                "schema_ddl",      # 数据库架构
                "business_docs",   # 业务文档
                "sql_examples"     # SQL范例
            ],
            "retrieval_strategy": "hybrid",  # 混合检索策略
            "rerank_enabled": True,
            "context_window": 4000  # 上下文窗口大小
        }
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            "connection_pool": {
                "min_connections": 1,
                "max_connections": 10,
                "connection_timeout": 30,
                "idle_timeout": 300
            },
            "query_limits": {
                "max_execution_time": 30,
                "max_result_rows": 1000,
                "max_memory_usage": "100MB"
            },
            "supported_functions": [
                "COUNT", "SUM", "AVG", "MAX", "MIN",
                "GROUP BY", "ORDER BY", "HAVING",
                "JOIN", "LEFT JOIN", "RIGHT JOIN",
                "UNION", "DISTINCT", "LIMIT"
            ]
        }
    
    def get_error_handling_config(self) -> Dict[str, str]:
        """获取错误处理配置"""
        return {
            "syntax_error": "SQL语法错误，正在尝试自动修复...",
            "execution_error": "查询执行失败，请检查数据和条件",
            "timeout_error": "查询超时，请尝试简化查询条件",
            "permission_error": "数据库权限不足，请联系管理员",
            "connection_error": "数据库连接失败，请稍后重试",
            "safety_violation": "查询包含不安全操作，已被阻止"
        }
    
    def get_optimization_rules(self) -> List[Dict[str, str]]:
        """获取SQL优化规则"""
        return [
            {
                "rule": "使用索引列进行WHERE条件过滤",
                "description": "优先使用已建立索引的列进行条件筛选"
            },
            {
                "rule": "避免SELECT *，明确指定需要的列",
                "description": "减少数据传输量，提高查询性能"
            },
            {
                "rule": "合理使用LIMIT限制结果数量",
                "description": "防止返回过多数据影响性能"
            },
            {
                "rule": "优化JOIN操作的顺序",
                "description": "小表驱动大表，减少中间结果集"
            },
            {
                "rule": "使用EXISTS替代IN子查询",
                "description": "在某些情况下EXISTS性能更好"
            }
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "capabilities": self.capabilities,
            "limitations": self.limitations,
            "workflow": self.workflow,
            "performance_metrics": self.performance_metrics,
            "sql_generation_config": self.get_sql_generation_config(),
            "rag_config": self.get_rag_config(),
            "database_config": self.get_database_config(),
            "error_handling_config": self.get_error_handling_config(),
            "optimization_rules": self.get_optimization_rules()
        }