#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

async def test_database_connection():
    """测试数据库连接"""
    try:
        from core.config import get_config
        from core.database_executor import DatabaseExecutor
        
        print("🔍 测试数据库连接...")
        
        # 获取配置
        config = get_config()
        print(f"数据库配置:")
        print(f"  类型: {config.vanna.db_type}")
        print(f"  主机: {config.database.host}")
        print(f"  端口: {config.database.port}")
        print(f"  数据库: {config.database.name}")
        print(f"  用户: {config.database.user}")
        
        # 创建数据库执行器
        db_executor = DatabaseExecutor()
        
        # 初始化连接池
        await db_executor.initialize_pool()
        
        if db_executor.connection_pool:
            print("✅ 数据库连接池初始化成功")
            
            # 测试简单查询
            test_sql = "SELECT 1 as test_value"
            result = await db_executor.execute_query(test_sql)
            
            if result.error:
                print(f"❌ 查询执行失败: {result.error}")
            else:
                print(f"✅ 查询执行成功: {result.data}")
                print(f"是否为模拟数据: {getattr(result, 'is_mock', False)}")
        else:
            print("❌ 数据库连接池初始化失败")
            
            # 测试模拟查询
            print("🔄 测试模拟查询...")
            test_sql = "SELECT username, follower_growth FROM creators ORDER BY follower_growth DESC LIMIT 5"
            result = await db_executor.execute_query(test_sql)
            
            if result.error:
                print(f"❌ 模拟查询失败: {result.error}")
            else:
                print(f"✅ 模拟查询成功: 返回 {result.row_count} 行数据")
                print(f"是否为模拟数据: {getattr(result, 'is_mock', False)}")
                if result.data:
                    print("前3行数据:")
                    for i, row in enumerate(result.data[:3]):
                        print(f"  {i+1}. {row}")
        
        # 关闭连接
        await db_executor.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_vanna_core():
    """测试VannaCore查询处理"""
    try:
        from core.vanna_core import VannaCore
        from models.query import UserQuery
        
        print("\n🔍 测试VannaCore查询处理...")
        
        # 创建VannaCore实例
        vanna_core = VannaCore()
        
        # 创建测试查询
        user_query = UserQuery(
            question="今天涨粉最多的前5个达人是谁？",
            user_id="test_user",
            session_id="test_session"
        )
        
        # 处理查询
        result = await vanna_core.process_query(user_query)
        
        if result.error:
            print(f"❌ VannaCore查询失败: {result.error}")
        else:
            print(f"✅ VannaCore查询成功: 返回 {result.row_count} 行数据")
            print(f"执行时间: {result.execution_time:.3f}秒")
            print(f"SQL: {result.sql_executed}")
            if result.data:
                print("前3行数据:")
                for i, row in enumerate(result.data[:3]):
                    print(f"  {i+1}. {row}")
        
        # 关闭VannaCore
        await vanna_core.close()
        
    except Exception as e:
        print(f"❌ VannaCore测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_router_agent():
    """测试RouterAgent"""
    try:
        from agents.router_agent import RouterAgent
        from core.vanna_core import VannaCore
        
        print("\n🔍 测试RouterAgent...")
        
        # 创建RouterAgent实例
        vanna_core = VannaCore()
        router_agent = RouterAgent(vanna_core)
        
        # 测试查询处理
        response = await router_agent.process_user_input(
            user_input="今天涨粉最多的前5个达人是谁？",
            session_id="test_session",
            user_id="test_user"
        )
        
        if response.success:
            print(f"✅ RouterAgent处理成功")
            print(f"响应类型: {response.data.get('type', 'unknown') if response.data else 'no data'}")
            if response.data and response.data.get('type') == 'data_query':
                print(f"返回行数: {response.data.get('row_count', 0)}")
                print(f"是否为模拟数据: {response.data.get('is_mock', False)}")
        else:
            print(f"❌ RouterAgent处理失败: {response.message}")
            if response.error:
                print(f"错误详情: {response.error}")
        
        # 关闭资源
        await router_agent.close()
        await vanna_core.close()
        
    except Exception as e:
        print(f"❌ RouterAgent测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("🎵 TikTok AI Agent 数据库连接测试")
    print("=" * 50)
    
    # 测试数据库连接
    await test_database_connection()
    
    # 测试VannaCore
    await test_vanna_core()
    
    # 测试RouterAgent
    await test_router_agent()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    asyncio.run(main())