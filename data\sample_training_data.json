{"schemas": [{"table_name": "creators", "ddl": "CREATE TABLE creators (\n                    id BIGINT PRIMARY KEY,\n                    username VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,\n                    display_name VA<PERSON><PERSON><PERSON>(200),\n                    category VARCHAR(50),\n                    follower_count BIGINT DEFAULT 0,\n                    following_count BIGINT DEFAULT 0,\n                    video_count INT DEFAULT 0,\n                    verified BOOLEAN DEFAULT FALSE,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n                )", "description": "TikTok达人基础信息表，存储达人的基本资料和统计数据"}, {"table_name": "videos", "ddl": "CREATE TABLE videos (\n                    id BIGINT <PERSON><PERSON><PERSON><PERSON>,\n                    creator_id BIGINT NOT NULL,\n                    title VARCHAR(500),\n                    description TEXT,\n                    duration_seconds INT,\n                    view_count BIGINT DEFAULT 0,\n                    like_count BIGINT DEFAULT 0,\n                    comment_count BIGINT DEFAULT 0,\n                    share_count BIGINT DEFAULT 0,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREI<PERSON><PERSON> KEY (creator_id) REFERENCES creators(id)\n                )", "description": "TikTok视频信息表，存储视频内容和互动数据"}, {"table_name": "daily_metrics", "ddl": "CREATE TABLE daily_metrics (\n                    id BIGINT <PERSON>IMARY KEY,\n                    creator_id BIGINT NOT NULL,\n                    date DATE NOT NULL,\n                    follower_count BIGINT,\n                    follower_growth INT,\n                    total_views BIGINT,\n                    total_likes BIGINT,\n                    video_count INT,\n                    avg_engagement_rate DECIMAL(5,4),\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREI<PERSON><PERSON> KEY (creator_id) REFERENCES creators(id),\n                    UNIQUE(creator_id, date)\n                )", "description": "达人每日指标表，用于跟踪达人的日常表现和增长趋势"}], "documents": [{"title": "涨粉量计算规则", "content": "涨粉量的计算方式和规则：\n\n1. 基本计算公式：\n   涨粉量 = 当前粉丝数 - 前一天粉丝数\n\n2. 计算规则：\n   - 使用daily_metrics表中的follower_count字段\n   - 比较相邻两天的数据\n   - 新达人（没有历史数据）的涨粉量等于当前粉丝数\n   - 涨粉量可能为负数，表示掉粉\n\n3. 示例SQL：\n   SELECT \n       c.username,\n       dm.follower_count - LAG(dm.follower_count) OVER (\n           PARTITION BY dm.creator_id ORDER BY dm.date\n       ) as follower_growth\n   FROM daily_metrics dm\n   JOIN creators c ON dm.creator_id = c.id\n   WHERE dm.date = CURRENT_DATE;\n\n4. 注意事项：\n   - 数据更新时间为每日凌晨2点\n   - 异常数据（如粉丝数突然归零）需要人工审核\n   - 涨粉量超过10万的情况需要特别关注", "category": "business_rules"}, {"title": "互动率计算方法", "content": "互动率的计算方法和标准：\n\n1. 基本公式：\n   互动率 = (点赞数 + 评论数 + 分享数) / 播放量 * 100%\n\n2. 计算维度：\n   - 单个视频互动率：针对特定视频\n   - 达人平均互动率：所有视频的平均值\n   - 时间段互动率：特定时间范围内的平均值\n\n3. 分级标准：\n   - 优秀：互动率 > 8%\n   - 良好：5% < 互动率 <= 8%\n   - 一般：2% < 互动率 <= 5%\n   - 较低：互动率 <= 2%\n\n4. 示例SQL：\n   SELECT \n       v.id,\n       v.title,\n       (v.like_count + v.comment_count + v.share_count) * 100.0 / v.view_count as engagement_rate\n   FROM videos v\n   WHERE v.view_count > 0\n   ORDER BY engagement_rate DESC;\n\n5. 应用场景：\n   - 达人价值评估\n   - 内容质量分析\n   - 投放效果预测", "category": "business_rules"}, {"title": "达人分类体系", "content": "TikTok达人分类体系说明：\n\n1. 主要分类：\n   - gaming: 游戏类达人\n   - beauty: 美妆类达人\n   - food: 美食类达人\n   - dance: 舞蹈类达人\n   - comedy: 搞笑类达人\n   - education: 教育类达人\n   - lifestyle: 生活方式类达人\n   - music: 音乐类达人\n   - sports: 体育类达人\n   - tech: 科技类达人\n   - fashion: 时尚类达人\n   - travel: 旅行类达人\n\n2. 分类规则：\n   - 基于达人发布内容的主要类型\n   - 一个达人只能属于一个主分类\n   - 分类由算法自动识别，人工审核确认\n   - 分类可能随时间调整\n\n3. 分类用途：\n   - 内容推荐算法\n   - 广告投放定向\n   - 达人价值评估\n   - 行业分析报告\n\n4. 查询示例：\n   SELECT category, COUNT(*) as creator_count\n   FROM creators\n   WHERE category IS NOT NULL\n   GROUP BY category\n   ORDER BY creator_count DESC;", "category": "data_dictionary"}], "sql_examples": [{"question": "过去一周哪个游戏达人播放量最高？", "sql": "SELECT \n    c.username,\n    c.display_name,\n    SUM(v.view_count) as total_views\nFROM creators c\nJOIN videos v ON c.id = v.creator_id\nWHERE c.category = 'gaming'\n    AND v.created_at >= CURRENT_DATE - INTERVAL '7 days'\nGROUP BY c.id, c.username, c.display_name\nORDER BY total_views DESC\nLIMIT 1;", "explanation": "查询游戏分类达人在过去7天内发布视频的总播放量，按播放量降序排列取第一名", "difficulty": "medium"}, {"question": "美妆类视频的平均点赞率是多少？", "sql": "SELECT \n    AVG(CASE WHEN v.view_count > 0 THEN v.like_count * 1.0 / v.view_count ELSE 0 END) as avg_like_rate\nFROM videos v\nJOIN creators c ON v.creator_id = c.id\nWHERE c.category = 'beauty'\n    AND v.view_count > 0;", "explanation": "计算美妆类达人所有视频的平均点赞率，排除播放量为0的视频避免除零错误", "difficulty": "medium"}, {"question": "今天涨粉最多的前10个达人是谁？", "sql": "SELECT \n    c.username,\n    c.display_name,\n    c.category,\n    dm.follower_growth\nFROM daily_metrics dm\nJOIN creators c ON dm.creator_id = c.id\nWHERE dm.date = CURRENT_DATE\n    AND dm.follower_growth > 0\nORDER BY dm.follower_growth DESC\nLIMIT 10;", "explanation": "查询今日涨粉量最多的前10个达人，使用daily_metrics表中预计算的follower_growth字段", "difficulty": "easy"}, {"question": "各个分类达人的平均粉丝数和视频数是多少？", "sql": "SELECT \n    category,\n    COUNT(*) as creator_count,\n    AVG(follower_count) as avg_followers,\n    AVG(video_count) as avg_videos,\n    SUM(follower_count) as total_followers\nFROM creators\nWHERE category IS NOT NULL\nGROUP BY category\nORDER BY avg_followers DESC;", "explanation": "按达人分类统计各项指标，包括达人数量、平均粉丝数、平均视频数和总粉丝数", "difficulty": "easy"}, {"question": "找出互动率最高的100个视频", "sql": "SELECT \n    v.id,\n    v.title,\n    c.username,\n    v.view_count,\n    v.like_count,\n    v.comment_count,\n    v.share_count,\n    (v.like_count + v.comment_count + v.share_count) * 100.0 / v.view_count as engagement_rate\nFROM videos v\nJOIN creators c ON v.creator_id = c.id\nWHERE v.view_count >= 1000  -- 过滤掉播放量过低的视频\nORDER BY engagement_rate DESC\nLIMIT 100;", "explanation": "计算所有视频的互动率并排序，只考虑播放量大于等于1000的视频以确保数据质量", "difficulty": "medium"}, {"question": "查询某个达人最近30天的粉丝增长趋势", "sql": "SELECT \n    dm.date,\n    dm.follower_count,\n    dm.follower_growth,\n    SUM(dm.follower_growth) OVER (\n        ORDER BY dm.date \n        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW\n    ) as weekly_growth\nFROM daily_metrics dm\nJOIN creators c ON dm.creator_id = c.id\nWHERE c.username = 'target_username'\n    AND dm.date >= CURRENT_DATE - INTERVAL '30 days'\nORDER BY dm.date;", "explanation": "查询指定达人最近30天的每日粉丝数据，并计算7天滚动涨粉量，用于分析增长趋势", "difficulty": "hard"}]}