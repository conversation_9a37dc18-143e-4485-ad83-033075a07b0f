-- TikTok AI Agent 数据库初始化脚本

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建知识库相关表
CREATE TABLE IF NOT EXISTS knowledge_schemas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(255) NOT NULL,
    ddl TEXT NOT NULL,
    description TEXT,
    embedding VECTOR(1536),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS knowledge_docs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(100),
    embedding VECTOR(1536),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS knowledge_sql_examples (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    question TEXT NOT NULL,
    sql TEXT NOT NULL,
    explanation TEXT,
    difficulty VARCHAR(50),
    embedding VECTOR(1536),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户查询历史表
CREATE TABLE IF NOT EXISTS user_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    question TEXT NOT NULL,
    generated_sql TEXT,
    execution_time FLOAT,
    status VARCHAR(50),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_knowledge_schemas_table_name ON knowledge_schemas(table_name);
CREATE INDEX IF NOT EXISTS idx_knowledge_docs_category ON knowledge_docs(category);
CREATE INDEX IF NOT EXISTS idx_user_queries_user_id ON user_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_user_queries_created_at ON user_queries(created_at);

-- 插入示例TikTok数据表结构
INSERT INTO knowledge_schemas (table_name, ddl, description) VALUES
('creators', 
 'CREATE TABLE creators (
    id BIGINT PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255),
    follower_count BIGINT DEFAULT 0,
    following_count BIGINT DEFAULT 0,
    video_count BIGINT DEFAULT 0,
    category VARCHAR(100),
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
 );',
 'TikTok达人基础信息表，包含用户名、粉丝数、分类等信息'),

('videos',
 'CREATE TABLE videos (
    id BIGINT PRIMARY KEY,
    creator_id BIGINT REFERENCES creators(id),
    title TEXT,
    description TEXT,
    duration INTEGER,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    share_count BIGINT DEFAULT 0,
    category VARCHAR(100),
    hashtags TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
 );',
 'TikTok视频信息表，包含播放量、点赞数、评论数等指标'),

('daily_metrics',
 'CREATE TABLE daily_metrics (
    id BIGINT PRIMARY KEY,
    creator_id BIGINT REFERENCES creators(id),
    date DATE NOT NULL,
    follower_growth INTEGER DEFAULT 0,
    total_views BIGINT DEFAULT 0,
    total_likes BIGINT DEFAULT 0,
    video_published INTEGER DEFAULT 0,
    engagement_rate DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
 );',
 '达人每日数据指标表，用于统计涨粉量、播放量等趋势数据');

-- 插入示例业务文档
INSERT INTO knowledge_docs (title, content, category) VALUES
('涨粉量计算方式',
 '涨粉量 = 当前粉丝数 - 前一天粉丝数。可以通过daily_metrics表的follower_growth字段直接获取，或者通过creators表的follower_count字段计算差值。',
 'business_rules'),

('播放量统计规则',
 '总播放量指达人所有视频的播放量总和。可以通过videos表的view_count字段求和，或者使用daily_metrics表的total_views字段获取每日累计播放量。',
 'business_rules'),

('达人分类说明',
 'TikTok达人主要分类包括：游戏(gaming)、美妆(beauty)、搞笑(comedy)、音乐(music)、舞蹈(dance)、美食(food)、教育(education)、时尚(fashion)等。',
 'category_info');

-- 插入示例SQL范例
INSERT INTO knowledge_sql_examples (question, sql, explanation, difficulty) VALUES
('过去一周哪个游戏达人播放量最高？',
 'SELECT c.username, c.display_name, SUM(v.view_count) as total_views
  FROM creators c
  JOIN videos v ON c.id = v.creator_id
  WHERE c.category = ''gaming''
    AND v.created_at >= CURRENT_DATE - INTERVAL ''7 days''
  GROUP BY c.id, c.username, c.display_name
  ORDER BY total_views DESC
  LIMIT 1;',
 '查询游戏分类达人在过去7天内的总播放量，按播放量降序排列取第一名',
 'medium'),

('美妆类视频的平均点赞率是多少？',
 'SELECT AVG(CASE WHEN v.view_count > 0 THEN v.like_count::DECIMAL / v.view_count ELSE 0 END) as avg_like_rate
  FROM videos v
  JOIN creators c ON v.creator_id = c.id
  WHERE c.category = ''beauty'';',
 '计算美妆分类视频的平均点赞率（点赞数/播放量）',
 'easy'),

('本月涨粉最快的前5名达人',
 'SELECT c.username, c.display_name, SUM(dm.follower_growth) as total_growth
  FROM creators c
  JOIN daily_metrics dm ON c.id = dm.creator_id
  WHERE dm.date >= DATE_TRUNC(''month'', CURRENT_DATE)
  GROUP BY c.id, c.username, c.display_name
  ORDER BY total_growth DESC
  LIMIT 5;',
 '统计本月每个达人的总涨粉量，按涨粉量降序排列取前5名',
 'medium');