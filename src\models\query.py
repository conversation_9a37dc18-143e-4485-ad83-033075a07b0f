"""查询相关数据模型"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field

from .base import BaseEntity


class QueryStatus(str, Enum):
    """查询状态枚举"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class IntentType(str, Enum):
    """意图类型枚举"""

    DATA_QUERY = "data_query"
    CHAT = "chat"
    HELP = "help"
    UNKNOWN = "unknown"


class UserQuery(BaseEntity):
    """用户查询模型"""

    user_id: Optional[str] = None
    session_id: Optional[str] = None
    question: str = Field(..., description="用户问题")
    intent: Optional[IntentType] = None
    status: QueryStatus = QueryStatus.PENDING

    class Config:
        use_enum_values = True


class SQLResult(BaseModel):
    """SQL生成结果模型"""

    sql: str = Field(..., description="生成的SQL语句")
    confidence: float = Field(ge=0.0, le=1.0, description="置信度")
    context_used: Optional[Any] = Field(default=None, description="使用的上下文")
    generation_time: float = Field(default=0.0, description="生成耗时(秒)")
    error: Optional[str] = Field(default=None, description="错误信息")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")


class QueryResult(BaseModel):
    """查询结果模型"""

    data: List[Dict[str, Any]] = Field(default_factory=list, description="查询数据")
    columns: List[str] = Field(default_factory=list, description="列名")
    row_count: int = Field(ge=0, description="行数")
    execution_time: float = Field(ge=0.0, description="执行耗时(秒)")
    sql_executed: Optional[str] = Field(default=None, description="执行的SQL语句")
    error: Optional[str] = Field(default=None, description="错误信息")
    executed_at: Optional[datetime] = Field(default=None, description="执行时间")
    is_mock: bool = Field(default=False, description="是否为模拟数据")
    context_used: Optional[Any] = Field(default=None, description="使用的上下文")
    sql_confidence: Optional[float] = Field(default=None, description="SQL置信度")


class AnalysisResult(BaseModel):
    """数据分析结果模型"""

    summary: str = Field(..., description="数据摘要")
    insights: List[str] = Field(default_factory=list, description="数据洞察")
    key_metrics: Dict[str, Any] = Field(default_factory=dict, description="关键指标")
    recommendations: List[str] = Field(default_factory=list, description="建议")


class VisualizationSpec(BaseModel):
    """可视化规格模型"""

    chart_type: str = Field(..., description="图表类型")
    title: str = Field(..., description="图表标题")
    data: Dict[str, Any] = Field(..., description="图表数据")
    config: Dict[str, Any] = Field(default_factory=dict, description="图表配置")


class Report(BaseModel):
    """报告模型"""

    query_id: UUID = Field(..., description="查询ID")
    text_analysis: str = Field(..., description="文本分析")
    visualizations: List[VisualizationSpec] = Field(
        default_factory=list, description="可视化图表"
    )
    raw_data: Optional[QueryResult] = None
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    generated_at: datetime = Field(default_factory=datetime.now)


class AgentResponse(BaseModel):
    """Agent响应模型"""

    agent_type: str = Field(..., description="Agent类型")
    success: bool = True
    data: Optional[Any] = None
    error: Optional[str] = None
    processing_time: float = Field(ge=0.0, description="处理耗时(秒)")
    metadata: Dict[str, Any] = Field(default_factory=dict)
