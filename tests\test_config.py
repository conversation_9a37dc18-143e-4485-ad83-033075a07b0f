"""
配置管理系统测试
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.core.config import (
    SystemConfig, QwenConfig, DatabaseConfig, RedisConfig, 
    AppConfig, <PERSON>naConfig, ConfigManager
)
from src.core.config_validator import ConfigValidator, ConfigHealthChecker
from src.core.config_loader import <PERSON><PERSON>g<PERSON>ileLoader, ConfigMerger, ConfigTemplate


class TestSystemConfig:
    """系统配置测试"""
    
    def test_qwen_config_validation(self):
        """测试千问配置验证"""
        # 有效配置
        config = QwenConfig(api_key="test_api_key_123")
        assert config.api_key == "test_api_key_123"
        assert config.api_base == "https://dashscope.aliyuncs.com/api/v1"
        
        # 无效配置 - 空API密钥
        with pytest.raises(ValueError, match="QWEN_API_KEY is required"):
            QwenConfig(api_key="")
    
    def test_database_config_connection_string(self):
        """测试数据库配置连接字符串生成"""
        config = DatabaseConfig(
            host="localhost",
            port=5432,
            name="test_db",
            user="test_user",
            password="test_pass"
        )
        
        expected = "postgresql://test_user:test_pass@localhost:5432/test_db"
        assert config.connection_string == expected
    
    def test_system_config_from_env(self):
        """测试从环境变量创建系统配置"""
        env_vars = {
            'QWEN_API_KEY': 'test_key_123',
            'QWEN_MODEL': 'qwen-plus',
            'DB_HOST': 'test_host',
            'DB_PORT': '3306',
            'DB_NAME': 'test_db',
            'REDIS_HOST': 'redis_host',
            'APP_DEBUG': 'true',
            'LOG_LEVEL': 'DEBUG'
        }
        
        with patch.dict(os.environ, env_vars):
            config = SystemConfig.from_env()
            
            assert config.qwen.api_key == 'test_key_123'
            assert config.qwen.model == 'qwen-plus'
            assert config.database.host == 'test_host'
            assert config.database.port == 3306
            assert config.database.name == 'test_db'
            assert config.redis.host == 'redis_host'
            assert config.app.debug is True
            assert config.app.log_level == 'DEBUG'
    
    def test_config_validation(self):
        """测试配置验证"""
        # 创建有效配置
        config = SystemConfig(
            qwen=QwenConfig(api_key="valid_key_123"),
            database=DatabaseConfig(password="test_pass"),
            redis=RedisConfig(),
            app=AppConfig(),
            vanna=VannaConfig()
        )
        
        assert config.validate() is True
        
        # 测试无效端口
        config.database.port = 99999
        with pytest.raises(ValueError, match="Invalid database port"):
            config.validate()
    
    def test_config_to_dict(self):
        """测试配置转换为字典"""
        config = SystemConfig(
            qwen=QwenConfig(api_key="secret_key"),
            database=DatabaseConfig(password="secret_pass"),
            redis=RedisConfig(),
            app=AppConfig(),
            vanna=VannaConfig()
        )
        
        config_dict = config.to_dict()
        
        # 检查敏感信息是否被隐藏
        assert config_dict['qwen']['api_key'] == '***'
        assert config_dict['database']['password'] == '***'
        
        # 检查非敏感信息是否正常显示
        assert config_dict['qwen']['model'] == 'qwen-turbo'
        assert config_dict['database']['host'] == 'localhost'


class TestConfigValidator:
    """配置验证器测试"""
    
    def test_validate_api_key(self):
        """测试API密钥验证"""
        assert ConfigValidator.validate_api_key("valid_key_123") is True
        assert ConfigValidator.validate_api_key("") is False
        assert ConfigValidator.validate_api_key("short") is False
        assert ConfigValidator.validate_api_key("key with spaces") is False
    
    def test_validate_url(self):
        """测试URL验证"""
        assert ConfigValidator.validate_url("https://api.example.com") is True
        assert ConfigValidator.validate_url("http://localhost:8000") is True
        assert ConfigValidator.validate_url("invalid_url") is False
        assert ConfigValidator.validate_url("") is False
    
    def test_validate_port(self):
        """测试端口验证"""
        assert ConfigValidator.validate_port(8000) is True
        assert ConfigValidator.validate_port(1) is True
        assert ConfigValidator.validate_port(65535) is True
        assert ConfigValidator.validate_port(0) is False
        assert ConfigValidator.validate_port(65536) is False
    
    def test_validate_host(self):
        """测试主机地址验证"""
        assert ConfigValidator.validate_host("localhost") is True
        assert ConfigValidator.validate_host("127.0.0.1") is True
        assert ConfigValidator.validate_host("0.0.0.0") is True
        assert ConfigValidator.validate_host("") is False
    
    def test_validate_log_level(self):
        """测试日志级别验证"""
        assert ConfigValidator.validate_log_level("INFO") is True
        assert ConfigValidator.validate_log_level("debug") is True  # 应该支持小写
        assert ConfigValidator.validate_log_level("INVALID") is False
    
    def test_validate_model_name(self):
        """测试模型名称验证"""
        assert ConfigValidator.validate_model_name("qwen-turbo") is True
        assert ConfigValidator.validate_model_name("qwen-plus") is True
        assert ConfigValidator.validate_model_name("invalid-model") is False


class TestConfigFileLoader:
    """配置文件加载器测试"""
    
    def test_load_json_config(self):
        """测试JSON配置文件加载"""
        config_data = {
            "qwen": {"api_key": "test_key"},
            "database": {"host": "localhost"}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            import json
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            loaded_config = ConfigFileLoader.load_config_file(temp_path)
            assert loaded_config == config_data
        finally:
            os.unlink(temp_path)
    
    def test_unsupported_file_format(self):
        """测试不支持的文件格式"""
        with tempfile.NamedTemporaryFile(suffix='.txt') as f:
            with pytest.raises(ValueError, match="Unsupported file format"):
                ConfigFileLoader.load_config_file(f.name)
    
    def test_file_not_found(self):
        """测试文件不存在"""
        with pytest.raises(FileNotFoundError):
            ConfigFileLoader.load_config_file("nonexistent.json")


class TestConfigMerger:
    """配置合并器测试"""
    
    def test_merge_configs(self):
        """测试配置合并"""
        config1 = {
            "qwen": {"api_key": "key1", "model": "qwen-turbo"},
            "database": {"host": "host1"}
        }
        
        config2 = {
            "qwen": {"api_key": "key2"},  # 覆盖key1
            "redis": {"host": "redis1"}   # 新增
        }
        
        merged = ConfigMerger.merge_configs(config1, config2)
        
        assert merged["qwen"]["api_key"] == "key2"  # 被覆盖
        assert merged["qwen"]["model"] == "qwen-turbo"  # 保留
        assert merged["database"]["host"] == "host1"  # 保留
        assert merged["redis"]["host"] == "redis1"  # 新增


class TestConfigManager:
    """配置管理器测试"""
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = ConfigManager()
        manager2 = ConfigManager()
        assert manager1 is manager2
    
    def test_config_loading(self):
        """测试配置加载"""
        manager = ConfigManager()
        
        # 模拟环境变量
        with patch.dict(os.environ, {'QWEN_API_KEY': 'test_key'}):
            config = manager.load_config()
            assert config.qwen.api_key == 'test_key'
            
            # 再次获取应该返回相同的配置
            config2 = manager.get_config()
            assert config is config2
    
    def test_config_not_loaded_error(self):
        """测试配置未加载错误"""
        manager = ConfigManager()
        manager._config = None  # 重置配置
        
        with pytest.raises(RuntimeError, match="Configuration not loaded"):
            manager.get_config()


class TestConfigTemplate:
    """配置模板测试"""
    
    def test_generate_config_template(self):
        """测试生成配置模板"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "config.json"
            
            ConfigTemplate.generate_config_template(template_path)
            
            assert template_path.exists()
            
            # 验证模板内容
            loaded_config = ConfigFileLoader.load_config_file(template_path)
            assert "qwen" in loaded_config
            assert "database" in loaded_config
            assert "redis" in loaded_config


# 集成测试
class TestConfigIntegration:
    """配置系统集成测试"""
    
    def test_full_config_workflow(self):
        """测试完整的配置工作流"""
        # 1. 设置环境变量
        env_vars = {
            'QWEN_API_KEY': 'test_integration_key',
            'DB_HOST': 'test_db_host',
            'REDIS_HOST': 'test_redis_host',
            'LOG_LEVEL': 'DEBUG'
        }
        
        with patch.dict(os.environ, env_vars):
            # 2. 加载配置
            config = SystemConfig.from_env()
            
            # 3. 验证配置
            assert config.validate() is True
            
            # 4. 检查配置值
            assert config.qwen.api_key == 'test_integration_key'
            assert config.database.host == 'test_db_host'
            assert config.redis.host == 'test_redis_host'
            assert config.app.log_level == 'DEBUG'
            
            # 5. 验证配置组件
            assert ConfigValidator.validate_api_key(config.qwen.api_key) is True
            assert ConfigValidator.validate_host(config.database.host) is True
            assert ConfigValidator.validate_host(config.redis.host) is True
            assert ConfigValidator.validate_log_level(config.app.log_level) is True


if __name__ == '__main__':
    pytest.main([__file__])