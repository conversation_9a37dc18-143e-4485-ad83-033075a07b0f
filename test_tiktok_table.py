#!/usr/bin/env python3
"""
测试TikTok达人表查询
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.database_executor import CachedDatabaseExecutor

async def test_tiktok_table():
    """测试TikTok达人表"""
    executor = CachedDatabaseExecutor()
    
    try:
        print("🔍 测试TikTok达人表查询...")
        await executor.initialize_pool()
        
        # 1. 检查表是否存在
        print("\n1️⃣ 检查表是否存在...")
        check_table_sql = """
        SELECT COUNT(*) as table_exists 
        FROM information_schema.tables 
        WHERE table_schema = 'test_core' 
        AND table_name = 'at_tiktok_author_pool'
        """
        
        result = await executor.execute_query(check_table_sql)
        if result.data and result.data[0]['table_exists'] > 0:
            print("✅ 表 at_tiktok_author_pool 存在")
        else:
            print("❌ 表 at_tiktok_author_pool 不存在")
            return
        
        # 2. 查看表结构
        print("\n2️⃣ 查看表结构...")
        describe_sql = "DESCRIBE at_tiktok_author_pool"
        result = await executor.execute_query(describe_sql)
        
        if result.data:
            print(f"表结构 ({len(result.data)} 个字段):")
            for row in result.data:
                field_name = row['Field']
                field_type = row['Type']
                field_comment = row.get('Comment', '')
                print(f"  - {field_name}: {field_type} {field_comment}")
        
        # 3. 查看数据总数
        print("\n3️⃣ 查看数据总数...")
        count_sql = "SELECT COUNT(*) as total_count FROM at_tiktok_author_pool"
        result = await executor.execute_query(count_sql)
        
        if result.data:
            total_count = result.data[0]['total_count']
            print(f"表中共有 {total_count} 条数据")
        
        # 4. 查看前几条数据
        print("\n4️⃣ 查看前5条数据...")
        sample_sql = """
        SELECT 
            author_id,
            unique_id,
            author_name,
            follower_count,
            following_count,
            heart_count,
            video_count,
            is_verified,
            region,
            create_time
        FROM at_tiktok_author_pool 
        WHERE is_del = 0 
        ORDER BY follower_count DESC 
        LIMIT 5
        """
        
        result = await executor.execute_query(sample_sql)
        
        if result.data:
            print("前5个达人数据:")
            for i, row in enumerate(result.data, 1):
                print(f"  {i}. {row['author_name']} (@{row['unique_id']})")
                print(f"     粉丝: {row['follower_count']:,}, 获赞: {row['heart_count']:,}")
                print(f"     视频: {row['video_count']}, 认证: {'是' if row['is_verified'] else '否'}")
                print(f"     地区: {row['region']}, 创建时间: {row['create_time']}")
                print()
        
        # 5. 统计分析
        print("5️⃣ 基础统计分析...")
        stats_sql = """
        SELECT 
            COUNT(*) as total_authors,
            SUM(CASE WHEN is_verified = 1 THEN 1 ELSE 0 END) as verified_count,
            SUM(CASE WHEN commerce_user = 1 THEN 1 ELSE 0 END) as commerce_count,
            AVG(follower_count) as avg_followers,
            MAX(follower_count) as max_followers,
            AVG(video_count) as avg_videos,
            COUNT(DISTINCT region) as unique_regions
        FROM at_tiktok_author_pool 
        WHERE is_del = 0
        """
        
        result = await executor.execute_query(stats_sql)
        
        if result.data:
            stats = result.data[0]
            print(f"总达人数: {stats['total_authors']:,}")
            print(f"认证达人: {stats['verified_count']:,} ({stats['verified_count']/stats['total_authors']*100:.1f}%)")
            print(f"电商达人: {stats['commerce_count']:,} ({stats['commerce_count']/stats['total_authors']*100:.1f}%)")
            print(f"平均粉丝数: {stats['avg_followers']:,.0f}")
            print(f"最高粉丝数: {stats['max_followers']:,}")
            print(f"平均视频数: {stats['avg_videos']:.1f}")
            print(f"覆盖地区数: {stats['unique_regions']}")
        
        # 6. 地区分布
        print("\n6️⃣ 地区分布 (前10)...")
        region_sql = """
        SELECT 
            region,
            COUNT(*) as author_count,
            AVG(follower_count) as avg_followers
        FROM at_tiktok_author_pool 
        WHERE is_del = 0 AND region IS NOT NULL
        GROUP BY region 
        ORDER BY author_count DESC 
        LIMIT 10
        """
        
        result = await executor.execute_query(region_sql)
        
        if result.data:
            print("地区分布:")
            for row in result.data:
                print(f"  {row['region']}: {row['author_count']} 人, 平均粉丝 {row['avg_followers']:,.0f}")
        
        print("\n✅ TikTok达人表测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await executor.close()

if __name__ == "__main__":
    asyncio.run(test_tiktok_table())