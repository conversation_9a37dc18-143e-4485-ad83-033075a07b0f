"""
配置管理系统使用示例
演示如何使用TikTok AI Agent的配置管理功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.config import load_config, get_config, SystemConfig
from src.core.config_loader import ConfigTemplate, create_default_config_files


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本配置使用示例 ===")
    
    # 设置测试环境变量
    os.environ['QWEN_API_KEY'] = 'sk-test-key-123456789'
    os.environ['DB_PASSWORD'] = 'test_password'
    
    # 加载配置
    config = load_config()
    
    # 访问配置项
    print(f"千问API密钥: {config.qwen.api_key[:10]}...")
    print(f"数据库连接: {config.database.host}:{config.database.port}")
    print(f"应用端口: {config.app.port}")
    print(f"调试模式: {config.app.debug}")
    
    # 获取全局配置实例
    global_config = get_config()
    print(f"全局配置实例相同: {config is global_config}")


def example_config_validation():
    """配置验证示例"""
    print("\n=== 配置验证示例 ===")
    
    try:
        config = get_config()
        config.validate()
        print("✓ 配置验证通过")
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")


def example_config_dict():
    """配置字典转换示例"""
    print("\n=== 配置字典转换示例 ===")
    
    config = get_config()
    config_dict = config.to_dict()
    
    print("配置结构:")
    for section, values in config_dict.items():
        print(f"  {section}:")
        for key, value in values.items():
            print(f"    {key}: {value}")


def example_template_generation():
    """配置模板生成示例"""
    print("\n=== 配置模板生成示例 ===")
    
    # 生成环境变量模板
    ConfigTemplate.generate_env_template("example.env")
    print("✓ 生成环境变量模板: example.env")
    
    # 生成JSON配置模板
    ConfigTemplate.generate_config_template("example_config.json")
    print("✓ 生成JSON配置模板: example_config.json")


def example_environment_specific():
    """环境特定配置示例"""
    print("\n=== 环境特定配置示例 ===")
    
    # 模拟不同环境的配置
    environments = {
        'development': {
            'DEBUG': 'true',
            'LOG_LEVEL': 'DEBUG',
            'DB_HOST': 'localhost'
        },
        'production': {
            'DEBUG': 'false', 
            'LOG_LEVEL': 'INFO',
            'DB_HOST': 'prod-db.example.com'
        }
    }
    
    for env_name, env_vars in environments.items():
        print(f"\n{env_name.title()} 环境配置:")
        
        # 临时设置环境变量
        original_env = {}
        for key, value in env_vars.items():
            original_env[key] = os.environ.get(key)
            os.environ[key] = value
        
        # 重新加载配置
        from src.core.config import ConfigManager
        manager = ConfigManager()
        config = manager.reload_config()
        
        print(f"  调试模式: {config.app.debug}")
        print(f"  日志级别: {config.app.log_level}")
        print(f"  数据库主机: {config.database.host}")
        
        # 恢复原始环境变量
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


def main():
    """主函数"""
    print("TikTok AI Agent 配置管理系统使用示例\n")
    
    try:
        example_basic_usage()
        example_config_validation()
        example_config_dict()
        example_template_generation()
        example_environment_specific()
        
        print("\n=== 示例执行完成 ===")
        print("配置管理系统功能演示完毕！")
        
    except Exception as e:
        print(f"示例执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()