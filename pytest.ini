[tool:pytest]
# pytest配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --maxfail=10
    --durations=10
    --color=yes

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
    api: API测试
    ui: UI测试
    agent: Agent测试
    core: 核心组件测试
    mock: 使用模拟的测试
    real: 使用真实服务的测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:streamlit.*

# 最小版本要求
minversion = 6.0

# 测试路径
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    node_modules

# 覆盖率配置（如果使用pytest-cov）
# addopts = --cov=src --cov-report=html --cov-report=term-missing

# 并行测试配置（如果使用pytest-xdist）
# addopts = -n auto

# 超时配置（如果使用pytest-timeout）
timeout = 300
timeout_method = thread