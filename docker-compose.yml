version: '3.8'

services:
  # TikTok AI Agent 主服务
  tiktok-ai-agent:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: tiktok-ai-agent
    ports:
      - "8000:8000"  # FastAPI
      - "8501:8501"  # Streamlit UI
    environment:
      # 应用配置
      - APP_NAME=TikTok AI Agent
      - APP_VERSION=1.0.0
      - DEBUG=false
      - LOG_LEVEL=info
      
      # 千问API配置（需要设置真实的API密钥）
      - QWEN_API_KEY=${QWEN_API_KEY:-your_qwen_api_key_here}
      - QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1
      - QWEN_MODEL=qwen-turbo
      - QWEN_TIMEOUT=30
      - QWEN_MAX_RETRIES=3
      
      # 数据库配置
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=tiktok_data
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - DB_POOL_SIZE=10
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - REDIS_PASSWORD=
      
      # Vanna配置
      - VANNA_MODEL=qwen
      - VANNA_DB_TYPE=postgres
      - VANNA_EMBEDDING_DIM=1536
      - VANNA_MAX_CONTEXT=4000
      - VANNA_SIMILARITY_THRESHOLD=0.7
      
      # 启动配置
      - INIT_KNOWLEDGE_BASE=true
      - API_WORKERS=1
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: ["dual"]  # 启动双模式（API + UI）
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: tiktok-postgres
    environment:
      - POSTGRES_DB=tiktok_data
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d tiktok_data"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: tiktok-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: tiktok-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - tiktok-ai-agent
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: tiktok-ai-network