"""数据模型模块"""

from .api import (
    ErrorResponse,
    HealthResponse,
    QueryRequest,
    QueryResponse,
    QueryStatusResponse,
    UserQueryHistory,
)
from .base import APIResponse, BaseEntity, PaginatedResponse
from .knowledge import (
    BusinessDoc,
    RetrievalResult,
    SchemaInfo,
    SQLExample,
    TrainingData,
)
from .query import (
    AgentResponse,
    AnalysisResult,
    IntentType,
    QueryResult,
    QueryStatus,
    Report,
    SQLResult,
    UserQuery,
    VisualizationSpec,
)

__all__ = [
    # Base models
    "BaseEntity",
    "APIResponse", 
    "PaginatedResponse",
    
    # Query models
    "UserQuery",
    "SQLResult",
    "QueryResult",
    "AnalysisResult",
    "VisualizationSpec",
    "Report",
    "AgentResponse",
    "QueryStatus",
    "IntentType",
    
    # Knowledge models
    "SchemaInfo",
    "BusinessDoc",
    "SQLExample",
    "TrainingData",
    "RetrievalResult",
    
    # API models
    "QueryRequest",
    "QueryResponse",
    "QueryStatusResponse",
    "HealthResponse",
    "ErrorResponse",
    "UserQueryHistory",
]