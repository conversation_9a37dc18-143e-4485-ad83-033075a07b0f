"""
TikTok AI Agent 生产版Streamlit界面
集成完整的Agent系统，支持实际数据查询和分析
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import uuid
import sys
import asyncio
import json
from pathlib import Path
from typing import Dict, Any, Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from core.config import get_config
    from agents.agent_coordinator import get_coordinator
    from models.base import APIResponse
    config = get_config()
    CONFIG_LOADED = True
except Exception as e:
    CONFIG_LOADED = False
    CONFIG_ERROR = str(e)

# 页面配置
st.set_page_config(
    page_title="TikTok AI数据分析助手",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #FF6B6B;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
        margin: 1rem 0;
    }
    
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
        margin: 1rem 0;
    }
    
    .info-box {
        background-color: #d1ecf1;
        color: #0c5460;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #bee5eb;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """初始化会话状态"""
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []


def render_header():
    """渲染页面头部"""
    st.markdown('<h1 class="main-header">🎵 TikTok AI数据分析助手</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <p style="font-size: 1.2rem; color: #666;">
            使用自然语言查询TikTok数据，获得智能分析和可视化结果
        </p>
    </div>
    """, unsafe_allow_html=True)


def render_system_status():
    """渲染系统状态"""
    st.subheader("🔧 系统状态")
    
    if CONFIG_LOADED:
        st.markdown('<div class="success-message">✅ 配置加载成功</div>', unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("应用名称", config.app.name)
            st.metric("调试模式", "开启" if config.app.debug else "关闭")
        
        with col2:
            st.metric("千问模型", config.qwen.model)
            api_status = "已配置" if config.qwen.api_key != "your_qwen_api_key_here" else "未配置"
            st.metric("API状态", api_status)
        
        with col3:
            st.metric("数据库类型", config.vanna.db_type)
            st.metric("会话ID", st.session_state.session_id[:8] + "...")
            
        if config.qwen.api_key == "your_qwen_api_key_here":
            st.markdown("""
            <div class="error-message">
                ⚠️ 千问API密钥未配置<br>
                请在 .env 文件中设置 QWEN_API_KEY<br>
                获取地址: <a href="https://bailian.console.aliyun.com/" target="_blank">阿里云百炼平台</a>
            </div>
            """, unsafe_allow_html=True)
    else:
        st.markdown(f'<div class="error-message">❌ 配置加载失败: {CONFIG_ERROR}</div>', unsafe_allow_html=True)


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.header("🔧 功能面板")
        
        # 查询示例
        st.subheader("💡 查询示例")
        examples = [
            "今天涨粉最多的前10个达人是谁？",
            "过去一周哪个游戏达人播放量最高？",
            "美妆类视频的平均点赞率是多少？",
            "各个分类达人的平均粉丝数和视频数是多少？",
            "找出互动率最高的100个视频"
        ]
        
        for i, example in enumerate(examples):
            if st.button(f"📝 示例 {i+1}", key=f"example_{i}"):
                st.session_state.query_input = example
                st.rerun()
        
        st.divider()
        
        # 查询历史
        st.subheader("📚 查询历史")
        if st.session_state.query_history:
            for i, query in enumerate(reversed(st.session_state.query_history[-5:])):
                with st.expander(f"查询 {len(st.session_state.query_history) - i}"):
                    st.write(f"**问题:** {query['question'][:30]}...")
                    st.write(f"**时间:** {query['timestamp']}")
        else:
            st.info("暂无查询历史")
        
        st.divider()
        
        # 系统操作
        st.subheader("⚙️ 系统操作")
        
        if st.button("🔄 重置会话"):
            st.session_state.session_id = str(uuid.uuid4())
            st.session_state.query_history = []
            st.success("会话已重置")
            st.rerun()
        
        if st.button("📊 生成示例数据"):
            generate_sample_data()
            st.success("示例数据已生成")


def render_query_interface():
    """渲染查询界面"""
    st.subheader("🔍 数据查询")
    
    # 查询输入框
    query_input = st.text_area(
        "请输入您的问题:",
        value=st.session_state.get('query_input', ''),
        height=100,
        placeholder="例如：今天涨粉最多的前10个达人是谁？",
        key="query_textarea"
    )
    
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        if st.button("🚀 开始分析", type="primary", use_container_width=True):
            if query_input.strip():
                if CONFIG_LOADED:
                    asyncio.run(process_query_with_agent(query_input.strip()))
                else:
                    st.error("系统配置未加载，无法处理查询")
            else:
                st.warning("请输入您的问题")
    
    with col2:
        if st.button("🧹 清空输入", use_container_width=True):
            st.session_state.query_input = ""
            st.rerun()
    
    with col3:
        if st.button("📋 示例查询", use_container_width=True):
            st.session_state.query_input = "今天涨粉最多的前10个达人是谁？"
            st.rerun()


async def process_query_with_agent(question: str):
    """使用Agent系统处理查询"""
    # 添加到历史记录
    st.session_state.query_history.append({
        'question': question,
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })
    
    # 创建进度条
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # 获取协调器
        status_text.text("🔧 初始化Agent系统...")
        progress_bar.progress(0.1)
        
        coordinator = await get_coordinator()
        
        # 处理用户请求
        status_text.text("🤖 正在分析您的问题...")
        progress_bar.progress(0.3)
        
        response = await coordinator.process_user_request(
            user_input=question,
            session_id=st.session_state.session_id,
            user_id=None
        )
        
        progress_bar.progress(0.9)
        status_text.text("✅ 分析完成！")
        progress_bar.progress(1.0)
        
        # 清除进度指示器
        progress_bar.empty()
        status_text.empty()
        
        if response.success:
            render_query_results(question, response)
        else:
            st.error(f"查询处理失败: {response.message}")
            if response.error:
                st.error(f"错误详情: {response.error}")
                
    except Exception as e:
        progress_bar.empty()
        status_text.empty()
        st.error(f"系统错误: {str(e)}")
        st.error("请检查系统配置和网络连接")


def render_query_results(question: str, response: APIResponse):
    """渲染查询结果"""
    st.success("✅ 查询处理完成！")
    
    st.subheader("📊 分析结果")
    st.write(f"**您的问题:** {question}")
    
    data = response.data
    
    if data and data.get("type") == "complete_analysis":
        # 显示SQL查询
        if data.get("sql"):
            with st.expander("🔍 生成的SQL查询"):
                st.code(data["sql"], language='sql')
        
        # 显示原始数据
        raw_data = data.get("raw_data", {})
        if raw_data.get("data"):
            st.subheader("📋 查询数据")
            
            # 检查是否是模拟数据
            if raw_data.get("is_mock", False):
                st.info("ℹ️ 当前显示的是模拟数据，用于演示系统功能")
            
            df = pd.DataFrame(raw_data["data"])
            if not df.empty:
                st.dataframe(df, use_container_width=True)
                
                # 显示数据统计
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("数据行数", raw_data.get("row_count", len(df)))
                with col2:
                    st.metric("查询耗时", f"{raw_data.get('execution_time', 0):.2f}秒")
                with col3:
                    st.metric("数据列数", len(df.columns))
        
        # 显示可视化
        visualizations = data.get("visualizations", [])
        if visualizations:
            st.subheader("📈 数据可视化")
            
            for viz in visualizations:
                st.write(f"**{viz.get('title', '图表')}**")
                if viz.get('description'):
                    st.write(viz['description'])
                
                try:
                    # 执行可视化代码
                    exec_globals = {
                        'df': df,
                        'px': px,
                        'go': go,
                        'st': st,
                        'pd': pd
                    }
                    exec(viz.get('code', ''), exec_globals)
                except Exception as e:
                    st.error(f"可视化生成失败: {str(e)}")
        
        # 显示分析结果
        analysis = data.get("analysis", {})
        if analysis:
            st.subheader("📝 智能分析")
            
            # 分析摘要
            if analysis.get("summary"):
                st.write("**分析摘要:**")
                st.write(analysis["summary"])
            
            # 关键洞察
            insights = analysis.get("insights", [])
            if insights:
                st.write("**关键洞察:**")
                for insight in insights:
                    st.write(f"• {insight}")
            
            # 关键指标
            key_metrics = analysis.get("key_metrics", {})
            if key_metrics:
                st.write("**关键指标:**")
                cols = st.columns(min(len(key_metrics), 4))
                for i, (metric, value) in enumerate(key_metrics.items()):
                    with cols[i % len(cols)]:
                        st.metric(metric, value)
            
            # 建议
            recommendations = analysis.get("recommendations", [])
            if recommendations:
                st.subheader("💡 建议")
                for rec in recommendations:
                    st.write(f"• {rec}")
    
    elif data and data.get("type") == "chat_response":
        # 处理聊天回复
        st.subheader("💬 AI回复")
        st.write(data.get("response", "抱歉，我无法理解您的问题。"))
    
    else:
        st.warning("未收到有效的分析结果")


def process_query_demo(question: str):
    """演示查询处理"""
    # 添加到历史记录
    st.session_state.query_history.append({
        'question': question,
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    })
    
    with st.spinner("正在分析您的问题..."):
        import time
        time.sleep(2)  # 模拟处理时间
        
        # 生成演示结果
        st.success("✅ 查询处理完成！")
        
        st.subheader("📊 分析结果")
        
        # 显示问题分析
        st.write(f"**您的问题:** {question}")
        
        # 模拟SQL生成
        if "涨粉" in question:
            sql = """
            SELECT c.username, c.display_name, 
                   dm.follower_growth as daily_growth
            FROM creators c
            JOIN daily_metrics dm ON c.id = dm.creator_id
            WHERE dm.date = CURRENT_DATE
            ORDER BY dm.follower_growth DESC
            LIMIT 10;
            """
        elif "播放量" in question:
            sql = """
            SELECT c.username, c.display_name, 
                   SUM(v.view_count) as total_views
            FROM creators c
            JOIN videos v ON c.id = v.creator_id
            WHERE v.created_at >= CURRENT_DATE - INTERVAL '7 days'
              AND c.category = 'gaming'
            GROUP BY c.id, c.username, c.display_name
            ORDER BY total_views DESC
            LIMIT 1;
            """
        else:
            sql = "SELECT * FROM creators LIMIT 10;"
        
        with st.expander("🔍 生成的SQL查询"):
            st.code(sql, language='sql')
        
        # 生成示例数据
        if "涨粉" in question:
            data = [
                {"用户名": "gamer_king", "显示名称": "游戏王者", "今日涨粉": 15420},
                {"用户名": "beauty_queen", "显示名称": "美妆女王", "今日涨粉": 12350},
                {"用户名": "dance_star", "显示名称": "舞蹈之星", "今日涨粉": 9870},
                {"用户名": "food_lover", "显示名称": "美食达人", "今日涨粉": 8650},
                {"用户名": "music_master", "显示名称": "音乐大师", "今日涨粉": 7430}
            ]
        elif "播放量" in question:
            data = [
                {"用户名": "pro_gamer", "显示名称": "职业玩家", "总播放量": 2580000},
                {"用户名": "game_streamer", "显示名称": "游戏主播", "总播放量": 1950000},
                {"用户名": "esports_star", "显示名称": "电竞明星", "总播放量": 1720000}
            ]
        else:
            data = [
                {"ID": 1, "用户名": "user1", "粉丝数": 100000, "分类": "游戏"},
                {"ID": 2, "用户名": "user2", "粉丝数": 85000, "分类": "美妆"},
                {"ID": 3, "用户名": "user3", "粉丝数": 92000, "分类": "舞蹈"}
            ]
        
        # 显示数据表格
        df = pd.DataFrame(data)
        st.dataframe(df, use_container_width=True)
        
        # 生成图表
        if len(df) > 1:
            st.subheader("📈 数据可视化")
            
            if "涨粉" in question:
                fig = px.bar(df, x="显示名称", y="今日涨粉", 
                           title="今日涨粉排行榜",
                           color="今日涨粉",
                           color_continuous_scale="viridis")
            elif "播放量" in question:
                fig = px.bar(df, x="显示名称", y="总播放量",
                           title="游戏达人播放量排行",
                           color="总播放量",
                           color_continuous_scale="plasma")
            else:
                fig = px.bar(df, x="用户名", y="粉丝数",
                           title="达人粉丝数统计",
                           color="分类")
            
            st.plotly_chart(fig, use_container_width=True)
        
        # 分析摘要
        st.subheader("📝 分析摘要")
        if "涨粉" in question:
            st.write("根据今日数据分析，游戏王者(gamer_king)以15,420的涨粉量位居第一，显示出游戏内容在当前具有很强的吸引力。")
        elif "播放量" in question:
            st.write("过去一周游戏分类中，职业玩家(pro_gamer)以258万的总播放量领先，说明专业游戏内容更受用户欢迎。")
        else:
            st.write("当前数据显示各分类达人都有不错的粉丝基础，其中游戏和舞蹈分类表现较为突出。")
        
        # 建议
        st.subheader("💡 建议")
        st.write("• 关注高涨粉达人的内容策略，学习其成功经验")
        st.write("• 游戏分类内容具有较高的用户参与度，可考虑相关合作")
        st.write("• 建议定期监控数据变化，及时调整内容策略")


def generate_sample_data():
    """生成示例数据展示"""
    st.subheader("📊 示例数据展示")
    
    # 创建示例达人数据
    creators_data = {
        "用户名": ["gamer_pro", "beauty_guru", "dance_star", "food_master", "music_lover"],
        "显示名称": ["游戏专家", "美妆导师", "舞蹈明星", "美食大师", "音乐爱好者"],
        "粉丝数": [150000, 120000, 98000, 85000, 76000],
        "分类": ["游戏", "美妆", "舞蹈", "美食", "音乐"],
        "认证状态": ["已认证", "已认证", "未认证", "已认证", "未认证"]
    }
    
    df = pd.DataFrame(creators_data)
    st.dataframe(df, use_container_width=True)
    
    # 生成图表
    col1, col2 = st.columns(2)
    
    with col1:
        fig1 = px.bar(df, x="显示名称", y="粉丝数", 
                     title="达人粉丝数排行",
                     color="分类")
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        fig2 = px.pie(df, values="粉丝数", names="分类",
                     title="各分类粉丝数分布")
        st.plotly_chart(fig2, use_container_width=True)


def main():
    """主函数"""
    # 初始化会话状态
    initialize_session_state()
    
    # 渲染页面
    render_header()
    
    # 系统状态
    render_system_status()
    
    st.divider()
    
    # 侧边栏
    render_sidebar()
    
    # 主要内容区域
    render_query_interface()
    
    st.divider()
    
    # 页脚
    st.markdown("---")
    st.markdown(
        "<div style='text-align: center; color: #666;'>"
        "TikTok AI数据分析助手 | 基于Vanna和千问模型构建 | 生产环境"
        "</div>",
        unsafe_allow_html=True
    )


if __name__ == "__main__":
    main()