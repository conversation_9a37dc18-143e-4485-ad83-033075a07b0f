"""
展示Agent实现
负责数据分析和可视化报告生成
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import json
import re

try:
    from ..core.qwen_text_generator import QwenTextGenerator
    from ..models.query import QueryResult
    from ..models.base import APIResponse
except ImportError:
    # 绝对导入作为备选
    from core.qwen_text_generator import QwenTextGenerator
    from models.query import QueryResult
    from models.base import APIResponse

logger = logging.getLogger(__name__)


class VisualizationType:
    """可视化类型常量"""
    BAR_CHART = "bar_chart"
    LINE_CHART = "line_chart"
    PIE_CHART = "pie_chart"
    SCATTER_PLOT = "scatter_plot"
    TABLE = "table"
    METRIC_CARD = "metric_card"


class AnalysisResult:
    """数据分析结果"""
    
    def __init__(self, summary: str, insights: List[str], 
                 recommendations: List[str], key_metrics: Dict[str, Any]):
        """
        初始化分析结果
        
        Args:
            summary: 数据摘要
            insights: 数据洞察
            recommendations: 建议
            key_metrics: 关键指标
        """
        self.summary = summary
        self.insights = insights
        self.recommendations = recommendations
        self.key_metrics = key_metrics
        self.generated_at = datetime.now()


class VisualizationCode:
    """可视化代码"""
    
    def __init__(self, chart_type: str, code: str, title: str, description: str):
        """
        初始化可视化代码
        
        Args:
            chart_type: 图表类型
            code: Python代码
            title: 图表标题
            description: 图表描述
        """
        self.chart_type = chart_type
        self.code = code
        self.title = title
        self.description = description
        self.generated_at = datetime.now()


class Report:
    """完整报告"""
    
    def __init__(self, analysis: AnalysisResult, visualizations: List[VisualizationCode],
                 raw_data: QueryResult):
        """
        初始化报告
        
        Args:
            analysis: 分析结果
            visualizations: 可视化列表
            raw_data: 原始数据
        """
        self.analysis = analysis
        self.visualizations = visualizations
        self.raw_data = raw_data
        self.generated_at = datetime.now()


class DisplayAgent:
    """展示Agent - 数据分析和可视化报告生成"""
    
    def __init__(self):
        """初始化展示Agent"""
        self.text_generator = QwenTextGenerator()
        
        # 分析配置
        self.max_insights = 5
        self.max_recommendations = 3
        
        logger.info("DisplayAgent initialized")
    
    async def analyze_data(self, query_result: QueryResult, 
                          original_question: str = "") -> AnalysisResult:
        """
        分析数据并生成洞察
        
        Args:
            query_result: 查询结果
            original_question: 原始问题
            
        Returns:
            AnalysisResult: 分析结果
        """
        try:
            logger.info("Analyzing query result data")
            
            if not query_result.data:
                return AnalysisResult(
                    summary="查询未返回任何数据",
                    insights=["数据集为空，无法进行分析"],
                    recommendations=["请检查查询条件或数据源"],
                    key_metrics={}
                )
            
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(query_result, original_question)
            
            # 使用千问模型生成分析
            analysis_text = await self.text_generator.generate_analysis(analysis_prompt)
            
            if analysis_text:
                # 解析分析结果
                parsed_analysis = self._parse_analysis_result(analysis_text)
            else:
                # 回退到基础分析
                parsed_analysis = self._generate_basic_analysis(query_result)
            
            # 计算关键指标
            key_metrics = self._calculate_key_metrics(query_result)
            parsed_analysis["key_metrics"] = key_metrics
            
            return AnalysisResult(
                summary=parsed_analysis.get("summary", "数据分析完成"),
                insights=parsed_analysis.get("insights", []),
                recommendations=parsed_analysis.get("recommendations", []),
                key_metrics=key_metrics
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze data: {e}")
            return AnalysisResult(
                summary="数据分析过程中发生错误",
                insights=[f"分析错误: {str(e)}"],
                recommendations=["请重试或联系技术支持"],
                key_metrics={}
            )
    
    async def generate_visualization(self, query_result: QueryResult,
                                   chart_type: Optional[str] = None) -> List[VisualizationCode]:
        """
        生成数据可视化代码
        
        Args:
            query_result: 查询结果
            chart_type: 指定图表类型
            
        Returns:
            List[VisualizationCode]: 可视化代码列表
        """
        try:
            logger.info("Generating visualization code")
            
            if not query_result.data:
                return []
            
            # 自动选择合适的图表类型
            if not chart_type:
                chart_type = self._auto_select_chart_type(query_result)
            
            visualizations = []
            
            # 生成主要图表
            main_viz = await self._generate_chart_code(query_result, chart_type)
            if main_viz:
                visualizations.append(main_viz)
            
            # 如果数据适合，生成额外的图表
            additional_charts = self._suggest_additional_charts(query_result, chart_type)
            for additional_type in additional_charts:
                additional_viz = await self._generate_chart_code(query_result, additional_type)
                if additional_viz:
                    visualizations.append(additional_viz)
            
            return visualizations
            
        except Exception as e:
            logger.error(f"Failed to generate visualization: {e}")
            return []
    
    async def create_report(self, query_result: QueryResult, 
                          original_question: str = "") -> Report:
        """
        创建完整的数据分析报告
        
        Args:
            query_result: 查询结果
            original_question: 原始问题
            
        Returns:
            Report: 完整报告
        """
        try:
            logger.info("Creating comprehensive data report")
            
            # 并行生成分析和可视化
            analysis_task = self.analyze_data(query_result, original_question)
            visualization_task = self.generate_visualization(query_result)
            
            analysis, visualizations = await asyncio.gather(
                analysis_task, visualization_task
            )
            
            return Report(
                analysis=analysis,
                visualizations=visualizations,
                raw_data=query_result
            )
            
        except Exception as e:
            logger.error(f"Failed to create report: {e}")
            # 返回基础报告
            basic_analysis = AnalysisResult(
                summary="报告生成过程中发生错误",
                insights=[f"错误: {str(e)}"],
                recommendations=["请重试或联系技术支持"],
                key_metrics={}
            )
            return Report(
                analysis=basic_analysis,
                visualizations=[],
                raw_data=query_result
            )
    
    def _build_analysis_prompt(self, query_result: QueryResult, original_question: str) -> str:
        """构建数据分析提示词"""
        data_sample = query_result.data[:5]  # 取前5行作为样本
        
        prompt_parts = [
            "请分析以下TikTok数据查询结果，提供深入的数据洞察和建议。",
            "",
            f"原始问题: {original_question}" if original_question else "",
            f"数据行数: {query_result.row_count}",
            f"执行时间: {query_result.execution_time:.3f}秒",
            "",
            "数据样本:",
            json.dumps(data_sample, ensure_ascii=False, indent=2),
            "",
            "请按以下格式返回分析结果:",
            "",
            "## 数据摘要",
            "[简要描述数据的主要内容和特征]",
            "",
            "## 关键洞察",
            "1. [洞察1]",
            "2. [洞察2]",
            "3. [洞察3]",
            "",
            "## 建议",
            "1. [建议1]",
            "2. [建议2]",
            "",
            "请确保分析专业、准确，并提供有价值的商业洞察。"
        ]
        
        return "\n".join(filter(None, prompt_parts))
    
    def _parse_analysis_result(self, analysis_text: str) -> Dict[str, Any]:
        """解析分析结果文本"""
        try:
            result = {
                "summary": "",
                "insights": [],
                "recommendations": []
            }
            
            # 提取摘要
            summary_match = re.search(r'## 数据摘要\s*\n(.*?)(?=\n##|\n$)', analysis_text, re.DOTALL)
            if summary_match:
                result["summary"] = summary_match.group(1).strip()
            
            # 提取洞察
            insights_match = re.search(r'## 关键洞察\s*\n(.*?)(?=\n##|\n$)', analysis_text, re.DOTALL)
            if insights_match:
                insights_text = insights_match.group(1).strip()
                insights = re.findall(r'\d+\.\s*(.*?)(?=\n\d+\.|\n$)', insights_text, re.DOTALL)
                result["insights"] = [insight.strip() for insight in insights]
            
            # 提取建议
            recommendations_match = re.search(r'## 建议\s*\n(.*?)(?=\n##|\n$)', analysis_text, re.DOTALL)
            if recommendations_match:
                recommendations_text = recommendations_match.group(1).strip()
                recommendations = re.findall(r'\d+\.\s*(.*?)(?=\n\d+\.|\n$)', recommendations_text, re.DOTALL)
                result["recommendations"] = [rec.strip() for rec in recommendations]
            
            return result
            
        except Exception as e:
            logger.warning(f"Failed to parse analysis result: {e}")
            return self._generate_basic_analysis(None)
    
    def _generate_basic_analysis(self, query_result: Optional[QueryResult]) -> Dict[str, Any]:
        """生成基础分析（回退方案）"""
        if not query_result or not query_result.data:
            return {
                "summary": "查询未返回数据",
                "insights": ["数据集为空"],
                "recommendations": ["检查查询条件"]
            }
        
        data = query_result.data
        columns = query_result.columns
        
        insights = []
        recommendations = []
        
        # 基础统计分析
        insights.append(f"查询返回了 {len(data)} 条记录")
        
        # 分析数值列
        numeric_columns = []
        for col in columns:
            if data and isinstance(data[0].get(col), (int, float)):
                numeric_columns.append(col)
        
        if numeric_columns:
            insights.append(f"数据包含 {len(numeric_columns)} 个数值字段: {', '.join(numeric_columns)}")
        
        # 分析数据分布
        if len(data) > 1:
            insights.append("数据显示了多个实体的对比情况")
            recommendations.append("可以进一步分析数据的分布特征和趋势")
        
        if len(data) >= 10:
            recommendations.append("数据量充足，适合进行深入的统计分析")
        
        return {
            "summary": f"成功查询到 {len(data)} 条TikTok相关数据记录",
            "insights": insights,
            "recommendations": recommendations
        }
    
    def _calculate_key_metrics(self, query_result: QueryResult) -> Dict[str, Any]:
        """计算关键指标"""
        try:
            if not query_result.data:
                return {}
            
            data = query_result.data
            metrics = {
                "total_records": len(data),
                "execution_time": query_result.execution_time
            }
            
            # 计算数值列的统计信息
            for col in query_result.columns:
                values = [row.get(col) for row in data if isinstance(row.get(col), (int, float))]
                
                if values:
                    metrics[f"{col}_sum"] = sum(values)
                    metrics[f"{col}_avg"] = sum(values) / len(values)
                    metrics[f"{col}_max"] = max(values)
                    metrics[f"{col}_min"] = min(values)
            
            return metrics
            
        except Exception as e:
            logger.warning(f"Failed to calculate key metrics: {e}")
            return {"total_records": len(query_result.data) if query_result.data else 0}
    
    def _auto_select_chart_type(self, query_result: QueryResult) -> str:
        """自动选择合适的图表类型"""
        try:
            data = query_result.data
            columns = query_result.columns
            
            if not data or len(data) == 0:
                return VisualizationType.TABLE
            
            # 分析数据特征
            numeric_cols = []
            categorical_cols = []
            
            for col in columns:
                sample_value = data[0].get(col)
                if isinstance(sample_value, (int, float)):
                    numeric_cols.append(col)
                else:
                    categorical_cols.append(col)
            
            # 根据数据特征选择图表类型
            if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
                # 有分类和数值数据，适合柱状图
                return VisualizationType.BAR_CHART
            elif len(numeric_cols) >= 2:
                # 多个数值列，适合散点图
                return VisualizationType.SCATTER_PLOT
            elif len(categorical_cols) >= 1 and len(data) <= 10:
                # 少量分类数据，适合饼图
                return VisualizationType.PIE_CHART
            elif len(data) > 20:
                # 大量数据，适合折线图
                return VisualizationType.LINE_CHART
            else:
                # 默认使用柱状图
                return VisualizationType.BAR_CHART
                
        except Exception as e:
            logger.warning(f"Failed to auto-select chart type: {e}")
            return VisualizationType.BAR_CHART
    
    async def _generate_chart_code(self, query_result: QueryResult, 
                                 chart_type: str) -> Optional[VisualizationCode]:
        """生成图表代码"""
        try:
            # 构建可视化提示词
            viz_prompt = self._build_visualization_prompt(query_result, chart_type)
            
            # 使用千问模型生成代码
            code = await self.text_generator.generate_visualization_code(viz_prompt)
            
            if code:
                # 清理和验证代码
                cleaned_code = self._clean_visualization_code(code)
                
                return VisualizationCode(
                    chart_type=chart_type,
                    code=cleaned_code,
                    title=self._generate_chart_title(query_result, chart_type),
                    description=self._generate_chart_description(query_result, chart_type)
                )
            else:
                # 回退到模板代码
                return self._generate_template_chart(query_result, chart_type)
                
        except Exception as e:
            logger.error(f"Failed to generate chart code: {e}")
            return self._generate_template_chart(query_result, chart_type)
    
    def _build_visualization_prompt(self, query_result: QueryResult, chart_type: str) -> str:
        """构建可视化提示词"""
        data_sample = query_result.data[:3]  # 取前3行作为样本
        
        prompt_parts = [
            f"请为以下TikTok数据生成{chart_type}类型的plotly可视化代码。",
            "",
            "数据结构:",
            f"列名: {', '.join(query_result.columns)}",
            f"数据行数: {query_result.row_count}",
            "",
            "数据样本:",
            json.dumps(data_sample, ensure_ascii=False, indent=2),
            "",
            "要求:",
            "1. 使用plotly.express或plotly.graph_objects",
            "2. 代码应该完整可执行",
            "3. 包含适当的标题和标签",
            "4. 使用中文标签",
            "5. 只返回Python代码，不要包含解释",
            "",
            "假设数据已经存在于变量'data'中（List[Dict]格式）。"
        ]
        
        return "\n".join(prompt_parts)
    
    def _clean_visualization_code(self, code: str) -> str:
        """清理可视化代码"""
        try:
            # 移除markdown标记
            code = re.sub(r'```python\s*', '', code)
            code = re.sub(r'```\s*$', '', code)
            
            # 确保导入语句
            if 'import plotly' not in code:
                code = "import plotly.express as px\nimport pandas as pd\n\n" + code
            
            # 确保数据转换
            if 'pd.DataFrame(data)' not in code and 'df =' not in code:
                code = "df = pd.DataFrame(data)\n" + code
            
            return code.strip()
            
        except Exception as e:
            logger.warning(f"Failed to clean visualization code: {e}")
            return code
    
    def _generate_template_chart(self, query_result: QueryResult, 
                               chart_type: str) -> VisualizationCode:
        """生成模板图表代码"""
        try:
            data = query_result.data
            columns = query_result.columns
            
            if chart_type == VisualizationType.BAR_CHART:
                code = self._generate_bar_chart_template(columns)
            elif chart_type == VisualizationType.LINE_CHART:
                code = self._generate_line_chart_template(columns)
            elif chart_type == VisualizationType.PIE_CHART:
                code = self._generate_pie_chart_template(columns)
            else:
                code = self._generate_table_template(columns)
            
            return VisualizationCode(
                chart_type=chart_type,
                code=code,
                title=self._generate_chart_title(query_result, chart_type),
                description=self._generate_chart_description(query_result, chart_type)
            )
            
        except Exception as e:
            logger.error(f"Failed to generate template chart: {e}")
            return VisualizationCode(
                chart_type=VisualizationType.TABLE,
                code="# 图表生成失败\nprint('无法生成图表')",
                title="数据展示",
                description="图表生成失败"
            )
    
    def _generate_bar_chart_template(self, columns: List[str]) -> str:
        """生成柱状图模板"""
        x_col = columns[0] if columns else "x"
        y_col = columns[1] if len(columns) > 1 else columns[0]
        
        return f"""import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.bar(df, x='{x_col}', y='{y_col}', 
             title='TikTok数据分析 - 柱状图',
             labels={{'{x_col}': '{x_col}', '{y_col}': '{y_col}'}})
fig.update_layout(xaxis_tickangle=-45)
fig.show()"""
    
    def _generate_line_chart_template(self, columns: List[str]) -> str:
        """生成折线图模板"""
        x_col = columns[0] if columns else "x"
        y_col = columns[1] if len(columns) > 1 else columns[0]
        
        return f"""import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.line(df, x='{x_col}', y='{y_col}',
              title='TikTok数据趋势 - 折线图',
              labels={{'{x_col}': '{x_col}', '{y_col}': '{y_col}'}})
fig.show()"""
    
    def _generate_pie_chart_template(self, columns: List[str]) -> str:
        """生成饼图模板"""
        names_col = columns[0] if columns else "names"
        values_col = columns[1] if len(columns) > 1 else columns[0]
        
        return f"""import plotly.express as px
import pandas as pd

df = pd.DataFrame(data)
fig = px.pie(df, names='{names_col}', values='{values_col}',
             title='TikTok数据分布 - 饼图')
fig.show()"""
    
    def _generate_table_template(self, columns: List[str]) -> str:
        """生成表格模板"""
        return """import plotly.graph_objects as go
import pandas as pd

df = pd.DataFrame(data)
fig = go.Figure(data=[go.Table(
    header=dict(values=list(df.columns),
                fill_color='paleturquoise',
                align='left'),
    cells=dict(values=[df[col] for col in df.columns],
               fill_color='lavender',
               align='left'))
])
fig.update_layout(title='TikTok数据表格')
fig.show()"""
    
    def _generate_chart_title(self, query_result: QueryResult, chart_type: str) -> str:
        """生成图表标题"""
        type_names = {
            VisualizationType.BAR_CHART: "柱状图",
            VisualizationType.LINE_CHART: "折线图",
            VisualizationType.PIE_CHART: "饼图",
            VisualizationType.SCATTER_PLOT: "散点图",
            VisualizationType.TABLE: "数据表格"
        }
        
        chart_name = type_names.get(chart_type, "数据图表")
        return f"TikTok数据分析 - {chart_name}"
    
    def _generate_chart_description(self, query_result: QueryResult, chart_type: str) -> str:
        """生成图表描述"""
        descriptions = {
            VisualizationType.BAR_CHART: f"展示了 {query_result.row_count} 条记录的对比分析",
            VisualizationType.LINE_CHART: f"显示了 {query_result.row_count} 个数据点的趋势变化",
            VisualizationType.PIE_CHART: f"展示了 {query_result.row_count} 个类别的分布情况",
            VisualizationType.SCATTER_PLOT: f"分析了 {query_result.row_count} 个数据点的相关性",
            VisualizationType.TABLE: f"详细展示了 {query_result.row_count} 条数据记录"
        }
        
        return descriptions.get(chart_type, f"展示了 {query_result.row_count} 条数据")
    
    def _suggest_additional_charts(self, query_result: QueryResult, 
                                 main_chart_type: str) -> List[str]:
        """建议额外的图表类型"""
        suggestions = []
        
        if len(query_result.data) > 5 and main_chart_type != VisualizationType.TABLE:
            suggestions.append(VisualizationType.TABLE)
        
        if len(query_result.data) <= 10 and main_chart_type != VisualizationType.PIE_CHART:
            suggestions.append(VisualizationType.PIE_CHART)
        
        return suggestions[:2]  # 最多2个额外图表
    
    async def close(self):
        """关闭资源"""
        if self.text_generator:
            await self.text_generator.close()
        logger.info("DisplayAgent closed")