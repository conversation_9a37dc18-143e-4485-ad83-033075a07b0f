"""
系统管理路由
提供系统管理和维护接口
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from ...agents.agent_coordinator import get_coordinator
from ...core.config import get_config

logger = logging.getLogger(__name__)

router = APIRouter()


class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    status: str
    timestamp: str
    components: Dict[str, Any]
    performance: Dict[str, Any]


class ConfigResponse(BaseModel):
    """配置响应模型"""
    app: Dict[str, Any]
    database: Dict[str, Any]
    vanna: Dict[str, Any]
    redis: Dict[str, Any]


class KnowledgeBaseStats(BaseModel):
    """知识库统计模型"""
    total_items: int
    schemas: Dict[str, Any]
    business_docs: Dict[str, Any]
    sql_examples: Dict[str, Any]
    embedding_coverage: Dict[str, Any]


class TaskCleanupRequest(BaseModel):
    """任务清理请求模型"""
    max_age_hours: int = Field(24, ge=1, le=168, description="任务最大保留时间（小时）")


# 简单的认证依赖（生产环境应使用更安全的认证方式）
async def verify_admin_token(token: str = None):
    """验证管理员令牌"""
    # 这里应该实现真正的认证逻辑
    # 目前为了演示，暂时跳过认证
    return True


@router.get("/status", response_model=SystemStatusResponse, summary="获取系统状态")
async def get_system_status(admin: bool = Depends(verify_admin_token)):
    """
    获取系统详细状态
    
    返回系统各个组件的详细状态信息，包括性能指标。
    """
    try:
        coordinator = await get_coordinator()
        config = get_config()
        
        # 获取系统状态
        system_status = coordinator.get_system_status()
        
        # 获取组件状态
        components = {
            "agent_coordinator": {
                "status": "running" if system_status.get("task_processor_running") else "stopped",
                "active_tasks": system_status.get("active_tasks", 0),
                "pending_tasks": system_status.get("pending_tasks", 0),
                "active_sessions": system_status.get("active_sessions", 0)
            },
            "vanna_core": {
                "status": "available",
                "knowledge_base": "connected"
            },
            "qwen_service": {
                "status": "available",
                "text_generation": "enabled",
                "embedding": "enabled"
            }
        }
        
        # 性能指标
        performance = {
            "completed_tasks": system_status.get("completed_tasks", 0),
            "failed_tasks": system_status.get("failed_tasks", 0),
            "success_rate": _calculate_success_rate(system_status),
            "average_response_time": "N/A"  # 需要实现响应时间统计
        }
        
        return SystemStatusResponse(
            status="healthy",
            timestamp=datetime.now().isoformat(),
            components=components,
            performance=performance
        )
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system status")


@router.get("/config", response_model=ConfigResponse, summary="获取系统配置")
async def get_system_config(admin: bool = Depends(verify_admin_token)):
    """
    获取系统配置信息
    
    返回系统的配置信息（敏感信息已脱敏）。
    """
    try:
        config = get_config()
        config_dict = config.to_dict()
        
        return ConfigResponse(
            app=config_dict.get("app", {}),
            database=config_dict.get("database", {}),
            vanna=config_dict.get("vanna", {}),
            redis=config_dict.get("redis", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to get system config: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system config")


@router.get("/knowledge-base/stats", response_model=KnowledgeBaseStats, summary="获取知识库统计")
async def get_knowledge_base_stats(admin: bool = Depends(verify_admin_token)):
    """
    获取知识库统计信息
    
    返回知识库中各类数据的统计信息。
    """
    try:
        coordinator = await get_coordinator()
        kb_manager = coordinator.vanna_core.kb_manager
        
        stats = await kb_manager.get_stats()
        
        return KnowledgeBaseStats(
            total_items=stats.get("total_items", 0),
            schemas=stats.get("schemas", {}),
            business_docs=stats.get("business_docs", {}),
            sql_examples=stats.get("sql_examples", {}),
            embedding_coverage=stats.get("embedding_coverage", {})
        )
        
    except Exception as e:
        logger.error(f"Failed to get knowledge base stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get knowledge base stats")


@router.post("/knowledge-base/rebuild", summary="重建知识库")
async def rebuild_knowledge_base(background_tasks: BackgroundTasks, 
                               admin: bool = Depends(verify_admin_token)):
    """
    重建知识库
    
    清空现有知识库并重新加载训练数据。
    """
    try:
        # 在后台执行重建任务
        background_tasks.add_task(_rebuild_knowledge_base_task)
        
        return {
            "success": True,
            "message": "Knowledge base rebuild started",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to start knowledge base rebuild: {e}")
        raise HTTPException(status_code=500, detail="Failed to start knowledge base rebuild")


@router.post("/knowledge-base/generate-embeddings", summary="生成嵌入向量")
async def generate_embeddings(background_tasks: BackgroundTasks,
                            admin: bool = Depends(verify_admin_token)):
    """
    为知识库中的所有项目生成嵌入向量
    
    为没有嵌入向量的知识库项目生成嵌入向量。
    """
    try:
        # 在后台执行嵌入生成任务
        background_tasks.add_task(_generate_embeddings_task)
        
        return {
            "success": True,
            "message": "Embedding generation started",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to start embedding generation: {e}")
        raise HTTPException(status_code=500, detail="Failed to start embedding generation")


@router.post("/tasks/cleanup", summary="清理旧任务")
async def cleanup_old_tasks(request: TaskCleanupRequest,
                          admin: bool = Depends(verify_admin_token)):
    """
    清理旧的任务记录
    
    删除超过指定时间的已完成或失败的任务记录。
    """
    try:
        coordinator = await get_coordinator()
        await coordinator.cleanup_old_tasks(request.max_age_hours)
        
        return {
            "success": True,
            "message": f"Cleaned up tasks older than {request.max_age_hours} hours",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old tasks: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup old tasks")


@router.get("/logs", summary="获取系统日志")
async def get_system_logs(
    lines: int = 100,
    level: str = "INFO",
    admin: bool = Depends(verify_admin_token)
):
    """
    获取系统日志
    
    返回最近的系统日志记录。
    
    - **lines**: 返回的日志行数
    - **level**: 日志级别过滤
    """
    try:
        # 这里应该实现真正的日志读取逻辑
        # 目前返回模拟数据
        mock_logs = [
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "System is running normally",
                "component": "main"
            },
            {
                "timestamp": datetime.now().isoformat(),
                "level": "INFO",
                "message": "Query processed successfully",
                "component": "query_processor"
            }
        ]
        
        return {
            "success": True,
            "message": f"Retrieved {len(mock_logs)} log entries",
            "data": {
                "logs": mock_logs,
                "total_lines": len(mock_logs),
                "level_filter": level
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get system logs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system logs")


@router.post("/cache/clear", summary="清空缓存")
async def clear_cache(admin: bool = Depends(verify_admin_token)):
    """
    清空系统缓存
    
    清空查询缓存和嵌入向量缓存。
    """
    try:
        coordinator = await get_coordinator()
        
        # 清空数据库查询缓存
        if hasattr(coordinator.vanna_core.db_executor, 'cache'):
            coordinator.vanna_core.db_executor.cache.clear()
        
        # 清空嵌入向量缓存
        if hasattr(coordinator.vanna_core.qwen_service, 'cache'):
            coordinator.vanna_core.qwen_service.cache.clear()
        
        return {
            "success": True,
            "message": "Cache cleared successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


async def _rebuild_knowledge_base_task():
    """重建知识库的后台任务"""
    try:
        logger.info("Starting knowledge base rebuild task")
        coordinator = await get_coordinator()
        kb_manager = coordinator.vanna_core.kb_manager
        
        # 清空现有知识库
        await kb_manager.clear_all()
        logger.info("Knowledge base cleared")
        
        # 这里应该重新加载训练数据
        # 目前只记录日志
        logger.info("Knowledge base rebuild completed")
        
    except Exception as e:
        logger.error(f"Knowledge base rebuild task failed: {e}")


async def _generate_embeddings_task():
    """生成嵌入向量的后台任务"""
    try:
        logger.info("Starting embedding generation task")
        coordinator = await get_coordinator()
        kb_manager = coordinator.vanna_core.kb_manager
        
        # 生成嵌入向量
        results = await kb_manager.generate_embeddings_for_all()
        logger.info(f"Embedding generation completed: {results}")
        
    except Exception as e:
        logger.error(f"Embedding generation task failed: {e}")


def _calculate_success_rate(system_status: Dict[str, Any]) -> float:
    """计算成功率"""
    completed = system_status.get("completed_tasks", 0)
    failed = system_status.get("failed_tasks", 0)
    total = completed + failed
    
    if total == 0:
        return 0.0
    
    return round((completed / total) * 100, 2)