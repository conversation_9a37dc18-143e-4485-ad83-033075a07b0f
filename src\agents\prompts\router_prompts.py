"""路由Agent提示词"""

from typing import Dict, Any
from .base_prompts import BasePrompts


class RouterPrompts(BasePrompts):
    """路由Agent提示词管理"""
    
    # 意图识别提示词
    INTENT_CLASSIFICATION = """你是一个智能路由助手，负责理解用户意图并进行分类。

请分析用户输入，判断属于以下哪种类型：

1. **data_query** - 数据查询类
   - 用户想要查询TikTok相关数据
   - 包含统计、排行、趋势等需求
   - 例如："今天涨粉最多的达人"、"游戏类视频播放量"

2. **chat** - 闲聊对话类  
   - 日常对话、问候、感谢等
   - 与数据分析无关的交流
   - 例如："你好"、"谢谢"、"今天天气怎么样"

3. **help** - 帮助请求类
   - 询问系统功能、使用方法
   - 寻求操作指导
   - 例如："你能做什么"、"如何使用"、"帮助"

4. **other** - 其他类型
   - 无法明确分类的输入
   - 模糊或不完整的请求

请以JSON格式返回结果：
{
    "intent": "意图类型",
    "confidence": 0.95,
    "reasoning": "判断理由",
    "extracted_info": {
        "keywords": ["关键词列表"],
        "entities": ["实体列表"],
        "time_range": "时间范围（如果有）"
    }
}

用户输入：{user_input}
"""

    # Function Calling 定义
    FUNCTION_DEFINITIONS = [
        {
            "name": "classify_intent",
            "description": "分类用户意图",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {
                        "type": "string",
                        "enum": ["data_query", "chat", "help", "other"],
                        "description": "用户意图类型"
                    },
                    "confidence": {
                        "type": "number",
                        "minimum": 0,
                        "maximum": 1,
                        "description": "置信度"
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "判断理由"
                    },
                    "extracted_info": {
                        "type": "object",
                        "properties": {
                            "keywords": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "提取的关键词"
                            },
                            "entities": {
                                "type": "array", 
                                "items": {"type": "string"},
                                "description": "识别的实体"
                            },
                            "time_range": {
                                "type": "string",
                                "description": "时间范围"
                            }
                        }
                    }
                },
                "required": ["intent", "confidence", "reasoning"]
            }
        }
    ]

    # 闲聊回复提示词
    CHAT_RESPONSE = """你是TikTok AI数据分析助手，用户正在与你进行日常对话。

请以友好、专业的方式回复，并适当引导用户了解你的数据分析能力。

回复要求：
1. 保持友好和专业的语调
2. 简洁明了，不要过于冗长
3. 可以适当介绍你的功能
4. 鼓励用户尝试数据查询

用户说：{user_input}

请直接回复用户，不需要JSON格式。
"""

    # 帮助信息提示词
    HELP_RESPONSE = """用户正在寻求帮助，请提供系统功能介绍和使用指导。

系统功能：
1. **数据查询** - 使用自然语言查询TikTok数据
2. **智能分析** - 自动生成数据洞察和建议
3. **可视化** - 创建图表和可视化展示
4. **历史记录** - 保存查询历史便于回顾

使用示例：
- "今天涨粉最多的前10个达人是谁？"
- "过去一周哪个游戏达人播放量最高？"
- "美妆类视频的平均点赞率是多少？"
- "各个分类达人的平均粉丝数和视频数是多少？"

用户问题：{user_input}

请提供有针对性的帮助信息，格式为JSON：
{
    "response": "回复内容",
    "suggestions": ["建议查询1", "建议查询2", "建议查询3"],
    "tips": ["使用提示1", "使用提示2"]
}
"""

    @staticmethod
    def get_intent_prompt(user_input: str) -> str:
        """获取意图识别提示词"""
        return RouterPrompts.INTENT_CLASSIFICATION.format(user_input=user_input)
    
    @staticmethod
    def get_chat_prompt(user_input: str) -> str:
        """获取闲聊回复提示词"""
        return RouterPrompts.CHAT_RESPONSE.format(user_input=user_input)
    
    @staticmethod
    def get_help_prompt(user_input: str) -> str:
        """获取帮助回复提示词"""
        return RouterPrompts.HELP_RESPONSE.format(user_input=user_input)
    
    @staticmethod
    def get_function_definitions() -> list:
        """获取Function Calling定义"""
        return RouterPrompts.FUNCTION_DEFINITIONS