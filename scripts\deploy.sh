#!/bin/bash

# TikTok AI Agent 部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# 检查环境变量
check_environment() {
    log_info "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        log_warning ".env file not found. Creating from template..."
        if [ -f ".env.template" ]; then
            cp .env.template .env
            log_info "Please edit .env file with your configuration"
        else
            log_error ".env.template not found. Please create .env file manually."
            exit 1
        fi
    fi
    
    # 检查关键环境变量
    source .env
    
    if [ -z "$QWEN_API_KEY" ] || [ "$QWEN_API_KEY" = "your_qwen_api_key_here" ]; then
        log_warning "QWEN_API_KEY is not set or using default value"
        log_info "Please set your Qwen API key in .env file"
    fi
    
    log_success "Environment check completed"
}

# 构建镜像
build_images() {
    log_info "Building Docker images..."
    
    docker-compose build --no-cache
    
    log_success "Docker images built successfully"
}

# 启动服务
start_services() {
    local mode=${1:-"dual"}
    
    log_info "Starting services in $mode mode..."
    
    # 创建必要的目录
    mkdir -p data logs
    
    # 启动依赖服务
    log_info "Starting database and cache services..."
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # 启动主服务
    log_info "Starting TikTok AI Agent..."
    docker-compose up -d tiktok-ai-agent
    
    # 等待服务启动
    log_info "Waiting for services to be ready..."
    sleep 15
    
    # 健康检查
    if check_health; then
        log_success "All services started successfully"
        show_service_info
    else
        log_error "Some services failed to start properly"
        docker-compose logs tiktok-ai-agent
        exit 1
    fi
}

# 健康检查
check_health() {
    log_info "Performing health check..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8000/api/v1/health > /dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# 显示服务信息
show_service_info() {
    log_info "Service Information:"
    echo "=================================="
    echo "🌐 Web UI:        http://localhost:8501"
    echo "🔗 API:           http://localhost:8000"
    echo "📚 API Docs:      http://localhost:8000/docs"
    echo "📊 Health Check:  http://localhost:8000/api/v1/health"
    echo "🗄️  Database:      localhost:5432"
    echo "🔄 Redis:         localhost:6379"
    echo "=================================="
    
    log_info "To view logs: docker-compose logs -f"
    log_info "To stop services: docker-compose down"
}

# 停止服务
stop_services() {
    log_info "Stopping services..."
    
    docker-compose down
    
    log_success "Services stopped"
}

# 清理
cleanup() {
    log_info "Cleaning up..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像（可选）
    if [ "$1" = "--remove-images" ]; then
        log_info "Removing Docker images..."
        docker-compose down --rmi all
    fi
    
    log_success "Cleanup completed"
}

# 查看日志
view_logs() {
    local service=${1:-"tiktok-ai-agent"}
    
    log_info "Viewing logs for $service..."
    docker-compose logs -f $service
}

# 重启服务
restart_services() {
    log_info "Restarting services..."
    
    docker-compose restart
    
    # 等待服务重启
    sleep 10
    
    if check_health; then
        log_success "Services restarted successfully"
    else
        log_error "Service restart failed"
        exit 1
    fi
}

# 更新服务
update_services() {
    log_info "Updating services..."
    
    # 拉取最新代码（如果是git仓库）
    if [ -d ".git" ]; then
        log_info "Pulling latest code..."
        git pull
    fi
    
    # 重新构建镜像
    build_images
    
    # 重启服务
    docker-compose up -d --force-recreate
    
    # 健康检查
    if check_health; then
        log_success "Services updated successfully"
    else
        log_error "Service update failed"
        exit 1
    fi
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating backup in $backup_dir..."
    
    mkdir -p $backup_dir
    
    # 备份数据库
    docker-compose exec -T postgres pg_dump -U postgres tiktok_data > $backup_dir/database.sql
    
    # 备份知识库文件
    cp -r data $backup_dir/
    
    log_success "Backup created in $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir=$1
    
    if [ -z "$backup_dir" ] || [ ! -d "$backup_dir" ]; then
        log_error "Backup directory not specified or not found"
        exit 1
    fi
    
    log_info "Restoring data from $backup_dir..."
    
    # 恢复数据库
    if [ -f "$backup_dir/database.sql" ]; then
        docker-compose exec -T postgres psql -U postgres -d tiktok_data < $backup_dir/database.sql
    fi
    
    # 恢复知识库文件
    if [ -d "$backup_dir/data" ]; then
        cp -r $backup_dir/data/* data/
    fi
    
    log_success "Data restored from $backup_dir"
}

# 显示帮助信息
show_help() {
    echo "TikTok AI Agent Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start [mode]     Start services (mode: api|ui|dual, default: dual)"
    echo "  stop             Stop all services"
    echo "  restart          Restart all services"
    echo "  build            Build Docker images"
    echo "  update           Update and restart services"
    echo "  logs [service]   View logs (default: tiktok-ai-agent)"
    echo "  health           Check service health"
    echo "  backup           Backup data"
    echo "  restore <dir>    Restore data from backup directory"
    echo "  cleanup [--remove-images]  Clean up containers and optionally images"
    echo "  help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start dual    # Start both API and UI"
    echo "  $0 start api     # Start API only"
    echo "  $0 logs          # View main service logs"
    echo "  $0 backup        # Create data backup"
    echo ""
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_dependencies
            check_environment
            start_services "${2:-dual}"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "build")
            check_dependencies
            build_images
            ;;
        "update")
            check_dependencies
            update_services
            ;;
        "logs")
            view_logs "$2"
            ;;
        "health")
            check_health
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "cleanup")
            cleanup "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"