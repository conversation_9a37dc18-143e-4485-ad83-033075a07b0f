"""
千问模型嵌入功能演示
展示如何使用千问模型进行文本嵌入和相似度搜索
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.knowledge_base import KnowledgeBaseManager
from src.core.qwen_embedding import CachedQwenEmbeddingService
from src.core.config import load_config


async def demo_basic_embedding():
    """演示基本嵌入功能"""
    print("🔤 基本嵌入功能演示")
    print("=" * 50)
    
    # 注意：这里使用模拟的API密钥，实际使用时需要真实的千问API密钥
    os.environ.setdefault("QWEN_API_KEY", "mock_key_for_demo")
    
    try:
        load_config()
        print("✅ 配置加载成功")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 创建嵌入服务（注意：这里会因为API密钥无效而失败，仅用于演示代码结构）
    try:
        embedding_service = CachedQwenEmbeddingService()
        print("✅ 嵌入服务初始化成功")
        
        # 演示文本嵌入（实际运行时会失败，因为API密钥无效）
        test_texts = [
            "TikTok达人粉丝数统计",
            "视频播放量分析",
            "用户互动数据查询",
            "内容分类和标签管理"
        ]
        
        print("\n📝 测试文本列表:")
        for i, text in enumerate(test_texts, 1):
            print(f"  {i}. {text}")
        
        print("\n🔄 生成嵌入向量...")
        print("注意：由于使用模拟API密钥，实际API调用会失败")
        print("在真实环境中，这里会调用千问API生成嵌入向量")
        
        # 模拟嵌入向量（实际使用时会调用API）
        mock_embeddings = [
            [0.1, 0.2, 0.3, 0.4, 0.5] * 100,  # 模拟1536维向量
            [0.2, 0.3, 0.4, 0.5, 0.6] * 100,
            [0.3, 0.4, 0.5, 0.6, 0.7] * 100,
            [0.4, 0.5, 0.6, 0.7, 0.8] * 100,
        ]
        
        print("\n📊 相似度计算演示:")
        query_embedding = mock_embeddings[0]
        
        for i, candidate_embedding in enumerate(mock_embeddings):
            similarity = embedding_service.calculate_similarity(query_embedding, candidate_embedding)
            print(f"  查询文本 vs 文本{i+1}: {similarity:.4f}")
        
        print("\n🔍 最相似文本查找:")
        most_similar = embedding_service.find_most_similar(
            query_embedding, mock_embeddings[1:], top_k=2
        )
        
        for idx, (candidate_idx, score) in enumerate(most_similar):
            actual_idx = candidate_idx + 1  # 调整索引
            print(f"  第{idx+1}相似: 文本{actual_idx+1} (相似度: {score:.4f})")
        
        await embedding_service.close()
        
    except Exception as e:
        print(f"❌ 嵌入服务演示失败: {e}")
        print("这是预期的，因为使用了模拟API密钥")


async def demo_knowledge_base_embedding():
    """演示知识库嵌入功能"""
    print("\n\n🗄️ 知识库嵌入功能演示")
    print("=" * 50)
    
    # 创建知识库管理器（禁用嵌入功能以避免API调用失败）
    kb_manager = KnowledgeBaseManager(
        db_path="data/embedding_demo.db",
        enable_embedding=False  # 禁用真实的嵌入功能
    )
    
    try:
        # 添加示例数据
        print("📊 添加示例数据...")
        
        # 添加Schema
        await kb_manager.add_schema(
            table_name="user_metrics",
            ddl="CREATE TABLE user_metrics (user_id BIGINT, follower_count BIGINT, video_count INT)",
            description="用户指标表，包含粉丝数和视频数"
        )
        
        await kb_manager.add_schema(
            table_name="video_stats",
            ddl="CREATE TABLE video_stats (video_id BIGINT, view_count BIGINT, like_count BIGINT)",
            description="视频统计表，包含播放量和点赞数"
        )
        
        # 添加业务文档
        await kb_manager.add_business_doc(
            title="粉丝增长率计算",
            content="粉丝增长率 = (当前粉丝数 - 上期粉丝数) / 上期粉丝数 * 100%",
            category="metrics"
        )
        
        await kb_manager.add_business_doc(
            title="视频热度评分",
            content="视频热度 = 播放量 * 0.4 + 点赞数 * 0.3 + 评论数 * 0.2 + 分享数 * 0.1",
            category="algorithms"
        )
        
        # 添加SQL范例
        await kb_manager.add_sql_example(
            question="如何查询粉丝数最多的用户？",
            sql="SELECT user_id, follower_count FROM user_metrics ORDER BY follower_count DESC LIMIT 10",
            explanation="按粉丝数降序排列，取前10名用户"
        )
        
        print("✅ 示例数据添加完成")
        
        # 获取统计信息
        stats = await kb_manager.get_stats()
        print(f"\n📈 知识库统计:")
        print(f"  Schema数量: {stats['schemas']['total']}")
        print(f"  业务文档数量: {stats['business_docs']['total']}")
        print(f"  SQL范例数量: {stats['sql_examples']['total']}")
        
        # 演示文本搜索（回退功能）
        print(f"\n🔍 文本搜索演示:")
        
        # 搜索Schema
        schemas = await kb_manager.search_schemas("用户")
        print(f"  搜索'用户'相关Schema: {len(schemas)}个结果")
        for schema in schemas:
            print(f"    - {schema.table_name}: {schema.description}")
        
        # 搜索文档
        docs = await kb_manager.search_business_docs("粉丝")
        print(f"  搜索'粉丝'相关文档: {len(docs)}个结果")
        for doc in docs:
            print(f"    - {doc.title}")
        
        print("\n💡 嵌入功能说明:")
        print("  在真实环境中，使用有效的千问API密钥时，系统会:")
        print("  1. 为所有Schema、文档和SQL范例生成嵌入向量")
        print("  2. 支持基于语义相似度的智能搜索")
        print("  3. 提供更准确的上下文检索结果")
        print("  4. 支持跨语言和同义词匹配")
        
        # 演示如何在真实环境中使用嵌入功能
        print("\n🚀 真实环境使用示例:")
        print("  # 生成所有项目的嵌入向量")
        print("  results = await kb_manager.generate_embeddings_for_all()")
        print("  ")
        print("  # 基于语义相似度搜索")
        print("  search_result = await kb_manager.similarity_search('用户粉丝统计')")
        print("  ")
        print("  # 查找相似的SQL范例")
        print("  similar_examples = await kb_manager.find_similar_sql_examples('查询热门用户')")
        
    except Exception as e:
        print(f"❌ 知识库演示失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await kb_manager.close()


async def demo_similarity_scenarios():
    """演示相似度搜索场景"""
    print("\n\n🎯 相似度搜索场景演示")
    print("=" * 50)
    
    # 模拟不同类型的查询和匹配场景
    scenarios = [
        {
            "query": "用户粉丝数量统计",
            "candidates": [
                "CREATE TABLE users (id BIGINT, follower_count BIGINT)",
                "粉丝增长率计算公式",
                "SELECT COUNT(*) FROM videos",
                "用户关注数据分析"
            ],
            "expected_match": "CREATE TABLE users (id BIGINT, follower_count BIGINT)"
        },
        {
            "query": "视频播放量分析",
            "candidates": [
                "用户注册流程",
                "视频热度评分算法",
                "CREATE TABLE video_stats (view_count BIGINT)",
                "评论管理系统"
            ],
            "expected_match": "CREATE TABLE video_stats (view_count BIGINT)"
        },
        {
            "query": "如何查询热门视频",
            "candidates": [
                "SELECT * FROM videos ORDER BY view_count DESC",
                "用户权限管理",
                "数据备份策略",
                "SELECT user_id FROM users"
            ],
            "expected_match": "SELECT * FROM videos ORDER BY view_count DESC"
        }
    ]
    
    print("📝 相似度匹配场景:")
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n场景 {i}: {scenario['query']}")
        print("候选项:")
        for j, candidate in enumerate(scenario['candidates'], 1):
            print(f"  {j}. {candidate}")
        print(f"预期最佳匹配: {scenario['expected_match']}")
        
        # 在真实环境中，这里会计算实际的语义相似度
        print("💡 在真实环境中，系统会:")
        print("  - 将查询和候选项转换为嵌入向量")
        print("  - 计算余弦相似度")
        print("  - 返回最相似的结果")
    
    print(f"\n🔧 技术实现要点:")
    print("1. 文本预处理: 清理和标准化输入文本")
    print("2. 嵌入生成: 调用千问API生成高质量向量")
    print("3. 相似度计算: 使用余弦相似度衡量语义相关性")
    print("4. 结果排序: 按相似度分数降序返回结果")
    print("5. 缓存优化: 缓存常用文本的嵌入向量")


async def main():
    """主函数"""
    print("🚀 千问模型嵌入功能演示")
    print("=" * 60)
    
    await demo_basic_embedding()
    await demo_knowledge_base_embedding()
    await demo_similarity_scenarios()
    
    print("\n" + "=" * 60)
    print("✅ 演示完成！")
    print("\n📋 总结:")
    print("1. 千问嵌入服务提供高质量的文本向量化能力")
    print("2. 支持批量处理和缓存优化")
    print("3. 知识库管理器集成嵌入功能，支持语义搜索")
    print("4. 提供多种相似度搜索接口")
    print("5. 在API密钥无效时自动回退到文本搜索")
    
    print("\n🔑 使用提示:")
    print("- 设置有效的QWEN_API_KEY环境变量")
    print("- 根据需要调整相似度阈值")
    print("- 定期更新嵌入向量以保持准确性")
    print("- 监控API调用频率和成本")


if __name__ == "__main__":
    asyncio.run(main())