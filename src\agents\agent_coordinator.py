"""
Agent协作机制实现
负责Agent间的通信、任务编排和状态同步
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from datetime import datetime
from enum import Enum
import uuid
import json

try:
    from .router_agent import RouterAgent
    from .display_agent import DisplayAgent, Report
    from ..core.vanna_core import Vanna<PERSON>ore
    from ..models.query import UserQuery, QueryResult
    from ..models.base import APIResponse
except ImportError:
    # 绝对导入作为备选
    from agents.router_agent import RouterAgent
    from agents.display_agent import DisplayAgent, Report
    from core.vanna_core import VannaCore
    from models.query import UserQuery, QueryResult
    from models.base import APIResponse

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """任务类型枚举"""
    USER_QUERY = "user_query"
    DATA_ANALYSIS = "data_analysis"
    VISUALIZATION = "visualization"
    REPORT_GENERATION = "report_generation"


class AgentMessage:
    """Agent间消息"""
    
    def __init__(self, sender: str, receiver: str, message_type: str, 
                 payload: Dict[str, Any], correlation_id: Optional[str] = None):
        """
        初始化Agent消息
        
        Args:
            sender: 发送者
            receiver: 接收者
            message_type: 消息类型
            payload: 消息载荷
            correlation_id: 关联ID
        """
        self.id = str(uuid.uuid4())
        self.sender = sender
        self.receiver = receiver
        self.message_type = message_type
        self.payload = payload
        self.correlation_id = correlation_id or self.id
        self.timestamp = datetime.now()
        self.processed = False


class Task:
    """异步任务"""
    
    def __init__(self, task_id: str, task_type: TaskType, payload: Dict[str, Any],
                 callback: Optional[Callable] = None):
        """
        初始化任务
        
        Args:
            task_id: 任务ID
            task_type: 任务类型
            payload: 任务载荷
            callback: 回调函数
        """
        self.task_id = task_id
        self.task_type = task_type
        self.payload = payload
        self.callback = callback
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Optional[Any] = None
        self.error: Optional[str] = None
        self.progress = 0.0
    
    def start(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
        self.progress = 0.1
    
    def complete(self, result: Any):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
        self.progress = 1.0
    
    def fail(self, error: str):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error = error
        self.progress = 0.0
    
    def update_progress(self, progress: float):
        """更新进度"""
        self.progress = max(0.0, min(1.0, progress))


class MessageBus:
    """消息总线"""
    
    def __init__(self):
        """初始化消息总线"""
        self.subscribers: Dict[str, List[Callable]] = {}
        self.message_queue: List[AgentMessage] = []
        self.processing = False
        
    def subscribe(self, message_type: str, handler: Callable):
        """订阅消息类型"""
        if message_type not in self.subscribers:
            self.subscribers[message_type] = []
        self.subscribers[message_type].append(handler)
        logger.info(f"Subscribed to message type: {message_type}")
    
    async def publish(self, message: AgentMessage):
        """发布消息"""
        self.message_queue.append(message)
        logger.info(f"Published message: {message.message_type} from {message.sender} to {message.receiver}")
        
        # 如果没有在处理消息，启动处理
        if not self.processing:
            await self._process_messages()
    
    async def _process_messages(self):
        """处理消息队列"""
        self.processing = True
        
        try:
            while self.message_queue:
                message = self.message_queue.pop(0)
                await self._handle_message(message)
        finally:
            self.processing = False
    
    async def _handle_message(self, message: AgentMessage):
        """处理单个消息"""
        try:
            handlers = self.subscribers.get(message.message_type, [])
            
            for handler in handlers:
                try:
                    await handler(message)
                except Exception as e:
                    logger.error(f"Message handler failed: {e}")
            
            message.processed = True
            logger.debug(f"Message processed: {message.id}")
            
        except Exception as e:
            logger.error(f"Failed to handle message {message.id}: {e}")


class AgentCoordinator:
    """Agent协调器 - 管理Agent间的协作"""
    
    def __init__(self):
        """初始化Agent协调器"""
        # 初始化各个Agent
        self.vanna_core = VannaCore()
        self.router_agent = RouterAgent(self.vanna_core)
        self.display_agent = DisplayAgent()
        
        # 消息总线
        self.message_bus = MessageBus()
        
        # 任务管理
        self.tasks: Dict[str, Task] = {}
        self.task_queue = asyncio.Queue()
        
        # 状态管理
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        # 启动后台任务处理
        self._task_processor_running = False
        
        logger.info("AgentCoordinator initialized")
    
    async def start(self):
        """启动协调器"""
        # 启动任务处理器
        if not self._task_processor_running:
            asyncio.create_task(self._task_processor())
            self._task_processor_running = True
        
        logger.info("AgentCoordinator started")
    
    async def process_user_request(self, user_input: str, session_id: str,
                                 user_id: Optional[str] = None) -> APIResponse:
        """
        处理用户请求的完整流程
        
        Args:
            user_input: 用户输入
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            APIResponse: 处理结果
        """
        try:
            logger.info(f"Processing user request: {user_input[:100]}...")
            
            # 创建任务ID
            task_id = str(uuid.uuid4())
            
            # 第一步：路由Agent处理用户输入
            router_response = await self.router_agent.process_user_input(
                user_input, session_id, user_id
            )
            
            if not router_response.success:
                return router_response
            
            # 检查是否是数据查询任务
            if (router_response.data and 
                router_response.data.get("type") == "data_query"):
                
                # 创建完整的数据分析任务
                return await self._handle_data_analysis_workflow(
                    router_response.data, task_id, session_id, user_input
                )
            else:
                # 非数据查询任务，直接返回路由结果
                return router_response
                
        except Exception as e:
            logger.error(f"Failed to process user request: {e}")
            return APIResponse(
                success=False,
                message="处理用户请求时发生错误",
                error=str(e)
            )
    
    async def _handle_data_analysis_workflow(self, query_data: Dict[str, Any],
                                           task_id: str, session_id: str,
                                           original_question: str) -> APIResponse:
        """处理数据分析工作流"""
        try:
            logger.info(f"Starting data analysis workflow for task: {task_id}")
            
            # 创建QueryResult对象
            query_result = QueryResult(
                data=query_data.get("data", []),
                columns=query_data.get("columns", []),
                row_count=query_data.get("row_count", 0),
                execution_time=query_data.get("execution_time", 0.0),
                sql_executed=query_data.get("sql", ""),
                executed_at=datetime.now()
            )
            
            # 使用DisplayAgent生成完整报告
            report = await self.display_agent.create_report(query_result, original_question)
            
            # 构建完整响应
            return APIResponse(
                success=True,
                message="数据分析完成",
                data={
                    "type": "complete_analysis",
                    "task_id": task_id,
                    "query": original_question,
                    "sql": query_data.get("sql", ""),
                    "raw_data": {
                        "data": query_result.data,
                        "columns": query_result.columns,
                        "row_count": query_result.row_count,
                        "execution_time": query_result.execution_time,
                        "is_mock": query_data.get("is_mock", False)
                    },
                    "analysis": {
                        "summary": report.analysis.summary,
                        "insights": report.analysis.insights,
                        "recommendations": report.analysis.recommendations,
                        "key_metrics": report.analysis.key_metrics
                    },
                    "visualizations": [
                        {
                            "chart_type": viz.chart_type,
                            "title": viz.title,
                            "description": viz.description,
                            "code": viz.code
                        }
                        for viz in report.visualizations
                    ],
                    "generated_at": report.generated_at.isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to handle data analysis workflow: {e}")
            return APIResponse(
                success=False,
                message="数据分析工作流处理失败",
                error=str(e)
            )
    
    async def create_task(self, task_type: TaskType, payload: Dict[str, Any],
                         callback: Optional[Callable] = None) -> str:
        """
        创建异步任务
        
        Args:
            task_type: 任务类型
            payload: 任务载荷
            callback: 回调函数
            
        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())
        task = Task(task_id, task_type, payload, callback)
        
        self.tasks[task_id] = task
        await self.task_queue.put(task)
        
        logger.info(f"Created task: {task_id} ({task_type.value})")
        return task_id
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        if not task:
            return None
        
        return {
            "task_id": task.task_id,
            "task_type": task.task_type.value,
            "status": task.status.value,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "error": task.error
        }
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            logger.info(f"Cancelled task: {task_id}")
            return True
        
        return False
    
    async def _task_processor(self):
        """后台任务处理器"""
        logger.info("Task processor started")
        
        while True:
            try:
                # 等待任务
                task = await self.task_queue.get()
                
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                # 处理任务
                await self._process_task(task)
                
            except Exception as e:
                logger.error(f"Task processor error: {e}")
                await asyncio.sleep(1)
    
    async def _process_task(self, task: Task):
        """处理单个任务"""
        try:
            logger.info(f"Processing task: {task.task_id}")
            task.start()
            
            if task.task_type == TaskType.USER_QUERY:
                result = await self._process_user_query_task(task)
            elif task.task_type == TaskType.DATA_ANALYSIS:
                result = await self._process_data_analysis_task(task)
            elif task.task_type == TaskType.VISUALIZATION:
                result = await self._process_visualization_task(task)
            elif task.task_type == TaskType.REPORT_GENERATION:
                result = await self._process_report_generation_task(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            task.complete(result)
            
            # 调用回调函数
            if task.callback:
                try:
                    await task.callback(task)
                except Exception as e:
                    logger.error(f"Task callback failed: {e}")
            
            logger.info(f"Task completed: {task.task_id}")
            
        except Exception as e:
            logger.error(f"Task processing failed: {e}")
            task.fail(str(e))
    
    async def _process_user_query_task(self, task: Task) -> Any:
        """处理用户查询任务"""
        payload = task.payload
        user_input = payload.get("user_input", "")
        session_id = payload.get("session_id", "")
        user_id = payload.get("user_id")
        
        task.update_progress(0.3)
        
        # 使用路由Agent处理
        result = await self.router_agent.process_user_input(user_input, session_id, user_id)
        
        task.update_progress(0.8)
        return result
    
    async def _process_data_analysis_task(self, task: Task) -> Any:
        """处理数据分析任务"""
        payload = task.payload
        query_result = payload.get("query_result")
        original_question = payload.get("original_question", "")
        
        task.update_progress(0.3)
        
        # 使用DisplayAgent分析数据
        analysis = await self.display_agent.analyze_data(query_result, original_question)
        
        task.update_progress(0.8)
        return analysis
    
    async def _process_visualization_task(self, task: Task) -> Any:
        """处理可视化任务"""
        payload = task.payload
        query_result = payload.get("query_result")
        chart_type = payload.get("chart_type")
        
        task.update_progress(0.3)
        
        # 使用DisplayAgent生成可视化
        visualizations = await self.display_agent.generate_visualization(query_result, chart_type)
        
        task.update_progress(0.8)
        return visualizations
    
    async def _process_report_generation_task(self, task: Task) -> Any:
        """处理报告生成任务"""
        payload = task.payload
        query_result = payload.get("query_result")
        original_question = payload.get("original_question", "")
        
        task.update_progress(0.3)
        
        # 使用DisplayAgent生成完整报告
        report = await self.display_agent.create_report(query_result, original_question)
        
        task.update_progress(0.8)
        return report
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "active_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
            "pending_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
            "completed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
            "failed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
            "active_sessions": len(self.active_sessions),
            "message_queue_size": len(self.message_bus.message_queue),
            "task_processor_running": self._task_processor_running
        }
    
    async def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        try:
            current_time = datetime.now()
            old_task_ids = []
            
            for task_id, task in self.tasks.items():
                age_hours = (current_time - task.created_at).total_seconds() / 3600
                if age_hours > max_age_hours and task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    old_task_ids.append(task_id)
            
            for task_id in old_task_ids:
                del self.tasks[task_id]
            
            if old_task_ids:
                logger.info(f"Cleaned up {len(old_task_ids)} old tasks")
                
        except Exception as e:
            logger.error(f"Failed to cleanup old tasks: {e}")
    
    async def close(self):
        """关闭协调器"""
        # 关闭各个Agent
        if self.router_agent:
            await self.router_agent.close()
        if self.display_agent:
            await self.display_agent.close()
        if self.vanna_core:
            await self.vanna_core.close()
        
        # 取消所有待处理任务
        for task in self.tasks.values():
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task.status = TaskStatus.CANCELLED
        
        logger.info("AgentCoordinator closed")


# 全局协调器实例
_coordinator_instance: Optional[AgentCoordinator] = None


async def get_coordinator() -> AgentCoordinator:
    """获取全局协调器实例"""
    global _coordinator_instance
    
    if _coordinator_instance is None:
        _coordinator_instance = AgentCoordinator()
        await _coordinator_instance.start()
    
    return _coordinator_instance


async def close_coordinator():
    """关闭全局协调器"""
    global _coordinator_instance
    
    if _coordinator_instance:
        await _coordinator_instance.close()
        _coordinator_instance = None