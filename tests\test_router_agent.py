"""
路由Agent单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agents.router_agent import RouterAgent, IntentType, SessionState
from models.base import APIResponse
from models.query import QueryResult


class TestSessionState:
    """会话状态测试"""
    
    def test_session_creation(self):
        """测试会话创建"""
        session = SessionState("test_session", "test_user")
        
        assert session.session_id == "test_session"
        assert session.user_id == "test_user"
        assert isinstance(session.created_at, datetime)
        assert isinstance(session.last_activity, datetime)
        assert session.context == {}
        assert session.query_history == []
    
    def test_update_activity(self):
        """测试活动更新"""
        session = SessionState("test_session")
        original_time = session.last_activity
        
        # 等待一小段时间确保时间差异
        import time
        time.sleep(0.01)
        
        session.update_activity()
        assert session.last_activity > original_time
    
    def test_add_query(self):
        """测试添加查询历史"""
        session = SessionState("test_session")
        
        session.add_query("测试问题", {"result": "测试结果"})
        
        assert len(session.query_history) == 1
        assert session.query_history[0]["query"] == "测试问题"
        assert session.query_history[0]["result"] == {"result": "测试结果"}
        assert "timestamp" in session.query_history[0]
    
    def test_get_recent_queries(self):
        """测试获取最近查询"""
        session = SessionState("test_session")
        
        # 添加多个查询
        for i in range(10):
            session.add_query(f"问题{i}", f"结果{i}")
        
        # 获取最近5个
        recent = session.get_recent_queries(5)
        assert len(recent) == 5
        assert recent[-1]["query"] == "问题9"  # 最新的查询
        
        # 获取全部（少于限制）
        session2 = SessionState("test_session2")
        session2.add_query("问题1", "结果1")
        recent2 = session2.get_recent_queries(5)
        assert len(recent2) == 1


class TestRouterAgent:
    """路由Agent测试"""
    
    @pytest.fixture
    def mock_vanna_core(self):
        """模拟Vanna核心"""
        mock = AsyncMock()
        mock.process_query.return_value = QueryResult(
            data=[{"name": "test", "value": 100}],
            columns=["name", "value"],
            row_count=1,
            execution_time=0.5,
            sql_executed="SELECT * FROM test"
        )
        return mock
    
    @pytest.fixture
    def router_agent(self, mock_vanna_core):
        """创建路由Agent实例"""
        with patch('agents.router_agent.QwenTextGenerator') as mock_generator:
            mock_generator.return_value = AsyncMock()
            agent = RouterAgent(mock_vanna_core)
            return agent
    
    def test_router_agent_initialization(self, router_agent):
        """测试路由Agent初始化"""
        assert router_agent.text_generator is not None
        assert router_agent.vanna_core is not None
        assert router_agent.sessions == {}
        assert router_agent.session_timeout == 3600
        assert router_agent.intent_confidence_threshold == 0.6
    
    @pytest.mark.asyncio
    async def test_classify_intent_data_query(self, router_agent):
        """测试数据查询意图分类"""
        # 模拟千问模型返回
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "data_query",
            "confidence": 0.8,
            "explanation": "检测到数据查询"
        }
        
        result = await router_agent.classify_intent("今天涨粉最多的达人是谁？")
        
        assert result["intent"] == "data_query"
        assert result["confidence"] == 0.8
        router_agent.text_generator.classify_intent.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_classify_intent_fallback(self, router_agent):
        """测试意图分类回退机制"""
        # 模拟千问模型调用失败
        router_agent.text_generator.classify_intent.side_effect = Exception("API调用失败")
        
        result = await router_agent.classify_intent("今天涨粉最多的达人是谁？")
        
        assert result["intent"] == "data_query"
        assert result["confidence"] > 0.5
        assert "涨粉" in result["keywords"]
    
    def test_fallback_intent_classification_data_query(self, router_agent):
        """测试回退意图分类 - 数据查询"""
        result = router_agent._fallback_intent_classification("查询今天播放量最高的视频")
        
        assert result["intent"] == "data_query"
        assert result["confidence"] > 0.5
        assert any(keyword in result["keywords"] for keyword in ["查询", "播放量"])
    
    def test_fallback_intent_classification_chat(self, router_agent):
        """测试回退意图分类 - 闲聊"""
        result = router_agent._fallback_intent_classification("你好，谢谢")
        
        assert result["intent"] == "chat"
        assert result["confidence"] > 0.5
        assert any(keyword in result["keywords"] for keyword in ["你好", "谢谢"])
    
    def test_fallback_intent_classification_help(self, router_agent):
        """测试回退意图分类 - 帮助"""
        result = router_agent._fallback_intent_classification("请帮助我了解如何使用")
        
        assert result["intent"] == "help"
        assert result["confidence"] > 0.5
        assert any(keyword in result["keywords"] for keyword in ["帮助", "如何使用"])
    
    def test_fallback_intent_classification_other(self, router_agent):
        """测试回退意图分类 - 其他"""
        result = router_agent._fallback_intent_classification("随机文本内容")
        
        assert result["intent"] == "other"
        assert result["confidence"] < 0.5
        assert result["keywords"] == []
    
    @pytest.mark.asyncio
    async def test_process_user_input_data_query(self, router_agent):
        """测试处理数据查询用户输入"""
        # 模拟意图分类返回数据查询
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "data_query",
            "confidence": 0.8
        }
        
        response = await router_agent.process_user_input(
            "今天涨粉最多的达人是谁？", "test_session", "test_user"
        )
        
        assert response.success is True
        assert response.data["type"] == "data_query"
        assert "sql" in response.data
        assert "data" in response.data
        
        # 检查会话是否创建
        assert "test_session" in router_agent.sessions
        session = router_agent.sessions["test_session"]
        assert session.user_id == "test_user"
        assert len(session.query_history) == 1
    
    @pytest.mark.asyncio
    async def test_process_user_input_help(self, router_agent):
        """测试处理帮助请求"""
        # 模拟意图分类返回帮助
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "help",
            "confidence": 0.8
        }
        
        response = await router_agent.process_user_input(
            "帮助", "test_session"
        )
        
        assert response.success is True
        assert response.data["type"] == "help"
        assert "system_info" in response.data["content"]
        assert "capabilities" in response.data["content"]
        assert "example_queries" in response.data["content"]
    
    @pytest.mark.asyncio
    async def test_process_user_input_chat(self, router_agent):
        """测试处理闲聊"""
        # 模拟意图分类返回闲聊
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "chat",
            "confidence": 0.8
        }
        
        response = await router_agent.process_user_input(
            "你好", "test_session"
        )
        
        assert response.success is True
        assert response.data["type"] == "chat"
        assert "response" in response.data
        assert "suggestions" in response.data
    
    @pytest.mark.asyncio
    async def test_process_user_input_unknown(self, router_agent):
        """测试处理未知意图"""
        # 模拟意图分类返回未知
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "other",
            "confidence": 0.3
        }
        
        response = await router_agent.process_user_input(
            "随机内容", "test_session"
        )
        
        assert response.success is True
        assert response.data["type"] == "unknown"
        assert "suggestions" in response.data
        assert "help_hint" in response.data
    
    @pytest.mark.asyncio
    async def test_process_user_input_low_confidence(self, router_agent):
        """测试低置信度数据查询处理"""
        # 模拟意图分类返回低置信度数据查询
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "data_query",
            "confidence": 0.4  # 低于阈值0.6
        }
        
        response = await router_agent.process_user_input(
            "可能是数据查询", "test_session"
        )
        
        # 应该被当作未知意图处理
        assert response.success is True
        assert response.data["type"] == "unknown"
    
    @pytest.mark.asyncio
    async def test_handle_data_query_success(self, router_agent):
        """测试成功处理数据查询"""
        session = SessionState("test_session", "test_user")
        
        response = await router_agent._handle_data_query("测试查询", session)
        
        assert response.success is True
        assert response.data["type"] == "data_query"
        assert response.data["query"] == "测试查询"
        assert "sql" in response.data
        assert "data" in response.data
        assert "formatted_result" in response.data
    
    @pytest.mark.asyncio
    async def test_handle_data_query_failure(self, router_agent):
        """测试数据查询失败处理"""
        session = SessionState("test_session", "test_user")
        
        # 模拟Vanna核心返回错误
        error_result = QueryResult(
            data=[],
            columns=[],
            row_count=0,
            execution_time=0.0,
            error="SQL执行失败"
        )
        router_agent.vanna_core.process_query.return_value = error_result
        
        response = await router_agent._handle_data_query("错误查询", session)
        
        assert response.success is False
        assert "数据查询失败" in response.message
    
    def test_format_query_result_single_row(self, router_agent):
        """测试格式化单行查询结果"""
        query_result = QueryResult(
            data=[{"name": "测试达人", "follower_count": 100000, "engagement_rate": 5.5}],
            columns=["name", "follower_count", "engagement_rate"],
            row_count=1,
            execution_time=0.5
        )
        
        formatted = router_agent._format_query_result(query_result)
        
        assert "查询结果：" in formatted
        assert "测试达人" in formatted
        assert "100,000" in formatted  # 数字格式化
        assert "5.50%" in formatted   # 百分比格式化
    
    def test_format_query_result_multiple_rows(self, router_agent):
        """测试格式化多行查询结果"""
        query_result = QueryResult(
            data=[
                {"name": "达人1", "views": 1000},
                {"name": "达人2", "views": 2000},
                {"name": "达人3", "views": 3000}
            ],
            columns=["name", "views"],
            row_count=3,
            execution_time=0.5
        )
        
        formatted = router_agent._format_query_result(query_result)
        
        assert "查询返回 3 条结果：" in formatted
        assert "1. 达人1" in formatted
        assert "2. 达人2" in formatted
        assert "3. 达人3" in formatted
    
    def test_format_query_result_empty(self, router_agent):
        """测试格式化空查询结果"""
        query_result = QueryResult(
            data=[],
            columns=[],
            row_count=0,
            execution_time=0.5
        )
        
        formatted = router_agent._format_query_result(query_result)
        
        assert "查询未返回任何数据" in formatted
    
    def test_session_management(self, router_agent):
        """测试会话管理"""
        # 创建新会话
        session1 = router_agent._get_or_create_session("session1", "user1")
        assert session1.session_id == "session1"
        assert session1.user_id == "user1"
        
        # 获取已存在会话
        session1_again = router_agent._get_or_create_session("session1")
        assert session1 is session1_again
        
        # 创建另一个会话
        session2 = router_agent._get_or_create_session("session2", "user2")
        assert session2.session_id == "session2"
        assert session2 != session1
        
        assert len(router_agent.sessions) == 2
    
    def test_cleanup_expired_sessions(self, router_agent):
        """测试清理过期会话"""
        # 创建会话并手动设置过期时间
        session = SessionState("expired_session")
        session.last_activity = datetime(2020, 1, 1)  # 很久以前
        router_agent.sessions["expired_session"] = session
        
        # 创建正常会话
        normal_session = router_agent._get_or_create_session("normal_session")
        
        # 清理过期会话
        router_agent._cleanup_expired_sessions()
        
        # 检查过期会话被删除，正常会话保留
        assert "expired_session" not in router_agent.sessions
        assert "normal_session" in router_agent.sessions
    
    def test_get_session_stats(self, router_agent):
        """测试获取会话统计"""
        # 创建几个会话
        router_agent._get_or_create_session("session1")
        router_agent._get_or_create_session("session2")
        router_agent._get_or_create_session("session3")
        
        stats = router_agent.get_session_stats()
        
        assert stats["active_sessions"] == 3
        assert stats["session_timeout"] == 3600
    
    @pytest.mark.asyncio
    async def test_error_handling(self, router_agent):
        """测试错误处理"""
        # 模拟Vanna核心抛出异常
        router_agent.vanna_core.process_query.side_effect = Exception("数据库连接失败")
        
        # 模拟意图分类返回数据查询
        router_agent.text_generator.classify_intent.return_value = {
            "intent": "data_query",
            "confidence": 0.8
        }
        
        response = await router_agent.process_user_input(
            "测试查询", "test_session"
        )
        
        assert response.success is False
        assert "处理用户输入时发生错误" in response.message
        assert response.error is not None
    
    @pytest.mark.asyncio
    async def test_close(self, router_agent):
        """测试关闭资源"""
        await router_agent.close()
        
        # 验证相关资源的close方法被调用
        router_agent.text_generator.close.assert_called_once()
        router_agent.vanna_core.close.assert_called_once()


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_workflow_data_query(self):
        """测试完整的数据查询工作流"""
        with patch('agents.router_agent.QwenTextGenerator') as mock_generator_class:
            with patch('agents.router_agent.VannaCore') as mock_vanna_class:
                # 设置模拟对象
                mock_generator = AsyncMock()
                mock_vanna = AsyncMock()
                mock_generator_class.return_value = mock_generator
                mock_vanna_class.return_value = mock_vanna
                
                # 配置模拟返回值
                mock_generator.classify_intent.return_value = {
                    "intent": "data_query",
                    "confidence": 0.8
                }
                
                mock_vanna.process_query.return_value = QueryResult(
                    data=[{"creator": "测试达人", "fans": 100000}],
                    columns=["creator", "fans"],
                    row_count=1,
                    execution_time=0.5,
                    sql_executed="SELECT creator, fans FROM creators ORDER BY fans DESC LIMIT 1"
                )
                
                # 创建路由Agent并测试
                agent = RouterAgent()
                
                response = await agent.process_user_input(
                    "粉丝最多的达人是谁？", "test_session", "test_user"
                )
                
                # 验证结果
                assert response.success is True
                assert response.data["type"] == "data_query"
                assert response.data["query"] == "粉丝最多的达人是谁？"
                assert len(response.data["data"]) == 1
                assert response.data["data"][0]["creator"] == "测试达人"
                
                # 验证会话状态
                assert "test_session" in agent.sessions
                session = agent.sessions["test_session"]
                assert len(session.query_history) == 1
                
                await agent.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])