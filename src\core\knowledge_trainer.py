#!/usr/bin/env python3
"""
知持批量导入Schema、文档和SQL范例，实现知识库的离线训练
"""

import asyncio
import json
import logging
import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import argparse
from datetime import datetime

from .knowledge_base import KnowledgeBaseManager
from .config import load_config
from ..models.knowledge import SchemaInfo, BusinessDoc, SQLExample, TrainingData

logger = logging.getLogger(__name__)


class KnowledgeTrainer:
    """知识库训练器"""
    
    def __init__(self, kb_manager: KnowledgeBaseManager):
        """
        初始化知识库训练器
        
        Args:
            kb_manager: 知识库管理器实例
        """
        self.kb_manager = kb_manager
        self.stats = {
            "schemas_added": 0,
            "docs_added": 0,
            "examples_added": 0,
            "embeddings_generated": 0,
            "errors": []
        }
        
        logger.info("KnowledgeTrainer initialized")
    
    async def load_from_json(self, file_path: Union[str, Path]) -> bool:
        """
        从JSON文件加载训练数据
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Loading training data from {file_path}")
            return await self._process_training_data(data)
            
        except Exception as e:
            logger.error(f"Failed to load from JSON: {e}")
            self.stats["errors"].append(f"JSON load error: {e}")
            return False
    
    async def load_from_yaml(self, file_path: Union[str, Path]) -> bool:
        """
        从YAML文件加载训练数据
        
        Args:
            file_path: YAML文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            logger.info(f"Loading training data from {file_path}")
            return await self._process_training_data(data)
            
        except Exception as e:
            logger.error(f"Failed to load from YAML: {e}")
            self.stats["errors"].append(f"YAML load error: {e}")
            return False
    
    async def load_from_directory(self, dir_path: Union[str, Path]) -> bool:
        """
        从目录批量加载训练数据
        
        Args:
            dir_path: 目录路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            dir_path = Path(dir_path)
            if not dir_path.exists() or not dir_path.is_dir():
                logger.error(f"Directory not found: {dir_path}")
                return False
            
            success_count = 0
            total_count = 0
            
            # 处理JSON文件
            for json_file in dir_path.glob("*.json"):
                total_count += 1
                if await self.load_from_json(json_file):
                    success_count += 1
                    logger.info(f"Successfully loaded: {json_file}")
                else:
                    logger.error(f"Failed to load: {json_file}")
            
            # 处理YAML文件
            for yaml_file in dir_path.glob("*.yaml"):
                total_count += 1
                if await self.load_from_yaml(yaml_file):
                    success_count += 1
                    logger.info(f"Successfully loaded: {yaml_file}")
                else:
                    logger.error(f"Failed to load: {yaml_file}")
            
            for yml_file in dir_path.glob("*.yml"):
                total_count += 1
                if await self.load_from_yaml(yml_file):
                    success_count += 1
                    logger.info(f"Successfully loaded: {yml_file}")
                else:
                    logger.error(f"Failed to load: {yml_file}")
            
            logger.info(f"Directory processing complete: {success_count}/{total_count} files loaded successfully")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Failed to load from directory: {e}")
            self.stats["errors"].append(f"Directory load error: {e}")
            return False
    
    async def _process_training_data(self, data: Dict[str, Any]) -> bool:
        """
        处理训练数据
        
        Args:
            data: 训练数据字典
            
        Returns:
            bool: 是否处理成功
        """
        try:
            success = True
            
            # 处理Schema数据
            if "schemas" in data:
                schemas_data = data["schemas"]
                if isinstance(schemas_data, list):
                    for schema_data in schemas_data:
                        if await self._add_schema_from_data(schema_data):
                            self.stats["schemas_added"] += 1
                        else:
                            success = False
                else:
                    logger.warning("Schemas data should be a list")
            
            # 处理业务文档数据
            if "documents" in data or "docs" in data:
                docs_data = data.get("documents", data.get("docs", []))
                if isinstance(docs_data, list):
                    for doc_data in docs_data:
                        if await self._add_document_from_data(doc_data):
                            self.stats["docs_added"] += 1
                        else:
                            success = False
                else:
                    logger.warning("Documents data should be a list")
            
            # 处理SQL范例数据
            if "sql_examples" in data or "examples" in data:
                examples_data = data.get("sql_examples", data.get("examples", []))
                if isinstance(examples_data, list):
                    for example_data in examples_data:
                        if await self._add_sql_example_from_data(example_data):
                            self.stats["examples_added"] += 1
                        else:
                            success = False
                else:
                    logger.warning("SQL examples data should be a list")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to process training data: {e}")
            self.stats["errors"].append(f"Data processing error: {e}")
            return False
    
    async def _add_schema_from_data(self, schema_data: Dict[str, Any]) -> bool:
        """从数据字典添加Schema"""
        try:
            required_fields = ["table_name", "ddl"]
            for field in required_fields:
                if field not in schema_data:
                    logger.error(f"Missing required field '{field}' in schema data")
                    return False
            
            return await self.kb_manager.add_schema(
                table_name=schema_data["table_name"],
                ddl=schema_data["ddl"],
                description=schema_data.get("description")
            )
            
        except Exception as e:
            logger.error(f"Failed to add schema: {e}")
            self.stats["errors"].append(f"Schema add error: {e}")
            return False
    
    async def _add_document_from_data(self, doc_data: Dict[str, Any]) -> bool:
        """从数据字典添加业务文档"""
        try:
            required_fields = ["title", "content"]
            for field in required_fields:
                if field not in doc_data:
                    logger.error(f"Missing required field '{field}' in document data")
                    return False
            
            return await self.kb_manager.add_business_doc(
                title=doc_data["title"],
                content=doc_data["content"],
                category=doc_data.get("category")
            )
            
        except Exception as e:
            logger.error(f"Failed to add document: {e}")
            self.stats["errors"].append(f"Document add error: {e}")
            return False
    
    async def _add_sql_example_from_data(self, example_data: Dict[str, Any]) -> bool:
        """从数据字典添加SQL范例"""
        try:
            required_fields = ["question", "sql"]
            for field in required_fields:
                if field not in example_data:
                    logger.error(f"Missing required field '{field}' in SQL example data")
                    return False
            
            return await self.kb_manager.add_sql_example(
                question=example_data["question"],
                sql=example_data["sql"],
                explanation=example_data.get("explanation"),
                difficulty=example_data.get("difficulty")
            )
            
        except Exception as e:
            logger.error(f"Failed to add SQL example: {e}")
            self.stats["errors"].append(f"SQL example add error: {e}")
            return False
    
    async def generate_embeddings(self) -> bool:
        """
        为所有数据生成嵌入向量
        
        Returns:
            bool: 是否生成成功
        """
        try:
            logger.info("Generating embeddings for all knowledge base items...")
            results = await self.kb_manager.generate_embeddings_for_all()
            
            total_embeddings = sum(results.values())
            self.stats["embeddings_generated"] = total_embeddings
            
            logger.info(f"Generated {total_embeddings} embeddings: {results}")
            return total_embeddings > 0
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            self.stats["errors"].append(f"Embedding generation error: {e}")
            return False
    
    async def rebuild_knowledge_base(self) -> bool:
        """
        重建知识库（清空后重新加载）
        
        Returns:
            bool: 是否重建成功
        """
        try:
            logger.info("Rebuilding knowledge base...")
            
            # 清空现有数据
            if not await self.kb_manager.clear_all():
                logger.error("Failed to clear existing knowledge base")
                return False
            
            # 重置统计信息
            self.stats = {
                "schemas_added": 0,
                "docs_added": 0,
                "examples_added": 0,
                "embeddings_generated": 0,
                "errors": []
            }
            
            logger.info("Knowledge base cleared, ready for new data")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rebuild knowledge base: {e}")
            self.stats["errors"].append(f"Rebuild error: {e}")
            return False
    
    def get_training_stats(self) -> Dict[str, Any]:
        """获取训练统计信息"""
        return {
            **self.stats,
            "total_items_added": (
                self.stats["schemas_added"] + 
                self.stats["docs_added"] + 
                self.stats["examples_added"]
            ),
            "success_rate": self._calculate_success_rate(),
            "timestamp": datetime.now().isoformat()
        }
    
    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        total_attempts = (
            self.stats["schemas_added"] + 
            self.stats["docs_added"] + 
            self.stats["examples_added"] + 
            len(self.stats["errors"])
        )
        
        if total_attempts == 0:
            return 0.0
        
        successful_attempts = (
            self.stats["schemas_added"] + 
            self.stats["docs_added"] + 
            self.stats["examples_added"]
        )
        
        return successful_attempts / total_attempts
    
    def print_stats(self) -> None:
        """打印训练统计信息"""
        stats = self.get_training_stats()
        
        print("\n" + "=" * 50)
        print("📊 知识库训练统计")
        print("=" * 50)
        print(f"Schema数量: {stats['schemas_added']}")
        print(f"业务文档数量: {stats['docs_added']}")
        print(f"SQL范例数量: {stats['examples_added']}")
        print(f"总项目数: {stats['total_items_added']}")
        print(f"生成嵌入向量数: {stats['embeddings_generated']}")
        print(f"成功率: {stats['success_rate']:.2%}")
        
        if stats['errors']:
            print(f"\n❌ 错误数量: {len(stats['errors'])}")
            for i, error in enumerate(stats['errors'][:5], 1):  # 只显示前5个错误
                print(f"  {i}. {error}")
            if len(stats['errors']) > 5:
                print(f"  ... 还有 {len(stats['errors']) - 5} 个错误")
        
        print(f"\n⏰ 完成时间: {stats['timestamp']}")
        print("=" * 50)


async def create_sample_training_data(output_path: Union[str, Path]) -> None:
    """
    创建示例训练数据文件
    
    Args:
        output_path: 输出文件路径
    """
    sample_data = {
        "schemas": [
            {
                "table_name": "creators",
                "ddl": """CREATE TABLE creators (
                    id BIGINT PRIMARY KEY,
                    username VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(200),
                    category VARCHAR(50),
                    follower_count BIGINT DEFAULT 0,
                    following_count BIGINT DEFAULT 0,
                    video_count INT DEFAULT 0,
                    verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )""",
                "description": "TikTok达人基础信息表，存储达人的基本资料和统计数据"
            },
            {
                "table_name": "videos",
                "ddl": """CREATE TABLE videos (
                    id BIGINT PRIMARY KEY,
                    creator_id BIGINT NOT NULL,
                    title VARCHAR(500),
                    description TEXT,
                    duration_seconds INT,
                    view_count BIGINT DEFAULT 0,
                    like_count BIGINT DEFAULT 0,
                    comment_count BIGINT DEFAULT 0,
                    share_count BIGINT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES creators(id)
                )""",
                "description": "TikTok视频信息表，存储视频内容和互动数据"
            },
            {
                "table_name": "daily_metrics",
                "ddl": """CREATE TABLE daily_metrics (
                    id BIGINT PRIMARY KEY,
                    creator_id BIGINT NOT NULL,
                    date DATE NOT NULL,
                    follower_count BIGINT,
                    follower_growth INT,
                    total_views BIGINT,
                    total_likes BIGINT,
                    video_count INT,
                    avg_engagement_rate DECIMAL(5,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES creators(id),
                    UNIQUE(creator_id, date)
                )""",
                "description": "达人每日指标表，用于跟踪达人的日常表现和增长趋势"
            }
        ],
        "documents": [
            {
                "title": "涨粉量计算规则",
                "content": """涨粉量的计算方式和规则：

1. 基本计算公式：
   涨粉量 = 当前粉丝数 - 前一天粉丝数

2. 计算规则：
   - 使用daily_metrics表中的follower_count字段
   - 比较相邻两天的数据
   - 新达人（没有历史数据）的涨粉量等于当前粉丝数
   - 涨粉量可能为负数，表示掉粉

3. 示例SQL：
   SELECT 
       c.username,
       dm.follower_count - LAG(dm.follower_count) OVER (
           PARTITION BY dm.creator_id ORDER BY dm.date
       ) as follower_growth
   FROM daily_metrics dm
   JOIN creators c ON dm.creator_id = c.id
   WHERE dm.date = CURRENT_DATE;

4. 注意事项：
   - 数据更新时间为每日凌晨2点
   - 异常数据（如粉丝数突然归零）需要人工审核
   - 涨粉量超过10万的情况需要特别关注""",
                "category": "business_rules"
            },
            {
                "title": "互动率计算方法",
                "content": """互动率的计算方法和标准：

1. 基本公式：
   互动率 = (点赞数 + 评论数 + 分享数) / 播放量 * 100%

2. 计算维度：
   - 单个视频互动率：针对特定视频
   - 达人平均互动率：所有视频的平均值
   - 时间段互动率：特定时间范围内的平均值

3. 分级标准：
   - 优秀：互动率 > 8%
   - 良好：5% < 互动率 <= 8%
   - 一般：2% < 互动率 <= 5%
   - 较低：互动率 <= 2%

4. 示例SQL：
   SELECT 
       v.id,
       v.title,
       (v.like_count + v.comment_count + v.share_count) * 100.0 / v.view_count as engagement_rate
   FROM videos v
   WHERE v.view_count > 0
   ORDER BY engagement_rate DESC;

5. 应用场景：
   - 达人价值评估
   - 内容质量分析
   - 投放效果预测""",
                "category": "business_rules"
            },
            {
                "title": "达人分类体系",
                "content": """TikTok达人分类体系说明：

1. 主要分类：
   - gaming: 游戏类达人
   - beauty: 美妆类达人
   - food: 美食类达人
   - dance: 舞蹈类达人
   - comedy: 搞笑类达人
   - education: 教育类达人
   - lifestyle: 生活方式类达人
   - music: 音乐类达人
   - sports: 体育类达人
   - tech: 科技类达人
   - fashion: 时尚类达人
   - travel: 旅行类达人

2. 分类规则：
   - 基于达人发布内容的主要类型
   - 一个达人只能属于一个主分类
   - 分类由算法自动识别，人工审核确认
   - 分类可能随时间调整

3. 分类用途：
   - 内容推荐算法
   - 广告投放定向
   - 达人价值评估
   - 行业分析报告

4. 查询示例：
   SELECT category, COUNT(*) as creator_count
   FROM creators
   WHERE category IS NOT NULL
   GROUP BY category
   ORDER BY creator_count DESC;""",
                "category": "data_dictionary"
            }
        ],
        "sql_examples": [
            {
                "question": "过去一周哪个游戏达人播放量最高？",
                "sql": """SELECT 
    c.username,
    c.display_name,
    SUM(v.view_count) as total_views
FROM creators c
JOIN videos v ON c.id = v.creator_id
WHERE c.category = 'gaming'
    AND v.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY c.id, c.username, c.display_name
ORDER BY total_views DESC
LIMIT 1;""",
                "explanation": "查询游戏分类达人在过去7天内发布视频的总播放量，按播放量降序排列取第一名",
                "difficulty": "medium"
            },
            {
                "question": "美妆类视频的平均点赞率是多少？",
                "sql": """SELECT 
    AVG(CASE WHEN v.view_count > 0 THEN v.like_count * 1.0 / v.view_count ELSE 0 END) as avg_like_rate
FROM videos v
JOIN creators c ON v.creator_id = c.id
WHERE c.category = 'beauty'
    AND v.view_count > 0;""",
                "explanation": "计算美妆类达人所有视频的平均点赞率，排除播放量为0的视频避免除零错误",
                "difficulty": "medium"
            },
            {
                "question": "今天涨粉最多的前10个达人是谁？",
                "sql": """SELECT 
    c.username,
    c.display_name,
    c.category,
    dm.follower_growth
FROM daily_metrics dm
JOIN creators c ON dm.creator_id = c.id
WHERE dm.date = CURRENT_DATE
    AND dm.follower_growth > 0
ORDER BY dm.follower_growth DESC
LIMIT 10;""",
                "explanation": "查询今日涨粉量最多的前10个达人，使用daily_metrics表中预计算的follower_growth字段",
                "difficulty": "easy"
            },
            {
                "question": "各个分类达人的平均粉丝数和视频数是多少？",
                "sql": """SELECT 
    category,
    COUNT(*) as creator_count,
    AVG(follower_count) as avg_followers,
    AVG(video_count) as avg_videos,
    SUM(follower_count) as total_followers
FROM creators
WHERE category IS NOT NULL
GROUP BY category
ORDER BY avg_followers DESC;""",
                "explanation": "按达人分类统计各项指标，包括达人数量、平均粉丝数、平均视频数和总粉丝数",
                "difficulty": "easy"
            },
            {
                "question": "找出互动率最高的100个视频",
                "sql": """SELECT 
    v.id,
    v.title,
    c.username,
    v.view_count,
    v.like_count,
    v.comment_count,
    v.share_count,
    (v.like_count + v.comment_count + v.share_count) * 100.0 / v.view_count as engagement_rate
FROM videos v
JOIN creators c ON v.creator_id = c.id
WHERE v.view_count >= 1000  -- 过滤掉播放量过低的视频
ORDER BY engagement_rate DESC
LIMIT 100;""",
                "explanation": "计算所有视频的互动率并排序，只考虑播放量大于等于1000的视频以确保数据质量",
                "difficulty": "medium"
            },
            {
                "question": "查询某个达人最近30天的粉丝增长趋势",
                "sql": """SELECT 
    dm.date,
    dm.follower_count,
    dm.follower_growth,
    SUM(dm.follower_growth) OVER (
        ORDER BY dm.date 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as weekly_growth
FROM daily_metrics dm
JOIN creators c ON dm.creator_id = c.id
WHERE c.username = 'target_username'
    AND dm.date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY dm.date;""",
                "explanation": "查询指定达人最近30天的每日粉丝数据，并计算7天滚动涨粉量，用于分析增长趋势",
                "difficulty": "hard"
            }
        ]
    }
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Sample training data created at: {output_path}")


async def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="知识库训练脚本")
    parser.add_argument("--config", "-c", help="配置文件路径")
    parser.add_argument("--db-path", help="知识库数据库路径", default="data/knowledge_base.db")
    parser.add_argument("--input", "-i", help="输入文件或目录路径")
    parser.add_argument("--format", choices=["json", "yaml", "auto"], default="auto", help="输入格式")
    parser.add_argument("--rebuild", action="store_true", help="重建知识库（清空现有数据）")
    parser.add_argument("--generate-embeddings", action="store_true", help="生成嵌入向量")
    parser.add_argument("--create-sample", help="创建示例训练数据文件")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.INFO)
    else:
        logging.basicConfig(level=logging.WARNING)
    
    try:
        # 创建示例数据
        if args.create_sample:
            await create_sample_training_data(args.create_sample)
            print(f"✅ 示例训练数据已创建: {args.create_sample}")
            return
        
        # 加载配置
        if args.config:
            # 如果指定了配置文件，需要设置环境变量或修改加载逻辑
            print(f"⚠️  注意：当前版本暂不支持自定义配置文件路径，使用默认配置")
        
        config = load_config()
        print("✅ 配置加载成功")
        
        # 创建知识库管理器
        kb_manager = KnowledgeBaseManager(db_path=args.db_path)
        trainer = KnowledgeTrainer(kb_manager)
        
        # 重建知识库
        if args.rebuild:
            print("🔄 重建知识库...")
            if await trainer.rebuild_knowledge_base():
                print("✅ 知识库重建成功")
            else:
                print("❌ 知识库重建失败")
                return
        
        # 加载训练数据
        if args.input:
            input_path = Path(args.input)
            
            if input_path.is_file():
                # 单个文件
                if args.format == "json" or (args.format == "auto" and input_path.suffix == ".json"):
                    success = await trainer.load_from_json(input_path)
                elif args.format == "yaml" or (args.format == "auto" and input_path.suffix in [".yaml", ".yml"]):
                    success = await trainer.load_from_yaml(input_path)
                else:
                    print(f"❌ 不支持的文件格式: {input_path.suffix}")
                    return
                
                if success:
                    print(f"✅ 成功加载训练数据: {input_path}")
                else:
                    print(f"❌ 加载训练数据失败: {input_path}")
            
            elif input_path.is_dir():
                # 目录批量加载
                print(f"📁 从目录批量加载: {input_path}")
                success = await trainer.load_from_directory(input_path)
                
                if success:
                    print("✅ 目录加载完成")
                else:
                    print("❌ 目录加载失败")
            
            else:
                print(f"❌ 输入路径不存在: {input_path}")
                return
        
        # 生成嵌入向量
        if args.generate_embeddings:
            print("🔤 生成嵌入向量...")
            if await trainer.generate_embeddings():
                print("✅ 嵌入向量生成完成")
            else:
                print("❌ 嵌入向量生成失败")
        
        # 显示统计信息
        trainer.print_stats()
        
        # 关闭资源
        await kb_manager.close()
        
    except Exception as e:
        print(f"❌ 训练过程出现错误: {e}")
        import traceback
        if args.verbose:
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())