"""
测试运行器 - 运行所有测试套件
"""

import pytest
import sys
import os
import time
from pathlib import Path
import argparse
from typing import List, Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行单元测试"""
        print("=" * 60)
        print("运行单元测试...")
        print("=" * 60)
        
        unit_test_files = [
            "tests/test_models.py",
            "tests/test_config.py", 
            "tests/test_knowledge_base.py",
            "tests/test_knowledge_trainer.py",
            "tests/test_qwen_embedding.py",
            "tests/test_router_agent.py",
            "tests/test_display_agent.py",
            "tests/test_agent_coordinator.py",
            "tests/test_vanna_core.py",
            "tests/test_error_handler.py"
        ]
        
        args = ["-v"] if verbose else []
        args.extend(["--tb=short", "--maxfail=5"])
        
        start_time = time.time()
        result = pytest.main(args + unit_test_files)
        end_time = time.time()
        
        return {
            "name": "单元测试",
            "result": result,
            "duration": end_time - start_time,
            "files": unit_test_files
        }
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行集成测试"""
        print("=" * 60)
        print("运行集成测试...")
        print("=" * 60)
        
        integration_test_files = [
            "tests/integration/test_end_to_end_workflow.py",
            "tests/integration/test_api_integration.py",
            "tests/integration/test_ui_integration.py"
        ]
        
        args = ["-v"] if verbose else []
        args.extend(["--tb=short", "--maxfail=3"])
        
        start_time = time.time()
        result = pytest.main(args + integration_test_files)
        end_time = time.time()
        
        return {
            "name": "集成测试",
            "result": result,
            "duration": end_time - start_time,
            "files": integration_test_files
        }
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行性能测试"""
        print("=" * 60)
        print("运行性能测试...")
        print("=" * 60)
        
        performance_test_files = [
            "tests/performance/test_load_testing.py",
            "tests/performance/test_stress_testing.py"
        ]
        
        args = ["-v"] if verbose else []
        args.extend(["--tb=short", "--maxfail=2", "-s"])  # -s显示print输出
        
        start_time = time.time()
        result = pytest.main(args + performance_test_files)
        end_time = time.time()
        
        return {
            "name": "性能测试",
            "result": result,
            "duration": end_time - start_time,
            "files": performance_test_files
        }
    
    def run_specific_tests(self, test_pattern: str, verbose: bool = False) -> Dict[str, Any]:
        """运行特定测试"""
        print("=" * 60)
        print(f"运行特定测试: {test_pattern}")
        print("=" * 60)
        
        args = ["-v"] if verbose else []
        args.extend(["--tb=short", "-k", test_pattern])
        
        start_time = time.time()
        result = pytest.main(args + ["tests/"])
        end_time = time.time()
        
        return {
            "name": f"特定测试 ({test_pattern})",
            "result": result,
            "duration": end_time - start_time,
            "pattern": test_pattern
        }
    
    def run_all_tests(self, verbose: bool = False, skip_performance: bool = False) -> Dict[str, List[Dict[str, Any]]]:
        """运行所有测试"""
        print("🚀 开始运行TikTok AI Agent测试套件")
        print("=" * 80)
        
        self.start_time = time.time()
        results = []
        
        # 运行单元测试
        try:
            unit_result = self.run_unit_tests(verbose)
            results.append(unit_result)
        except Exception as e:
            print(f"单元测试运行失败: {e}")
            results.append({
                "name": "单元测试",
                "result": 1,
                "duration": 0,
                "error": str(e)
            })
        
        # 运行集成测试
        try:
            integration_result = self.run_integration_tests(verbose)
            results.append(integration_result)
        except Exception as e:
            print(f"集成测试运行失败: {e}")
            results.append({
                "name": "集成测试", 
                "result": 1,
                "duration": 0,
                "error": str(e)
            })
        
        # 运行性能测试（可选）
        if not skip_performance:
            try:
                performance_result = self.run_performance_tests(verbose)
                results.append(performance_result)
            except Exception as e:
                print(f"性能测试运行失败: {e}")
                results.append({
                    "name": "性能测试",
                    "result": 1,
                    "duration": 0,
                    "error": str(e)
                })
        
        self.end_time = time.time()
        
        return {
            "results": results,
            "total_duration": self.end_time - self.start_time
        }
    
    def generate_report(self, test_results: Dict[str, Any]) -> str:
        """生成测试报告"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("🧪 TikTok AI Agent 测试报告")
        report_lines.append("=" * 80)
        
        total_duration = test_results["total_duration"]
        results = test_results["results"]
        
        # 总体统计
        passed_suites = sum(1 for r in results if r["result"] == 0)
        failed_suites = len(results) - passed_suites
        
        report_lines.append(f"📊 总体统计:")
        report_lines.append(f"   测试套件总数: {len(results)}")
        report_lines.append(f"   通过套件: {passed_suites}")
        report_lines.append(f"   失败套件: {failed_suites}")
        report_lines.append(f"   总耗时: {total_duration:.2f}秒")
        report_lines.append("")
        
        # 详细结果
        report_lines.append("📋 详细结果:")
        for result in results:
            status = "✅ 通过" if result["result"] == 0 else "❌ 失败"
            duration = result.get("duration", 0)
            
            report_lines.append(f"   {result['name']}: {status} ({duration:.2f}秒)")
            
            if "error" in result:
                report_lines.append(f"      错误: {result['error']}")
            
            if "files" in result:
                report_lines.append(f"      文件数: {len(result['files'])}")
        
        report_lines.append("")
        
        # 建议
        report_lines.append("💡 建议:")
        if failed_suites == 0:
            report_lines.append("   🎉 所有测试都通过了！系统质量良好。")
        else:
            report_lines.append("   🔧 有测试失败，请检查并修复相关问题。")
            report_lines.append("   📝 查看详细的测试输出以了解失败原因。")
        
        if total_duration > 300:  # 5分钟
            report_lines.append("   ⚡ 测试耗时较长，考虑优化测试性能。")
        
        report_lines.append("")
        report_lines.append("=" * 80)
        
        return "\n".join(report_lines)
    
    def save_report(self, report: str, filename: str = "test_report.txt"):
        """保存测试报告"""
        report_path = Path("test_reports") / filename
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"📄 测试报告已保存到: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TikTok AI Agent 测试运行器")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "all"], 
                       default="all", help="测试类型")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--skip-performance", action="store_true", help="跳过性能测试")
    parser.add_argument("--pattern", "-k", help="运行匹配模式的测试")
    parser.add_argument("--report", help="保存报告的文件名")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.pattern:
            # 运行特定测试
            result = runner.run_specific_tests(args.pattern, args.verbose)
            results = {"results": [result], "total_duration": result["duration"]}
        elif args.type == "unit":
            result = runner.run_unit_tests(args.verbose)
            results = {"results": [result], "total_duration": result["duration"]}
        elif args.type == "integration":
            result = runner.run_integration_tests(args.verbose)
            results = {"results": [result], "total_duration": result["duration"]}
        elif args.type == "performance":
            result = runner.run_performance_tests(args.verbose)
            results = {"results": [result], "total_duration": result["duration"]}
        else:
            # 运行所有测试
            results = runner.run_all_tests(args.verbose, args.skip_performance)
        
        # 生成报告
        report = runner.generate_report(results)
        print(report)
        
        # 保存报告
        if args.report:
            runner.save_report(report, args.report)
        else:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            runner.save_report(report, f"test_report_{timestamp}.txt")
        
        # 返回适当的退出码
        failed_suites = sum(1 for r in results["results"] if r["result"] != 0)
        sys.exit(failed_suites)
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试运行器发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()