"""
负载测试和性能测试
"""

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import patch, AsyncMock
import psutil
import threading
from typing import List, Dict, Any

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from agents.agent_coordinator import AgentCoordinator
from tests.test_utils import MockDataGenerator, MockServices


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.response_times: List[float] = []
        self.success_count = 0
        self.error_count = 0
        self.start_time = None
        self.end_time = None
        self.memory_usage: List[float] = []
        self.cpu_usage: List[float] = []
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.response_times.clear()
        self.success_count = 0
        self.error_count = 0
        self.memory_usage.clear()
        self.cpu_usage.clear()
    
    def record_response(self, response_time: float, success: bool):
        """记录响应"""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    def record_system_metrics(self):
        """记录系统指标"""
        process = psutil.Process()
        self.memory_usage.append(process.memory_info().rss / 1024 / 1024)  # MB
        self.cpu_usage.append(process.cpu_percent())
    
    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.response_times:
            return {"error": "No data collected"}
        
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        total_requests = self.success_count + self.error_count
        
        return {
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / total_requests if total_requests > 0 else 0,
            "total_time": total_time,
            "requests_per_second": total_requests / total_time if total_time > 0 else 0,
            "response_times": {
                "min": min(self.response_times),
                "max": max(self.response_times),
                "mean": statistics.mean(self.response_times),
                "median": statistics.median(self.response_times),
                "p95": self._percentile(self.response_times, 95),
                "p99": self._percentile(self.response_times, 99)
            },
            "memory_usage": {
                "min": min(self.memory_usage) if self.memory_usage else 0,
                "max": max(self.memory_usage) if self.memory_usage else 0,
                "mean": statistics.mean(self.memory_usage) if self.memory_usage else 0
            },
            "cpu_usage": {
                "min": min(self.cpu_usage) if self.cpu_usage else 0,
                "max": max(self.cpu_usage) if self.cpu_usage else 0,
                "mean": statistics.mean(self.cpu_usage) if self.cpu_usage else 0
            }
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]


class TestConcurrentLoad:
    """并发负载测试"""
    
    @pytest.fixture
    async def coordinator_with_fast_mocks(self):
        """创建带快速模拟的协调器"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 创建快速响应的模拟
                    mock_vanna = AsyncMock()
                    mock_router = AsyncMock()
                    mock_display = AsyncMock()
                    
                    mock_vanna_class.return_value = mock_vanna
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = mock_display
                    
                    # 配置快速响应
                    async def fast_response(*args, **kwargs):
                        await asyncio.sleep(0.01)  # 10ms模拟处理时间
                        return {
                            "success": True,
                            "message": "快速响应",
                            "data": {
                                "type": "complete_analysis",
                                "raw_data": {"data": [], "columns": [], "row_count": 0},
                                "analysis": {"summary": "快速分析"},
                                "visualizations": []
                            }
                        }
                    
                    mock_router.process_user_input.side_effect = fast_response
                    mock_display.create_report.return_value = MockDataGenerator.generate_report()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    yield coordinator
                    
                    await coordinator.close()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, coordinator_with_fast_mocks):
        """测试并发请求性能"""
        coordinator = coordinator_with_fast_mocks
        metrics = PerformanceMetrics()
        
        # 测试参数
        concurrent_users = 10
        requests_per_user = 5
        
        metrics.start_monitoring()
        
        async def user_simulation(user_id: int):
            """模拟用户行为"""
            user_metrics = []
            
            for request_id in range(requests_per_user):
                start_time = time.time()
                
                try:
                    response = await coordinator.process_user_request(
                        user_input=f"用户{user_id}的查询{request_id}",
                        session_id=f"session_{user_id}",
                        user_id=f"user_{user_id}"
                    )
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    success = response.get("success", False)
                    
                    user_metrics.append((response_time, success))
                    
                except Exception as e:
                    end_time = time.time()
                    response_time = end_time - start_time
                    user_metrics.append((response_time, False))
            
            return user_metrics
        
        # 启动系统监控
        monitoring_active = True
        
        def system_monitor():
            while monitoring_active:
                metrics.record_system_metrics()
                time.sleep(0.1)
        
        monitor_thread = threading.Thread(target=system_monitor)
        monitor_thread.start()
        
        # 执行并发测试
        tasks = [user_simulation(i) for i in range(concurrent_users)]
        results = await asyncio.gather(*tasks)
        
        # 停止监控
        monitoring_active = False
        monitor_thread.join()
        
        # 收集结果
        for user_results in results:
            for response_time, success in user_results:
                metrics.record_response(response_time, success)
        
        metrics.stop_monitoring()
        
        # 分析结果
        summary = metrics.get_summary()
        
        print(f"\n=== 并发性能测试结果 ===")
        print(f"总请求数: {summary['total_requests']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"QPS: {summary['requests_per_second']:.2f}")
        print(f"平均响应时间: {summary['response_times']['mean']:.3f}s")
        print(f"P95响应时间: {summary['response_times']['p95']:.3f}s")
        print(f"P99响应时间: {summary['response_times']['p99']:.3f}s")
        print(f"内存使用: {summary['memory_usage']['mean']:.2f}MB")
        print(f"CPU使用: {summary['cpu_usage']['mean']:.2f}%")
        
        # 性能断言
        assert summary['success_rate'] >= 0.95  # 成功率应该 >= 95%
        assert summary['response_times']['mean'] <= 1.0  # 平均响应时间 <= 1秒
        assert summary['response_times']['p95'] <= 2.0  # P95响应时间 <= 2秒
        assert summary['requests_per_second'] >= 10  # QPS >= 10
    
    @pytest.mark.asyncio
    async def test_sustained_load_performance(self, coordinator_with_fast_mocks):
        """测试持续负载性能"""
        coordinator = coordinator_with_fast_mocks
        metrics = PerformanceMetrics()
        
        # 测试参数
        duration_seconds = 30  # 持续30秒
        concurrent_users = 5
        
        metrics.start_monitoring()
        
        async def sustained_user(user_id: int, duration: int):
            """持续负载用户"""
            end_time = time.time() + duration
            request_count = 0
            
            while time.time() < end_time:
                start_time = time.time()
                
                try:
                    response = await coordinator.process_user_request(
                        user_input=f"持续查询{request_count}",
                        session_id=f"sustained_session_{user_id}",
                        user_id=f"sustained_user_{user_id}"
                    )
                    
                    response_time = time.time() - start_time
                    success = response.get("success", False)
                    metrics.record_response(response_time, success)
                    
                    request_count += 1
                    
                    # 短暂休息模拟真实用户行为
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    response_time = time.time() - start_time
                    metrics.record_response(response_time, False)
        
        # 启动系统监控
        monitoring_active = True
        
        def system_monitor():
            while monitoring_active:
                metrics.record_system_metrics()
                time.sleep(1)
        
        monitor_thread = threading.Thread(target=system_monitor)
        monitor_thread.start()
        
        # 执行持续负载测试
        tasks = [sustained_user(i, duration_seconds) for i in range(concurrent_users)]
        await asyncio.gather(*tasks)
        
        # 停止监控
        monitoring_active = False
        monitor_thread.join()
        
        metrics.stop_monitoring()
        
        # 分析结果
        summary = metrics.get_summary()
        
        print(f"\n=== 持续负载测试结果 ===")
        print(f"测试时长: {duration_seconds}秒")
        print(f"总请求数: {summary['total_requests']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"平均QPS: {summary['requests_per_second']:.2f}")
        print(f"平均响应时间: {summary['response_times']['mean']:.3f}s")
        print(f"内存使用趋势: {summary['memory_usage']['min']:.2f}MB -> {summary['memory_usage']['max']:.2f}MB")
        
        # 性能断言
        assert summary['success_rate'] >= 0.90  # 持续负载下成功率 >= 90%
        assert summary['response_times']['mean'] <= 1.5  # 平均响应时间 <= 1.5秒
        assert summary['memory_usage']['max'] <= 500  # 内存使用 <= 500MB
    
    @pytest.mark.asyncio
    async def test_spike_load_performance(self, coordinator_with_fast_mocks):
        """测试突发负载性能"""
        coordinator = coordinator_with_fast_mocks
        metrics = PerformanceMetrics()
        
        metrics.start_monitoring()
        
        # 阶段1: 正常负载 (5个并发用户)
        print("阶段1: 正常负载...")
        normal_tasks = []
        for i in range(5):
            task = coordinator.process_user_request(
                f"正常负载查询{i}", f"normal_session_{i}", f"normal_user_{i}"
            )
            normal_tasks.append(task)
        
        normal_results = await asyncio.gather(*normal_tasks, return_exceptions=True)
        
        for i, result in enumerate(normal_results):
            success = not isinstance(result, Exception) and result.get("success", False)
            metrics.record_response(0.1, success)  # 假设响应时间
        
        # 阶段2: 突发负载 (50个并发用户)
        print("阶段2: 突发负载...")
        spike_tasks = []
        spike_start = time.time()
        
        for i in range(50):
            task = coordinator.process_user_request(
                f"突发负载查询{i}", f"spike_session_{i}", f"spike_user_{i}"
            )
            spike_tasks.append(task)
        
        spike_results = await asyncio.gather(*spike_tasks, return_exceptions=True)
        spike_end = time.time()
        
        for i, result in enumerate(spike_results):
            success = not isinstance(result, Exception) and result.get("success", False)
            response_time = (spike_end - spike_start) / len(spike_results)
            metrics.record_response(response_time, success)
        
        # 阶段3: 恢复正常负载
        print("阶段3: 恢复正常负载...")
        recovery_tasks = []
        for i in range(5):
            task = coordinator.process_user_request(
                f"恢复负载查询{i}", f"recovery_session_{i}", f"recovery_user_{i}"
            )
            recovery_tasks.append(task)
        
        recovery_results = await asyncio.gather(*recovery_tasks, return_exceptions=True)
        
        for i, result in enumerate(recovery_results):
            success = not isinstance(result, Exception) and result.get("success", False)
            metrics.record_response(0.1, success)
        
        metrics.stop_monitoring()
        
        # 分析结果
        summary = metrics.get_summary()
        
        print(f"\n=== 突发负载测试结果 ===")
        print(f"总请求数: {summary['total_requests']}")
        print(f"成功率: {summary['success_rate']:.2%}")
        print(f"最大响应时间: {summary['response_times']['max']:.3f}s")
        print(f"P99响应时间: {summary['response_times']['p99']:.3f}s")
        
        # 突发负载容忍度断言
        assert summary['success_rate'] >= 0.80  # 突发负载下成功率 >= 80%
        assert summary['response_times']['p99'] <= 5.0  # P99响应时间 <= 5秒


class TestMemoryPerformance:
    """内存性能测试"""
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = AsyncMock()
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 记录初始内存使用
                    process = psutil.Process()
                    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
                    
                    memory_samples = [initial_memory]
                    
                    # 执行大量请求
                    for batch in range(10):
                        print(f"执行批次 {batch + 1}/10...")
                        
                        # 每批次100个请求
                        tasks = []
                        for i in range(100):
                            task = coordinator.process_user_request(
                                f"内存测试查询{batch}_{i}",
                                f"memory_session_{batch}_{i}",
                                f"memory_user_{batch}_{i}"
                            )
                            tasks.append(task)
                        
                        await asyncio.gather(*tasks, return_exceptions=True)
                        
                        # 记录内存使用
                        current_memory = process.memory_info().rss / 1024 / 1024
                        memory_samples.append(current_memory)
                        
                        print(f"当前内存使用: {current_memory:.2f}MB")
                        
                        # 强制垃圾回收
                        import gc
                        gc.collect()
                    
                    await coordinator.close()
                    
                    # 分析内存使用趋势
                    final_memory = memory_samples[-1]
                    memory_growth = final_memory - initial_memory
                    
                    print(f"\n=== 内存泄漏检测结果 ===")
                    print(f"初始内存: {initial_memory:.2f}MB")
                    print(f"最终内存: {final_memory:.2f}MB")
                    print(f"内存增长: {memory_growth:.2f}MB")
                    print(f"内存增长率: {(memory_growth / initial_memory * 100):.2f}%")
                    
                    # 内存泄漏断言
                    assert memory_growth <= 100  # 内存增长不超过100MB
                    assert memory_growth / initial_memory <= 0.5  # 内存增长不超过50%
    
    @pytest.mark.asyncio
    async def test_large_data_memory_usage(self):
        """测试大数据内存使用"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 配置大数据响应
                    mock_router = AsyncMock()
                    large_data = MockDataGenerator.generate_creator_data(10000)  # 10K记录
                    
                    mock_router.process_user_input.return_value = {
                        "success": True,
                        "data": {
                            "type": "complete_analysis",
                            "raw_data": {
                                "data": large_data,
                                "columns": ["creator_name", "follower_count"],
                                "row_count": len(large_data)
                            },
                            "analysis": {"summary": "大数据分析"},
                            "visualizations": []
                        }
                    }
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 记录处理大数据前的内存
                    process = psutil.Process()
                    before_memory = process.memory_info().rss / 1024 / 1024
                    
                    # 处理大数据请求
                    response = await coordinator.process_user_request(
                        "获取所有达人数据",
                        "large_data_session",
                        "large_data_user"
                    )
                    
                    # 记录处理后的内存
                    after_memory = process.memory_info().rss / 1024 / 1024
                    memory_increase = after_memory - before_memory
                    
                    print(f"\n=== 大数据内存使用测试结果 ===")
                    print(f"数据记录数: {len(large_data)}")
                    print(f"处理前内存: {before_memory:.2f}MB")
                    print(f"处理后内存: {after_memory:.2f}MB")
                    print(f"内存增长: {memory_increase:.2f}MB")
                    
                    await coordinator.close()
                    
                    # 大数据处理内存断言
                    assert memory_increase <= 200  # 处理10K记录内存增长不超过200MB
                    assert response["success"] is True


class TestResponseTimePerformance:
    """响应时间性能测试"""
    
    @pytest.mark.asyncio
    async def test_response_time_distribution(self):
        """测试响应时间分布"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 配置不同响应时间的模拟
                    mock_router = AsyncMock()
                    
                    async def variable_response_time(*args, **kwargs):
                        # 模拟不同的处理时间
                        import random
                        delay = random.uniform(0.01, 0.5)  # 10ms到500ms
                        await asyncio.sleep(delay)
                        
                        return {
                            "success": True,
                            "data": {"type": "chat", "response": "变长响应"}
                        }
                    
                    mock_router.process_user_input.side_effect = variable_response_time
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 收集响应时间数据
                    response_times = []
                    
                    for i in range(100):
                        start_time = time.time()
                        
                        response = await coordinator.process_user_request(
                            f"响应时间测试{i}",
                            f"rt_session_{i}",
                            f"rt_user_{i}"
                        )
                        
                        end_time = time.time()
                        response_time = end_time - start_time
                        response_times.append(response_time)
                    
                    await coordinator.close()
                    
                    # 分析响应时间分布
                    response_times.sort()
                    
                    print(f"\n=== 响应时间分布测试结果 ===")
                    print(f"样本数量: {len(response_times)}")
                    print(f"最小响应时间: {min(response_times):.3f}s")
                    print(f"最大响应时间: {max(response_times):.3f}s")
                    print(f"平均响应时间: {statistics.mean(response_times):.3f}s")
                    print(f"中位数响应时间: {statistics.median(response_times):.3f}s")
                    print(f"P90响应时间: {response_times[int(len(response_times) * 0.9)]:.3f}s")
                    print(f"P95响应时间: {response_times[int(len(response_times) * 0.95)]:.3f}s")
                    print(f"P99响应时间: {response_times[int(len(response_times) * 0.99)]:.3f}s")
                    
                    # 响应时间分布断言
                    assert statistics.mean(response_times) <= 1.0  # 平均响应时间 <= 1秒
                    assert response_times[int(len(response_times) * 0.95)] <= 2.0  # P95 <= 2秒
                    assert response_times[int(len(response_times) * 0.99)] <= 3.0  # P99 <= 3秒


class TestResourceUtilization:
    """资源利用率测试"""
    
    @pytest.mark.asyncio
    async def test_cpu_utilization(self):
        """测试CPU利用率"""
        with patch('agents.agent_coordinator.VannaCore') as mock_vanna_class:
            with patch('agents.agent_coordinator.RouterAgent') as mock_router_class:
                with patch('agents.agent_coordinator.DisplayAgent') as mock_display_class:
                    
                    # 配置CPU密集型模拟
                    mock_router = AsyncMock()
                    
                    async def cpu_intensive_task(*args, **kwargs):
                        # 模拟CPU密集型任务
                        import math
                        for _ in range(10000):
                            math.sqrt(12345)
                        
                        return {
                            "success": True,
                            "data": {"type": "data_query", "result": "CPU密集型结果"}
                        }
                    
                    mock_router.process_user_input.side_effect = cpu_intensive_task
                    
                    mock_vanna_class.return_value = AsyncMock()
                    mock_router_class.return_value = mock_router
                    mock_display_class.return_value = AsyncMock()
                    
                    coordinator = AgentCoordinator()
                    await coordinator.start()
                    
                    # 监控CPU使用率
                    process = psutil.Process()
                    cpu_samples = []
                    
                    # 启动CPU监控
                    monitoring_active = True
                    
                    def cpu_monitor():
                        while monitoring_active:
                            cpu_percent = process.cpu_percent(interval=0.1)
                            cpu_samples.append(cpu_percent)
                    
                    monitor_thread = threading.Thread(target=cpu_monitor)
                    monitor_thread.start()
                    
                    # 执行CPU密集型任务
                    tasks = []
                    for i in range(20):
                        task = coordinator.process_user_request(
                            f"CPU测试{i}",
                            f"cpu_session_{i}",
                            f"cpu_user_{i}"
                        )
                        tasks.append(task)
                    
                    await asyncio.gather(*tasks)
                    
                    # 停止监控
                    monitoring_active = False
                    monitor_thread.join()
                    
                    await coordinator.close()
                    
                    # 分析CPU使用率
                    if cpu_samples:
                        avg_cpu = statistics.mean(cpu_samples)
                        max_cpu = max(cpu_samples)
                        
                        print(f"\n=== CPU利用率测试结果 ===")
                        print(f"平均CPU使用率: {avg_cpu:.2f}%")
                        print(f"最大CPU使用率: {max_cpu:.2f}%")
                        print(f"CPU样本数: {len(cpu_samples)}")
                        
                        # CPU利用率断言
                        assert avg_cpu <= 80  # 平均CPU使用率不超过80%
                        assert max_cpu <= 95  # 最大CPU使用率不超过95%


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])