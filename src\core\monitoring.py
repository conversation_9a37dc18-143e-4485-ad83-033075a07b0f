"""
监控和日志系统
提供系统指标收集、健康检查和性能监控
"""

import asyncio
import logging
import time
import psutil
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
import threading
import json
import os

from .config import get_config

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_io: Dict[str, int] = field(default_factory=dict)
    process_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_used_mb": self.memory_used_mb,
            "memory_available_mb": self.memory_available_mb,
            "disk_usage_percent": self.disk_usage_percent,
            "network_io": self.network_io,
            "process_count": self.process_count
        }


@dataclass
class ApplicationMetrics:
    """应用指标"""
    timestamp: datetime
    active_sessions: int = 0
    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    average_response_time: float = 0.0
    active_tasks: int = 0
    pending_tasks: int = 0
    cache_hit_rate: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "active_sessions": self.active_sessions,
            "total_queries": self.total_queries,
            "successful_queries": self.successful_queries,
            "failed_queries": self.failed_queries,
            "success_rate": self.successful_queries / max(self.total_queries, 1),
            "average_response_time": self.average_response_time,
            "active_tasks": self.active_tasks,
            "pending_tasks": self.pending_tasks,
            "cache_hit_rate": self.cache_hit_rate
        }


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "tags": self.tags
        }


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history_size: int = 1000):
        """
        初始化指标收集器
        
        Args:
            max_history_size: 最大历史记录大小
        """
        self.max_history_size = max_history_size
        self.system_metrics_history: deque = deque(maxlen=max_history_size)
        self.app_metrics_history: deque = deque(maxlen=max_history_size)
        self.performance_metrics: deque = deque(maxlen=max_history_size)
        
        # 应用指标计数器
        self.query_count = 0
        self.success_count = 0
        self.failure_count = 0
        self.response_times: deque = deque(maxlen=100)  # 保留最近100次响应时间
        
        # 启动时间
        self.start_time = datetime.now()
        
        logger.info("MetricsCollector initialized")
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            # 网络IO
            network_io = psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            
            # 进程数量
            process_count = len(psutil.pids())
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.percent,
                network_io=network_io,
                process_count=process_count
            )
            
            self.system_metrics_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_available_mb=0.0,
                disk_usage_percent=0.0
            )
    
    def collect_application_metrics(self, coordinator=None) -> ApplicationMetrics:
        """收集应用指标"""
        try:
            # 从协调器获取指标
            active_sessions = 0
            active_tasks = 0
            pending_tasks = 0
            
            if coordinator:
                system_status = coordinator.get_system_status()
                active_sessions = system_status.get("active_sessions", 0)
                active_tasks = system_status.get("active_tasks", 0)
                pending_tasks = system_status.get("pending_tasks", 0)
            
            # 计算平均响应时间
            avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0.0
            
            metrics = ApplicationMetrics(
                timestamp=datetime.now(),
                active_sessions=active_sessions,
                total_queries=self.query_count,
                successful_queries=self.success_count,
                failed_queries=self.failure_count,
                average_response_time=avg_response_time,
                active_tasks=active_tasks,
                pending_tasks=pending_tasks,
                cache_hit_rate=0.0  # 需要从缓存系统获取
            )
            
            self.app_metrics_history.append(metrics)
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to collect application metrics: {e}")
            return ApplicationMetrics(timestamp=datetime.now())
    
    def record_query(self, success: bool, response_time: float):
        """记录查询指标"""
        self.query_count += 1
        if success:
            self.success_count += 1
        else:
            self.failure_count += 1
        
        self.response_times.append(response_time)
    
    def record_performance_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        self.performance_metrics.append(metric)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            current_time = datetime.now()
            uptime = (current_time - self.start_time).total_seconds()
            
            # 最新的系统指标
            latest_system = self.system_metrics_history[-1] if self.system_metrics_history else None
            latest_app = self.app_metrics_history[-1] if self.app_metrics_history else None
            
            return {
                "uptime_seconds": uptime,
                "system_metrics": latest_system.to_dict() if latest_system else None,
                "application_metrics": latest_app.to_dict() if latest_app else None,
                "metrics_history_size": {
                    "system": len(self.system_metrics_history),
                    "application": len(self.app_metrics_history),
                    "performance": len(self.performance_metrics)
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get metrics summary: {e}")
            return {"error": str(e)}
    
    def get_historical_metrics(self, hours: int = 1) -> Dict[str, List[Dict[str, Any]]]:
        """获取历史指标"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # 过滤历史数据
            system_history = [
                m.to_dict() for m in self.system_metrics_history
                if m.timestamp >= cutoff_time
            ]
            
            app_history = [
                m.to_dict() for m in self.app_metrics_history
                if m.timestamp >= cutoff_time
            ]
            
            performance_history = [
                m.to_dict() for m in self.performance_metrics
                if m.timestamp >= cutoff_time
            ]
            
            return {
                "system_metrics": system_history,
                "application_metrics": app_history,
                "performance_metrics": performance_history
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical metrics: {e}")
            return {"error": str(e)}


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        """初始化健康检查器"""
        self.health_checks: Dict[str, Callable] = {}
        self.last_check_results: Dict[str, Dict[str, Any]] = {}
        
        # 注册默认健康检查
        self.register_check("system_resources", self._check_system_resources)
        
        logger.info("HealthChecker initialized")
    
    def register_check(self, name: str, check_func: Callable):
        """注册健康检查"""
        self.health_checks[name] = check_func
        logger.info(f"Health check registered: {name}")
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_status = "healthy"
        
        for name, check_func in self.health_checks.items():
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()
                
                results[name] = result
                self.last_check_results[name] = result
                
                # 更新整体状态
                if result.get("status") != "healthy":
                    overall_status = "degraded"
                    
            except Exception as e:
                error_result = {
                    "status": "unhealthy",
                    "message": f"Health check failed: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
                results[name] = error_result
                self.last_check_results[name] = error_result
                overall_status = "unhealthy"
        
        return {
            "overall_status": overall_status,
            "checks": results,
            "timestamp": datetime.now().isoformat()
        }
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存检查
            memory = psutil.virtual_memory()
            
            # 磁盘检查
            disk = psutil.disk_usage('/')
            
            # 判断健康状态
            status = "healthy"
            issues = []
            
            if cpu_percent > 90:
                status = "degraded"
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory.percent > 90:
                status = "degraded"
                issues.append(f"High memory usage: {memory.percent:.1f}%")
            
            if disk.percent > 90:
                status = "degraded"
                issues.append(f"High disk usage: {disk.percent:.1f}%")
            
            return {
                "status": status,
                "message": "System resources OK" if not issues else "; ".join(issues),
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "message": f"Failed to check system resources: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        """初始化告警管理器"""
        self.alert_rules: List[Dict[str, Any]] = []
        self.active_alerts: List[Dict[str, Any]] = []
        self.alert_history: deque = deque(maxlen=1000)
        
        # 默认告警规则
        self._setup_default_rules()
        
        logger.info("AlertManager initialized")
    
    def _setup_default_rules(self):
        """设置默认告警规则"""
        self.alert_rules = [
            {
                "name": "high_cpu_usage",
                "condition": lambda metrics: metrics.get("cpu_percent", 0) > 80,
                "severity": "warning",
                "message": "CPU usage is high"
            },
            {
                "name": "high_memory_usage",
                "condition": lambda metrics: metrics.get("memory_percent", 0) > 85,
                "severity": "warning",
                "message": "Memory usage is high"
            },
            {
                "name": "high_error_rate",
                "condition": lambda metrics: (
                    metrics.get("failed_queries", 0) / max(metrics.get("total_queries", 1), 1) > 0.1
                ),
                "severity": "critical",
                "message": "Error rate is too high"
            },
            {
                "name": "slow_response_time",
                "condition": lambda metrics: metrics.get("average_response_time", 0) > 10.0,
                "severity": "warning",
                "message": "Response time is slow"
            }
        ]
    
    def check_alerts(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """检查告警条件"""
        try:
            # 合并指标
            all_metrics = {**system_metrics.to_dict(), **app_metrics.to_dict()}
            
            for rule in self.alert_rules:
                try:
                    if rule["condition"](all_metrics):
                        self._trigger_alert(rule, all_metrics)
                    else:
                        self._resolve_alert(rule["name"])
                        
                except Exception as e:
                    logger.error(f"Failed to evaluate alert rule {rule['name']}: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to check alerts: {e}")
    
    def _trigger_alert(self, rule: Dict[str, Any], metrics: Dict[str, Any]):
        """触发告警"""
        alert_id = rule["name"]
        
        # 检查是否已经是活跃告警
        if any(alert["id"] == alert_id for alert in self.active_alerts):
            return
        
        alert = {
            "id": alert_id,
            "name": rule["name"],
            "severity": rule["severity"],
            "message": rule["message"],
            "triggered_at": datetime.now().isoformat(),
            "metrics": metrics
        }
        
        self.active_alerts.append(alert)
        self.alert_history.append({**alert, "action": "triggered"})
        
        logger.warning(f"Alert triggered: {rule['name']} - {rule['message']}")
    
    def _resolve_alert(self, alert_id: str):
        """解决告警"""
        for i, alert in enumerate(self.active_alerts):
            if alert["id"] == alert_id:
                resolved_alert = self.active_alerts.pop(i)
                resolved_alert["resolved_at"] = datetime.now().isoformat()
                self.alert_history.append({**resolved_alert, "action": "resolved"})
                
                logger.info(f"Alert resolved: {alert_id}")
                break
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        return self.active_alerts.copy()
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            alert for alert in self.alert_history
            if datetime.fromisoformat(alert["triggered_at"]) >= cutoff_time
        ]


class MonitoringSystem:
    """监控系统"""
    
    def __init__(self):
        """初始化监控系统"""
        self.metrics_collector = MetricsCollector()
        self.health_checker = HealthChecker()
        self.alert_manager = AlertManager()
        
        # 监控配置
        self.monitoring_interval = 60  # 监控间隔（秒）
        self.monitoring_task = None
        self.is_monitoring = False
        
        logger.info("MonitoringSystem initialized")
    
    async def start_monitoring(self, coordinator=None):
        """启动监控"""
        if self.is_monitoring:
            logger.warning("Monitoring is already running")
            return
        
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop(coordinator))
        logger.info("Monitoring started")
    
    async def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Monitoring stopped")
    
    async def _monitoring_loop(self, coordinator=None):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集指标
                system_metrics = self.metrics_collector.collect_system_metrics()
                app_metrics = self.metrics_collector.collect_application_metrics(coordinator)
                
                # 检查告警
                self.alert_manager.check_alerts(system_metrics, app_metrics)
                
                # 记录性能指标
                self.metrics_collector.record_performance_metric(
                    "monitoring_cycle_completed", 1.0, {"component": "monitoring_system"}
                )
                
                # 等待下一个监控周期
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "monitoring_interval": self.monitoring_interval,
            "metrics_summary": self.metrics_collector.get_metrics_summary(),
            "active_alerts": self.alert_manager.get_active_alerts(),
            "last_health_check": self.health_checker.last_check_results
        }
    
    async def get_comprehensive_status(self) -> Dict[str, Any]:
        """获取综合状态"""
        try:
            # 运行健康检查
            health_results = await self.health_checker.run_all_checks()
            
            # 获取指标摘要
            metrics_summary = self.metrics_collector.get_metrics_summary()
            
            # 获取告警信息
            active_alerts = self.alert_manager.get_active_alerts()
            
            return {
                "monitoring_status": self.get_monitoring_status(),
                "health_check": health_results,
                "metrics": metrics_summary,
                "alerts": {
                    "active_count": len(active_alerts),
                    "active_alerts": active_alerts
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive status: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


# 全局监控系统实例
_monitoring_system_instance: Optional[MonitoringSystem] = None


def get_monitoring_system() -> MonitoringSystem:
    """获取全局监控系统实例"""
    global _monitoring_system_instance
    
    if _monitoring_system_instance is None:
        _monitoring_system_instance = MonitoringSystem()
    
    return _monitoring_system_instance


async def start_monitoring(coordinator=None):
    """启动监控的便捷函数"""
    monitoring_system = get_monitoring_system()
    await monitoring_system.start_monitoring(coordinator)


async def stop_monitoring():
    """停止监控的便捷函数"""
    monitoring_system = get_monitoring_system()
    await monitoring_system.stop_monitoring()