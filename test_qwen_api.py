#!/usr/bin/env python3
"""
测试千问API连接和调用
"""

import asyncio
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_qwen_api():
    """测试千问API"""
    try:
        from core.config import get_config
        from core.qwen_text_generator import QwenTextGenerator
        
        print("🔍 测试千问API连接...")
        
        # 获取配置
        config = get_config()
        print(f"✅ 配置加载成功")
        print(f"API Key: {config.qwen.api_key[:10]}...{config.qwen.api_key[-10:]}")
        print(f"API Base: {config.qwen.api_base}")
        print(f"Model: {config.qwen.model}")
        
        # 创建文本生成器
        generator = QwenTextGenerator()
        print("✅ 文本生成器创建成功")
        
        # 测试简单的文本生成
        test_prompt = "请生成一个简单的SQL查询，查询用户表中的所有记录"
        print(f"\n🚀 测试SQL生成...")
        print(f"提示词: {test_prompt}")
        
        result = await generator.generate_sql(test_prompt)
        
        if result:
            print(f"✅ SQL生成成功:")
            print(f"结果: {result}")
        else:
            print("❌ SQL生成失败")
        
        # 测试意图分类
        print(f"\n🧠 测试意图分类...")
        intent_test = "今天涨粉最多的前10个达人是谁？"
        print(f"测试输入: {intent_test}")
        
        intent_result = await generator.classify_intent(intent_test)
        print(f"✅ 意图分类结果: {intent_result}")
        
        # 关闭客户端
        await generator.close()
        print("\n🎉 千问API测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_qwen_api())