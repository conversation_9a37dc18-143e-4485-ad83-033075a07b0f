"""展示Agent提示词"""

from typing import Dict, Any, List
from .base_prompts import BasePrompts


class DisplayPrompts(BasePrompts):
    """展示Agent提示词管理"""
    
    # 数据分析系统提示词
    DATA_ANALYSIS_SYSTEM = """你是一个专业的数据分析师，专门分析TikTok相关数据并提供有价值的洞察。

你的任务：
1. 分析查询结果数据
2. 提取关键指标和趋势
3. 生成业务洞察和建议
4. 创建合适的数据可视化

分析原则：
- 基于数据事实，避免主观臆断
- 关注业务价值和实用性
- 提供可操作的建议
- 考虑数据的局限性

TikTok业务背景：
- 关注用户增长和留存
- 重视内容质量和互动
- 分析不同内容类型的表现
- 监控达人和创作者生态

当前时间：{current_time}
"""

    # 数据分析用户提示词
    DATA_ANALYSIS_USER = """请分析以下数据：

原始问题：{original_question}
SQL查询：{sql}
查询结果：{query_results}

请提供全面的数据分析，包括：

1. **数据摘要** - 简要描述数据概况
2. **关键指标** - 提取重要的数值指标
3. **趋势洞察** - 发现数据中的趋势和模式
4. **业务建议** - 基于数据提供可操作的建议

请以JSON格式返回：
{
    "summary": "数据摘要",
    "key_metrics": {
        "指标名1": "数值1",
        "指标名2": "数值2"
    },
    "insights": [
        "洞察1：具体的发现和解释",
        "洞察2：趋势分析",
        "洞察3：异常或特殊情况"
    ],
    "recommendations": [
        "建议1：具体的行动建议",
        "建议2：优化策略",
        "建议3：风险提醒"
    ],
    "data_quality": {
        "completeness": "数据完整性评估",
        "reliability": "数据可靠性说明",
        "limitations": "数据局限性"
    }
}
"""

    # 可视化生成提示词
    VISUALIZATION_GENERATION = """基于以下数据生成合适的可视化方案：

数据：{data}
原始问题：{question}
数据类型：{data_type}

请选择最合适的图表类型并生成可视化代码：

图表类型选择指南：
- **柱状图(bar)**: 分类数据比较、排行榜
- **折线图(line)**: 时间序列、趋势变化
- **饼图(pie)**: 占比分布、构成分析
- **散点图(scatter)**: 相关性分析、分布情况
- **热力图(heatmap)**: 矩阵数据、相关性矩阵
- **箱线图(box)**: 数据分布、异常值检测

请以JSON格式返回：
{
    "visualizations": [
        {
            "type": "图表类型",
            "title": "图表标题",
            "description": "图表描述",
            "code": "Python可视化代码(使用plotly)",
            "insights": "图表洞察"
        }
    ],
    "recommended_charts": ["推荐的其他图表类型"],
    "data_preparation_notes": "数据准备说明"
}

代码要求：
1. 使用plotly库
2. 代码可直接执行
3. 包含适当的标题和标签
4. 考虑颜色和样式美观性
"""

    # 报告生成提示词
    REPORT_GENERATION = """生成完整的数据分析报告：

分析内容：
- 原始问题：{question}
- 数据分析：{analysis}
- 可视化：{visualizations}

请生成一份专业的分析报告，包括：

1. **执行摘要** - 核心发现和结论
2. **详细分析** - 深入的数据解读
3. **可视化说明** - 图表解释和洞察
4. **行动建议** - 具体的业务建议
5. **附录** - 技术细节和数据说明

报告格式要求：
- 结构清晰，逻辑性强
- 语言专业但易懂
- 突出关键信息
- 提供可操作的建议

请以JSON格式返回：
{
    "executive_summary": "执行摘要",
    "detailed_analysis": "详细分析内容",
    "visualization_insights": "可视化洞察",
    "recommendations": {
        "immediate_actions": ["立即行动建议"],
        "long_term_strategies": ["长期策略建议"],
        "monitoring_metrics": ["需要监控的指标"]
    },
    "appendix": {
        "methodology": "分析方法说明",
        "data_sources": "数据来源",
        "limitations": "分析局限性"
    }
}
"""

    # 趋势分析提示词
    TREND_ANALYSIS = """分析以下数据的趋势特征：

时间序列数据：{time_series_data}
分析维度：{dimensions}

请识别以下趋势模式：
1. **整体趋势** - 上升、下降、平稳
2. **周期性** - 是否存在周期性变化
3. **季节性** - 季节性影响因素
4. **异常点** - 异常值和特殊事件
5. **拐点** - 趋势转折点

返回格式：
{
    "overall_trend": "整体趋势描述",
    "trend_strength": "趋势强度(强/中/弱)",
    "cyclical_patterns": ["周期性模式描述"],
    "seasonal_effects": ["季节性影响"],
    "anomalies": [
        {
            "date": "异常时间点",
            "value": "异常值",
            "possible_cause": "可能原因"
        }
    ],
    "turning_points": [
        {
            "date": "转折点时间",
            "description": "转折点描述"
        }
    ],
    "forecast_insights": "未来趋势预测"
}
"""

    @staticmethod
    def get_data_analysis_prompt(original_question: str, sql: str, query_results: List[Dict]) -> Dict[str, str]:
        """获取数据分析提示词"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        system_prompt = DisplayPrompts.DATA_ANALYSIS_SYSTEM.format(current_time=current_time)
        
        user_prompt = DisplayPrompts.DATA_ANALYSIS_USER.format(
            original_question=original_question,
            sql=sql,
            query_results=str(query_results)[:2000]  # 限制长度
        )
        
        return {
            "system": system_prompt,
            "user": user_prompt
        }
    
    @staticmethod
    def get_visualization_prompt(data: List[Dict], question: str, data_type: str = "general") -> str:
        """获取可视化生成提示词"""
        return DisplayPrompts.VISUALIZATION_GENERATION.format(
            data=str(data)[:1000],  # 限制长度
            question=question,
            data_type=data_type
        )
    
    @staticmethod
    def get_report_prompt(question: str, analysis: Dict, visualizations: List[Dict]) -> str:
        """获取报告生成提示词"""
        return DisplayPrompts.REPORT_GENERATION.format(
            question=question,
            analysis=str(analysis),
            visualizations=str(visualizations)
        )
    
    @staticmethod
    def get_trend_analysis_prompt(time_series_data: List[Dict], dimensions: List[str]) -> str:
        """获取趋势分析提示词"""
        return DisplayPrompts.TREND_ANALYSIS.format(
            time_series_data=str(time_series_data),
            dimensions=str(dimensions)
        )