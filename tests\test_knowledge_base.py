"""
知识库管理器测试
"""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from uuid import uuid4

from src.core.knowledge_base import KnowledgeBaseManager
from src.core.config import load_config


class TestKnowledgeBaseManager:
    """知识库管理器测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """创建临时数据库路径"""
        temp_dir = tempfile.mkdtemp()
        db_path = Path(temp_dir) / "test_knowledge_base.db"
        yield str(db_path)
        # 在Windows上，需要等待一下让SQLite释放文件锁
        import time
        time.sleep(0.1)
        try:
            shutil.rmtree(temp_dir)
        except PermissionError:
            # 在Windows上忽略清理错误
            pass
    
    @pytest.fixture
    def kb_manager(self, temp_db_path):
        """创建知识库管理器实例"""
        # 加载配置
        try:
            load_config()
        except:
            # 如果配置加载失败，创建一个最小配置
            import os
            os.environ.setdefault("QWEN_API_KEY", "test_key")
            load_config()
        
        manager = KnowledgeBaseManager(db_path=temp_db_path)
        yield manager
        manager.close()
    
    def test_init_database(self, kb_manager):
        """测试数据库初始化"""
        # 数据库文件应该存在
        assert kb_manager.db_path.exists()
        
        # 检查表是否创建
        import sqlite3
        with sqlite3.connect(kb_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            assert "schema_info" in tables
            assert "business_docs" in tables
            assert "sql_examples" in tables
    
    @pytest.mark.asyncio
    async def test_add_schema(self, kb_manager):
        """测试添加Schema"""
        # 添加Schema
        result = await kb_manager.add_schema(
            table_name="test_table",
            ddl="CREATE TABLE test_table (id INT PRIMARY KEY)",
            description="测试表"
        )
        assert result is True
        
        # 获取所有Schema
        schemas = await kb_manager.get_all_schemas()
        assert len(schemas) == 1
        assert schemas[0].table_name == "test_table"
        assert schemas[0].ddl == "CREATE TABLE test_table (id INT PRIMARY KEY)"
        assert schemas[0].description == "测试表"
    
    @pytest.mark.asyncio
    async def test_add_duplicate_schema(self, kb_manager):
        """测试添加重复Schema"""
        # 添加第一个Schema
        result1 = await kb_manager.add_schema(
            table_name="test_table",
            ddl="CREATE TABLE test_table (id INT PRIMARY KEY)"
        )
        assert result1 is True
        
        # 添加相同表名的Schema（应该更新）
        result2 = await kb_manager.add_schema(
            table_name="test_table",
            ddl="CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(100))",
            description="更新的测试表"
        )
        assert result2 is True
        
        # 应该只有一条记录
        schemas = await kb_manager.get_all_schemas()
        assert len(schemas) == 1
        assert "name VARCHAR(100)" in schemas[0].ddl
        assert schemas[0].description == "更新的测试表"
    
    @pytest.mark.asyncio
    async def test_add_business_doc(self, kb_manager):
        """测试添加业务文档"""
        result = await kb_manager.add_business_doc(
            title="涨粉量计算方式",
            content="涨粉量 = 当前粉丝数 - 前一天粉丝数",
            category="business_rules"
        )
        assert result is True
        
        # 获取所有文档
        docs = await kb_manager.get_all_business_docs()
        assert len(docs) == 1
        assert docs[0].title == "涨粉量计算方式"
        assert docs[0].content == "涨粉量 = 当前粉丝数 - 前一天粉丝数"
        assert docs[0].category == "business_rules"
    
    @pytest.mark.asyncio
    async def test_add_sql_example(self, kb_manager):
        """测试添加SQL范例"""
        result = await kb_manager.add_sql_example(
            question="过去一周哪个游戏达人播放量最高？",
            sql="SELECT c.username, SUM(v.view_count) FROM creators c JOIN videos v ON c.id = v.creator_id WHERE c.category = 'gaming' AND v.created_at >= NOW() - INTERVAL '7 days' GROUP BY c.id ORDER BY SUM(v.view_count) DESC LIMIT 1",
            explanation="查询游戏分类达人的总播放量",
            difficulty="medium"
        )
        assert result is True
        
        # 获取所有SQL范例
        examples = await kb_manager.get_all_sql_examples()
        assert len(examples) == 1
        assert examples[0].question == "过去一周哪个游戏达人播放量最高？"
        assert "gaming" in examples[0].sql
        assert examples[0].explanation == "查询游戏分类达人的总播放量"
        assert examples[0].difficulty == "medium"
    
    @pytest.mark.asyncio
    async def test_search_schemas(self, kb_manager):
        """测试搜索Schema"""
        # 添加测试数据
        await kb_manager.add_schema("users", "CREATE TABLE users (id INT)", "用户表")
        await kb_manager.add_schema("videos", "CREATE TABLE videos (id INT)", "视频表")
        await kb_manager.add_schema("creators", "CREATE TABLE creators (id INT)", "达人表")
        
        # 搜索包含"user"的Schema
        results = await kb_manager.search_schemas("user")
        assert len(results) == 1
        assert results[0].table_name == "users"
        
        # 搜索包含"视频"的Schema
        results = await kb_manager.search_schemas("视频")
        assert len(results) == 1
        assert results[0].table_name == "videos"
    
    @pytest.mark.asyncio
    async def test_search_business_docs(self, kb_manager):
        """测试搜索业务文档"""
        # 添加测试数据
        await kb_manager.add_business_doc("涨粉量计算", "涨粉量计算方式", "business_rules")
        await kb_manager.add_business_doc("播放量统计", "播放量统计规则", "business_rules")
        await kb_manager.add_business_doc("用户指南", "系统使用指南", "guide")
        
        # 搜索包含"涨粉"的文档
        results = await kb_manager.search_business_docs("涨粉")
        assert len(results) == 1
        assert results[0].title == "涨粉量计算"
        
        # 按分类搜索
        results = await kb_manager.search_business_docs("", category="business_rules")
        assert len(results) == 2
        
        results = await kb_manager.search_business_docs("", category="guide")
        assert len(results) == 1
        assert results[0].title == "用户指南"
    
    @pytest.mark.asyncio
    async def test_update_embedding(self, kb_manager):
        """测试更新嵌入向量"""
        # 添加Schema
        await kb_manager.add_schema("test_table", "CREATE TABLE test_table (id INT)")
        schemas = await kb_manager.get_all_schemas()
        schema_id = schemas[0].id
        
        # 更新嵌入向量
        test_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        result = await kb_manager.update_embedding("schema", schema_id, test_embedding)
        assert result is True
        
        # 验证嵌入向量已更新
        updated_schemas = await kb_manager.get_all_schemas()
        assert updated_schemas[0].embedding == test_embedding
    
    @pytest.mark.asyncio
    async def test_get_stats(self, kb_manager):
        """测试获取统计信息"""
        # 添加测试数据
        await kb_manager.add_schema("table1", "CREATE TABLE table1 (id INT)")
        await kb_manager.add_schema("table2", "CREATE TABLE table2 (id INT)")
        await kb_manager.add_business_doc("doc1", "content1")
        await kb_manager.add_sql_example("question1", "SELECT * FROM table1")
        
        # 获取统计信息
        stats = await kb_manager.get_stats()
        
        assert stats["total_items"] == 4
        assert stats["schemas"]["total"] == 2
        assert stats["business_docs"]["total"] == 1
        assert stats["sql_examples"]["total"] == 1
        
        # 所有项目都没有嵌入向量
        assert stats["schemas"]["with_embedding"] == 0
        assert stats["business_docs"]["with_embedding"] == 0
        assert stats["sql_examples"]["with_embedding"] == 0
        
        # 添加嵌入向量
        schemas = await kb_manager.get_all_schemas()
        await kb_manager.update_embedding("schema", schemas[0].id, [0.1, 0.2])
        
        # 重新获取统计信息
        stats = await kb_manager.get_stats()
        assert stats["schemas"]["with_embedding"] == 1
        assert stats["embedding_coverage"]["schemas"] == 0.5  # 1/2
    
    @pytest.mark.asyncio
    async def test_clear_all(self, kb_manager):
        """测试清空所有数据"""
        # 添加测试数据
        await kb_manager.add_schema("table1", "CREATE TABLE table1 (id INT)")
        await kb_manager.add_business_doc("doc1", "content1")
        await kb_manager.add_sql_example("question1", "SELECT * FROM table1")
        
        # 验证数据存在
        stats = await kb_manager.get_stats()
        assert stats["total_items"] == 3
        
        # 清空所有数据
        result = await kb_manager.clear_all()
        assert result is True
        
        # 验证数据已清空
        stats = await kb_manager.get_stats()
        assert stats["total_items"] == 0
        
        schemas = await kb_manager.get_all_schemas()
        docs = await kb_manager.get_all_business_docs()
        examples = await kb_manager.get_all_sql_examples()
        
        assert len(schemas) == 0
        assert len(docs) == 0
        assert len(examples) == 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])