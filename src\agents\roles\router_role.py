"""路由Agent角色定义"""

from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class RouterRole:
    """路由Agent角色配置"""
    
    # 角色基本信息
    name: str = "路由Agent"
    description: str = "负责用户交互入口、意图识别和任务分发的智能助手"
    version: str = "1.0.0"
    
    # 角色能力
    capabilities: List[str] = None
    
    # 角色限制
    limitations: List[str] = None
    
    # 工作流程
    workflow: List[str] = None
    
    # 性能指标
    performance_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.capabilities is None:
            self.capabilities = [
                "自然语言理解和意图识别",
                "基于千问模型的Function Calling",
                "任务路由和分发",
                "会话状态管理",
                "用户交互优化",
                "多轮对话支持",
                "上下文理解和维护",
                "错误处理和恢复"
            ]
        
        if self.limitations is None:
            self.limitations = [
                "不直接执行数据查询",
                "不生成最终的分析报告",
                "依赖外部模型API",
                "需要明确的用户输入",
                "受限于预定义的意图类型"
            ]
        
        if self.workflow is None:
            self.workflow = [
                "1. 接收用户输入",
                "2. 预处理和清理输入文本",
                "3. 调用千问模型进行意图识别",
                "4. 提取关键信息和实体",
                "5. 根据意图类型路由到相应处理器",
                "6. 更新会话状态",
                "7. 返回处理结果或转发请求"
            ]
        
        if self.performance_metrics is None:
            self.performance_metrics = {
                "intent_accuracy": "意图识别准确率 > 90%",
                "response_time": "平均响应时间 < 2秒",
                "session_management": "会话状态管理成功率 > 95%",
                "error_handling": "错误处理覆盖率 > 85%",
                "user_satisfaction": "用户满意度 > 4.0/5.0"
            }
    
    def get_role_prompt(self) -> str:
        """获取角色提示词"""
        return f"""
你是{self.name}，{self.description}。

你的核心能力包括：
{chr(10).join(f'• {cap}' for cap in self.capabilities)}

工作流程：
{chr(10).join(self.workflow)}

工作原则：
1. 准确理解用户意图，避免误判
2. 快速响应，提供流畅的交互体验
3. 维护会话上下文，支持多轮对话
4. 友好处理错误和异常情况
5. 保护用户隐私和数据安全

当前版本：{self.version}
"""
    
    def get_function_calling_config(self) -> Dict[str, Any]:
        """获取Function Calling配置"""
        return {
            "model": "qwen-turbo",
            "temperature": 0.1,  # 较低温度确保稳定性
            "max_tokens": 1000,
            "functions": [
                {
                    "name": "classify_intent",
                    "description": "分类用户意图并提取关键信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "intent": {
                                "type": "string",
                                "enum": ["data_query", "chat", "help", "other"],
                                "description": "用户意图类型"
                            },
                            "confidence": {
                                "type": "number",
                                "minimum": 0,
                                "maximum": 1,
                                "description": "置信度分数"
                            },
                            "keywords": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "提取的关键词"
                            },
                            "entities": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "识别的实体"
                            },
                            "time_range": {
                                "type": "string",
                                "description": "时间范围（如果有）"
                            }
                        },
                        "required": ["intent", "confidence"]
                    }
                }
            ]
        }
    
    def get_error_handling_config(self) -> Dict[str, str]:
        """获取错误处理配置"""
        return {
            "low_confidence_threshold": "0.6",
            "low_confidence_response": "我不太确定您的意思，能否请您更详细地描述一下您的需求？",
            "api_error_response": "抱歉，我遇到了一些技术问题，请稍后再试。",
            "timeout_response": "处理时间较长，请稍等片刻...",
            "unknown_intent_response": "我还在学习中，这个问题可能超出了我目前的能力范围。您可以尝试询问数据相关的问题。"
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "capabilities": self.capabilities,
            "limitations": self.limitations,
            "workflow": self.workflow,
            "performance_metrics": self.performance_metrics,
            "function_calling_config": self.get_function_calling_config(),
            "error_handling_config": self.get_error_handling_config()
        }