"""
配置验证工具
提供配置项的验证和检查功能
"""

import re
import socket
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)


class ConfigValidator:
    """配置验证器"""

    @staticmethod
    def validate_api_key(api_key: str, min_length: int = 8) -> bool:
        """验证API密钥格式"""
        if not api_key or len(api_key) < min_length:
            return False
        # 允许测试API密钥
        if api_key.startswith("test_") or api_key.endswith("_test_key"):
            return True
        # 基本格式检查：不能包含空格，应该是字母数字和常见符号组合
        return bool(re.match(r"^[a-zA-Z0-9_.-]+$", api_key))

    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    @staticmethod
    def validate_port(port: int) -> bool:
        """验证端口号"""
        return 1 <= port <= 65535

    @staticmethod
    def validate_host(host: str) -> bool:
        """验证主机地址"""
        if not host:
            return False

        # 允许测试主机名
        if host.startswith("test_") or host.endswith("_host"):
            return True

        # 检查是否是有效的IP地址或域名
        try:
            socket.gethostbyname(host)
            return True
        except socket.gaierror:
            # 如果不是有效的域名，检查是否是localhost或有效的IP格式
            if host in ["localhost", "127.0.0.1", "0.0.0.0"]:
                return True

            # 简单的IP地址格式检查
            ip_pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
            if re.match(ip_pattern, host):
                parts = host.split(".")
                return all(0 <= int(part) <= 255 for part in parts)

            return False

    @staticmethod
    def validate_database_connection(connection_string: str) -> bool:
        """验证数据库连接字符串"""
        if not connection_string:
            return False

        # 基本的PostgreSQL连接字符串格式检查
        postgres_pattern = r"^postgresql://[^:]+:[^@]*@[^:/]+:\d+/[^/]+$"
        return bool(re.match(postgres_pattern, connection_string))

    @staticmethod
    def validate_log_level(log_level: str) -> bool:
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        return log_level.upper() in valid_levels

    @staticmethod
    def validate_model_name(model_name: str) -> bool:
        """验证模型名称"""
        if not model_name:
            return False

        # 千问模型名称格式检查
        qwen_models = [
            "qwen-turbo",
            "qwen-plus",
            "qwen-max",
            "qwen-7b-chat",
            "qwen-14b-chat",
            "qwen-72b-chat",
        ]
        return model_name in qwen_models

    @classmethod
    def validate_config_dict(cls, config_dict: Dict[str, Any]) -> List[str]:
        """验证配置字典，返回错误列表"""
        errors = []

        # 验证千问配置
        if "qwen" in config_dict:
            qwen_config = config_dict["qwen"]

            if not cls.validate_api_key(qwen_config.get("api_key", "")):
                errors.append("Invalid QWEN_API_KEY format")

            if not cls.validate_url(qwen_config.get("api_base", "")):
                errors.append("Invalid QWEN_API_BASE URL")

            if not cls.validate_model_name(qwen_config.get("model", "")):
                errors.append("Invalid QWEN_MODEL name")

        # 验证数据库配置
        if "database" in config_dict:
            db_config = config_dict["database"]

            if not cls.validate_host(db_config.get("host", "")):
                errors.append("Invalid database host")

            if not cls.validate_port(db_config.get("port", 0)):
                errors.append("Invalid database port")

            connection_string = db_config.get("connection_string")
            if connection_string and not cls.validate_database_connection(
                connection_string
            ):
                errors.append("Invalid database connection string format")

        # 验证Redis配置
        if "redis" in config_dict:
            redis_config = config_dict["redis"]

            if not cls.validate_host(redis_config.get("host", "")):
                errors.append("Invalid Redis host")

            if not cls.validate_port(redis_config.get("port", 0)):
                errors.append("Invalid Redis port")

        # 验证应用配置
        if "app" in config_dict:
            app_config = config_dict["app"]

            if not cls.validate_log_level(app_config.get("log_level", "")):
                errors.append("Invalid log level")

            if not cls.validate_port(app_config.get("port", 0)):
                errors.append("Invalid application port")

        return errors


class ConfigHealthChecker:
    """配置健康检查器"""

    @staticmethod
    def check_database_connectivity(db_config) -> bool:
        """检查数据库连接性"""
        try:
            import psycopg2

            conn = psycopg2.connect(
                host=db_config.host,
                port=db_config.port,
                database=db_config.name,
                user=db_config.user,
                password=db_config.password,
                connect_timeout=5,
            )
            conn.close()
            logger.info("Database connectivity check passed")
            return True
        except Exception as e:
            logger.error(f"Database connectivity check failed: {e}")
            return False

    @staticmethod
    def check_redis_connectivity(redis_config) -> bool:
        """检查Redis连接性"""
        try:
            import redis

            r = redis.Redis(
                host=redis_config.host,
                port=redis_config.port,
                db=redis_config.db,
                password=redis_config.password,
                socket_timeout=redis_config.timeout,
            )
            r.ping()
            logger.info("Redis connectivity check passed")
            return True
        except Exception as e:
            logger.error(f"Redis connectivity check failed: {e}")
            return False

    @staticmethod
    def check_qwen_api_connectivity(qwen_config) -> bool:
        """检查千问API连接性"""
        try:
            import httpx

            headers = {
                "Authorization": f"Bearer {qwen_config.api_key}",
                "Content-Type": "application/json",
            }

            # 简单的API健康检查
            with httpx.Client(timeout=qwen_config.timeout) as client:
                response = client.get(f"{qwen_config.api_base}/models", headers=headers)
                if response.status_code == 200:
                    logger.info("Qwen API connectivity check passed")
                    return True
                else:
                    logger.warning(f"Qwen API returned status {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"Qwen API connectivity check failed: {e}")
            return False

    @classmethod
    def run_health_checks(cls, config) -> Dict[str, bool]:
        """运行所有健康检查"""
        results = {}

        try:
            results["database"] = cls.check_database_connectivity(config.database)
        except ImportError:
            logger.warning("psycopg2 not available, skipping database check")
            results["database"] = None

        try:
            results["redis"] = cls.check_redis_connectivity(config.redis)
        except ImportError:
            logger.warning("redis not available, skipping Redis check")
            results["redis"] = None

        try:
            results["qwen_api"] = cls.check_qwen_api_connectivity(config.qwen)
        except ImportError:
            logger.warning("httpx not available, skipping Qwen API check")
            results["qwen_api"] = None

        return results
