#!/usr/bin/env python3
"""简单的API启动脚本"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动TikTok AI Agent API服务")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/api/v1/health")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        uvicorn.run(
            "api.main:app",
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 解决建议:")
        print("1. 检查 .env 文件是否存在")
        print("2. 确保端口8000未被占用")
        print("3. 检查Python依赖是否完整安装")