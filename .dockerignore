# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Test coverage
.coverage
htmlcov/
.pytest_cache/

# Documentation
docs/_build/

# Temporary files
*.tmp
*.temp

# Local data (keep structure but ignore content)
data/*.db
data/*.json
!data/sample_training_data.json

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml

# Environment files (will be mounted)
.env
.env.local
.env.production

# Kiro IDE
.kiro/