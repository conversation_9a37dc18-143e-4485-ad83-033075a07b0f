"""数据模型测试"""

import sys
from pathlib import Path
from datetime import datetime
from uuid import uuid4

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from models import (
    UserQuery, SQLResult, QueryResult, AnalysisResult,
    SchemaInfo, BusinessDoc, SQLExample,
    QueryRequest, QueryResponse, IntentType, QueryStatus
)


class TestQueryModels:
    """查询模型测试"""
    
    def test_user_query_creation(self):
        """测试用户查询创建"""
        query = UserQuery(
            user_id="test_user",
            question="测试问题",
            intent=IntentType.DATA_QUERY
        )
        
        assert query.user_id == "test_user"
        assert query.question == "测试问题"
        assert query.intent == IntentType.DATA_QUERY
        assert query.status == QueryStatus.PENDING
        assert query.id is not None
        assert query.created_at is not None
    
    def test_sql_result_creation(self):
        """测试SQL结果创建"""
        result = SQLResult(
            sql="SELECT * FROM test",
            confidence=0.95,
            context_used=["schema1", "doc1"],
            generation_time=1.5
        )
        
        assert result.sql == "SELECT * FROM test"
        assert result.confidence == 0.95
        assert result.context_used == ["schema1", "doc1"]
        assert result.generation_time == 1.5
    
    def test_query_result_creation(self):
        """测试查询结果创建"""
        result = QueryResult(
            data=[{"name": "test", "value": 100}],
            columns=["name", "value"],
            row_count=1,
            execution_time=0.5
        )
        
        assert len(result.data) == 1
        assert result.columns == ["name", "value"]
        assert result.row_count == 1
        assert result.execution_time == 0.5


class TestKnowledgeModels:
    """知识库模型测试"""
    
    def test_schema_info_creation(self):
        """测试Schema信息创建"""
        schema = SchemaInfo(
            table_name="test_table",
            ddl="CREATE TABLE test_table (id INT PRIMARY KEY)",
            description="测试表"
        )
        
        assert schema.table_name == "test_table"
        assert "CREATE TABLE" in schema.ddl
        assert schema.description == "测试表"
        assert schema.id is not None
    
    def test_business_doc_creation(self):
        """测试业务文档创建"""
        doc = BusinessDoc(
            title="测试文档",
            content="这是测试内容",
            category="test"
        )
        
        assert doc.title == "测试文档"
        assert doc.content == "这是测试内容"
        assert doc.category == "test"
    
    def test_sql_example_creation(self):
        """测试SQL范例创建"""
        example = SQLExample(
            question="测试问题",
            sql="SELECT * FROM test",
            explanation="测试解释",
            difficulty="easy"
        )
        
        assert example.question == "测试问题"
        assert example.sql == "SELECT * FROM test"
        assert example.explanation == "测试解释"
        assert example.difficulty == "easy"


class TestAPIModels:
    """API模型测试"""
    
    def test_query_request_creation(self):
        """测试查询请求创建"""
        request = QueryRequest(
            question="测试问题",
            user_id="test_user",
            session_id="test_session"
        )
        
        assert request.question == "测试问题"
        assert request.user_id == "test_user"
        assert request.session_id == "test_session"
    
    def test_query_response_creation(self):
        """测试查询响应创建"""
        query_id = uuid4()
        response = QueryResponse(
            query_id=query_id,
            sql="SELECT * FROM test",
            data=[{"result": "test"}],
            analysis="测试分析",
            execution_time=2.0
        )
        
        assert response.query_id == query_id
        assert response.sql == "SELECT * FROM test"
        assert len(response.data) == 1
        assert response.analysis == "测试分析"
        assert response.execution_time == 2.0


class TestModelSerialization:
    """模型序列化测试"""
    
    def test_model_to_dict(self):
        """测试模型转字典"""
        query = UserQuery(
            question="测试问题",
            user_id="test_user"
        )
        
        data = query.model_dump()
        assert isinstance(data, dict)
        assert data["question"] == "测试问题"
        assert data["user_id"] == "test_user"
        assert "id" in data
        assert "created_at" in data
    
    def test_model_to_json(self):
        """测试模型转JSON"""
        query = UserQuery(
            question="测试问题",
            user_id="test_user"
        )
        
        json_str = query.model_dump_json()
        assert isinstance(json_str, str)
        assert "测试问题" in json_str
        assert "test_user" in json_str


if __name__ == "__main__":
    # 简单测试运行
    test_query = TestQueryModels()
    test_query.test_user_query_creation()
    test_query.test_sql_result_creation()
    test_query.test_query_result_creation()
    
    test_knowledge = TestKnowledgeModels()
    test_knowledge.test_schema_info_creation()
    test_knowledge.test_business_doc_creation()
    test_knowledge.test_sql_example_creation()
    
    test_api = TestAPIModels()
    test_api.test_query_request_creation()
    test_api.test_query_response_creation()
    
    test_serialization = TestModelSerialization()
    test_serialization.test_model_to_dict()
    test_serialization.test_model_to_json()
    
    print("✅ 所有数据模型测试通过！")