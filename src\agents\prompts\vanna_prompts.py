"""Vanna核心提示词"""

from typing import Dict, Any, List
from .base_prompts import BasePrompts


class VannaPrompts(BasePrompts):
    """Vanna核心提示词管理"""
    
    # SQL生成系统提示词
    SQL_GENERATION_SYSTEM = """你是一个专业的SQL生成专家，专门为TikTok数据分析生成准确的SQL查询。

数据库架构信息：
{schema_info}

业务规则：
{business_rules}

SQL范例：
{sql_examples}

生成SQL时请遵循以下原则：
1. **准确性** - 确保SQL语法正确，逻辑清晰
2. **安全性** - 避免危险操作，使用参数化查询
3. **性能** - 优化查询性能，合理使用索引
4. **业务理解** - 正确理解TikTok业务指标和概念

TikTok业务指标说明：
- 涨粉量：follower_growth，表示粉丝增长数
- 播放量：view_count，视频播放次数
- 点赞率：like_count/view_count，互动指标
- 互动率：(like_count + comment_count + share_count)/view_count
- 达人分类：category字段，包括游戏、美妆、舞蹈、音乐等

时间处理：
- "今天"：CURRENT_DATE
- "昨天"：CURRENT_DATE - INTERVAL '1 day'
- "过去一周"：>= CURRENT_DATE - INTERVAL '7 days'
- "本月"：>= DATE_TRUNC('month', CURRENT_DATE)

请根据用户问题生成准确的SQL查询。
"""

    # SQL生成用户提示词
    SQL_GENERATION_USER = """用户问题：{question}

请生成对应的SQL查询，要求：
1. SQL语法正确
2. 符合业务逻辑
3. 包含必要的注释
4. 考虑性能优化

请以JSON格式返回：
{
    "sql": "生成的SQL语句",
    "explanation": "SQL解释说明",
    "confidence": 0.95,
    "assumptions": ["假设条件1", "假设条件2"],
    "potential_issues": ["可能的问题1", "可能的问题2"]
}
"""

    # SQL优化提示词
    SQL_OPTIMIZATION = """请优化以下SQL查询：

原始SQL：
{original_sql}

优化目标：
1. 提高查询性能
2. 减少资源消耗
3. 保持结果准确性

请提供优化后的SQL和优化说明：
{
    "optimized_sql": "优化后的SQL",
    "optimizations": ["优化点1", "优化点2"],
    "performance_impact": "性能影响说明"
}
"""

    # 错误修复提示词
    SQL_ERROR_FIX = """SQL执行出现错误，请帮助修复：

原始SQL：
{sql}

错误信息：
{error_message}

数据库架构：
{schema_info}

请分析错误原因并提供修复方案：
{
    "error_analysis": "错误分析",
    "fixed_sql": "修复后的SQL",
    "explanation": "修复说明"
}
"""

    # 查询结果解释提示词
    RESULT_EXPLANATION = """请解释以下SQL查询结果：

SQL查询：
{sql}

查询结果：
{results}

用户原始问题：
{original_question}

请提供易懂的结果解释：
{
    "summary": "结果摘要",
    "key_findings": ["关键发现1", "关键发现2"],
    "business_insights": ["业务洞察1", "业务洞察2"],
    "data_quality_notes": ["数据质量说明"]
}
"""

    @staticmethod
    def get_sql_generation_prompt(question: str, schema_info: str = "", 
                                business_rules: str = "", sql_examples: str = "") -> Dict[str, str]:
        """获取SQL生成提示词"""
        system_prompt = VannaPrompts.SQL_GENERATION_SYSTEM.format(
            schema_info=schema_info or "暂无架构信息",
            business_rules=business_rules or "暂无业务规则",
            sql_examples=sql_examples or "暂无SQL范例"
        )
        
        user_prompt = VannaPrompts.SQL_GENERATION_USER.format(question=question)
        
        return {
            "system": system_prompt,
            "user": user_prompt
        }
    
    @staticmethod
    def get_sql_optimization_prompt(original_sql: str) -> str:
        """获取SQL优化提示词"""
        return VannaPrompts.SQL_OPTIMIZATION.format(original_sql=original_sql)
    
    @staticmethod
    def get_sql_error_fix_prompt(sql: str, error_message: str, schema_info: str = "") -> str:
        """获取SQL错误修复提示词"""
        return VannaPrompts.SQL_ERROR_FIX.format(
            sql=sql,
            error_message=error_message,
            schema_info=schema_info or "暂无架构信息"
        )
    
    @staticmethod
    def get_result_explanation_prompt(sql: str, results: List[Dict], original_question: str) -> str:
        """获取结果解释提示词"""
        return VannaPrompts.RESULT_EXPLANATION.format(
            sql=sql,
            results=str(results)[:1000],  # 限制长度
            original_question=original_question
        )