"""配置管理系统"""

import os
from typing import Optional
from pathlib import Path

from pydantic import BaseModel, Field
from dotenv import load_dotenv


class QwenConfig(BaseModel):
    """千问模型配置"""
    api_key: str = Field(..., description="千问API密钥")
    api_base: str = Field(default="https://dashscope.aliyuncs.com/api/v1", description="API基础URL")
    model: str = Field(default="qwen-turbo", description="模型名称")
    timeout: int = Field(default=30, description="请求超时时间")
    max_retries: int = Field(default=3, description="最大重试次数")


class DatabaseConfig(BaseModel):
    """数据库配置"""
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, description="数据库端口")
    name: str = Field(default="tiktok_data", description="数据库名称")
    user: str = Field(default="postgres", description="数据库用户")
    password: str = Field(default="", description="数据库密码")
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="最大溢出连接数")
    
    @property
    def connection_string(self) -> str:
        """获取数据库连接字符串"""
        if self.password:
            return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        else:
            return f"postgresql://{self.user}@{self.host}:{self.port}/{self.name}"


class RedisConfig(BaseModel):
    """Redis配置"""
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    db: int = Field(default=0, description="Redis数据库编号")
    password: Optional[str] = Field(default=None, description="Redis密码")
    timeout: int = Field(default=5, description="连接超时时间")
    max_connections: int = Field(default=10, description="最大连接数")


class AppConfig(BaseModel):
    """应用配置"""
    name: str = Field(default="TikTok AI Agent", description="应用名称")
    version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    log_level: str = Field(default="INFO", description="日志级别")
    host: str = Field(default="0.0.0.0", description="服务主机")
    port: int = Field(default=8000, description="服务端口")


class VannaConfig(BaseModel):
    """Vanna配置"""
    model: str = Field(default="qwen", description="Vanna使用的模型")
    db_type: str = Field(default="postgres", description="数据库类型")
    embedding_dimension: int = Field(default=1536, description="嵌入向量维度")
    max_context_length: int = Field(default=4000, description="最大上下文长度")
    similarity_threshold: float = Field(default=0.7, description="相似度阈值")


class Config(BaseModel):
    """主配置类"""
    app: AppConfig = Field(default_factory=AppConfig)
    qwen: QwenConfig
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    redis: RedisConfig = Field(default_factory=RedisConfig)
    vanna: VannaConfig = Field(default_factory=VannaConfig)
    
    # 其他配置
    init_knowledge_base: bool = Field(default=True, description="是否初始化知识库")
    api_workers: int = Field(default=1, description="API工作进程数")
    training_data_path: Optional[str] = Field(default=None, description="训练数据路径")
    knowledge_base_path: str = Field(default="data/knowledge_base.db", description="知识库路径")


def load_config() -> Config:
    """加载配置"""
    # 加载环境变量
    env_file = Path(".env")
    if env_file.exists():
        load_dotenv(env_file)
    
    # 检查必需的环境变量
    qwen_api_key = os.getenv("QWEN_API_KEY")
    if not qwen_api_key or qwen_api_key == "your_qwen_api_key_here":
        raise ValueError(
            "QWEN_API_KEY 未设置或使用默认值。\n"
            "请在 .env 文件中设置千问API密钥。\n"
            "获取地址: https://bailian.console.aliyun.com/"
        )
    
    # 构建配置
    config_data = {
        "app": {
            "name": os.getenv("APP_NAME", "TikTok AI Agent"),
            "version": os.getenv("APP_VERSION", "1.0.0"),
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "log_level": os.getenv("LOG_LEVEL", "INFO").upper(),
            "host": os.getenv("APP_HOST", "0.0.0.0"),
            "port": int(os.getenv("APP_PORT", "8000")),
        },
        "qwen": {
            "api_key": qwen_api_key,
            "api_base": os.getenv("QWEN_API_BASE", "https://dashscope.aliyuncs.com/api/v1"),
            "model": os.getenv("QWEN_MODEL", "qwen-turbo"),
            "timeout": int(os.getenv("QWEN_TIMEOUT", "30")),
            "max_retries": int(os.getenv("QWEN_MAX_RETRIES", "3")),
        },
        "database": {
            "host": os.getenv("DB_HOST", "localhost"),
            "port": int(os.getenv("DB_PORT", "5432")),
            "name": os.getenv("DB_NAME", "tiktok_data"),
            "user": os.getenv("DB_USER", "postgres"),
            "password": os.getenv("DB_PASSWORD", ""),
            "pool_size": int(os.getenv("DB_POOL_SIZE", "10")),
            "max_overflow": int(os.getenv("DB_MAX_OVERFLOW", "20")),
        },
        "redis": {
            "host": os.getenv("REDIS_HOST", "localhost"),
            "port": int(os.getenv("REDIS_PORT", "6379")),
            "db": int(os.getenv("REDIS_DB", "0")),
            "password": os.getenv("REDIS_PASSWORD") or None,
            "timeout": int(os.getenv("REDIS_TIMEOUT", "5")),
            "max_connections": int(os.getenv("REDIS_MAX_CONNECTIONS", "10")),
        },
        "vanna": {
            "model": os.getenv("VANNA_MODEL", "qwen"),
            "db_type": os.getenv("VANNA_DB_TYPE", "mysql"),
            "embedding_dimension": int(os.getenv("VANNA_EMBEDDING_DIM", "1536")),
            "max_context_length": int(os.getenv("VANNA_MAX_CONTEXT", "4000")),
            "similarity_threshold": float(os.getenv("VANNA_SIMILARITY_THRESHOLD", "0.7")),
        },
        "init_knowledge_base": os.getenv("INIT_KNOWLEDGE_BASE", "true").lower() == "true",
        "api_workers": int(os.getenv("API_WORKERS", "1")),
        "training_data_path": os.getenv("TRAINING_DATA_PATH") or None,
        "knowledge_base_path": os.getenv("KNOWLEDGE_BASE_PATH", "data/knowledge_base.db"),
    }
    
    return Config(**config_data)


# 全局配置实例
_config: Optional[Config] = None


def get_config() -> Config:
    """获取全局配置实例"""
    global _config
    if _config is None:
        _config = load_config()
    return _config


def reload_config() -> Config:
    """重新加载配置"""
    global _config
    _config = load_config()
    return _config


if __name__ == "__main__":
    # 测试配置加载
    try:
        config = load_config()
        print("✅ 配置加载成功")
        print(f"应用名称: {config.app.name}")
        print(f"调试模式: {config.app.debug}")
        print(f"千问模型: {config.qwen.model}")
        print(f"数据库: {config.database.host}:{config.database.port}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")