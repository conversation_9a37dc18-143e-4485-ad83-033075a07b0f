"""Agent配置管理器"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.roles.router_role import RouterRole
from agents.roles.display_role import DisplayRole
from agents.roles.vanna_role import Vanna<PERSON>ole
from agents.prompts.router_prompts import RouterPrompts
from agents.prompts.display_prompts import DisplayPrompts
from agents.prompts.vanna_prompts import VannaPrompts


class AgentConfigManager:
    """Agent配置管理器"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录下的config
        """
        self.config_dir = config_dir or Path(__file__).parent / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        # 初始化角色配置
        self.router_role = RouterRole()
        self.display_role = DisplayRole()
        self.vanna_role = VannaRole()
        
        # 初始化提示词管理器
        self.router_prompts = RouterPrompts()
        self.display_prompts = DisplayPrompts()
        self.vanna_prompts = VannaPrompts()
    
    def get_agent_config(self, agent_type: str) -> Dict[str, Any]:
        """
        获取指定Agent的配置
        
        Args:
            agent_type: Agent类型 (router, display, vanna)
            
        Returns:
            Agent配置字典
        """
        if agent_type == "router":
            return self.router_role.to_dict()
        elif agent_type == "display":
            return self.display_role.to_dict()
        elif agent_type == "vanna":
            return self.vanna_role.to_dict()
        else:
            raise ValueError(f"未知的Agent类型: {agent_type}")
    
    def get_prompt_manager(self, agent_type: str):
        """
        获取指定Agent的提示词管理器
        
        Args:
            agent_type: Agent类型
            
        Returns:
            提示词管理器实例
        """
        if agent_type == "router":
            return self.router_prompts
        elif agent_type == "display":
            return self.display_prompts
        elif agent_type == "vanna":
            return self.vanna_prompts
        else:
            raise ValueError(f"未知的Agent类型: {agent_type}")
    
    def save_config_to_file(self, agent_type: str, format: str = "json"):
        """
        保存Agent配置到文件
        
        Args:
            agent_type: Agent类型
            format: 文件格式 (json, yaml)
        """
        config = self.get_agent_config(agent_type)
        
        if format == "json":
            file_path = self.config_dir / f"{agent_type}_config.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        elif format == "yaml":
            file_path = self.config_dir / f"{agent_type}_config.yaml"
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        
        print(f"配置已保存到: {file_path}")
    
    def load_config_from_file(self, agent_type: str, format: str = "json") -> Dict[str, Any]:
        """
        从文件加载Agent配置
        
        Args:
            agent_type: Agent类型
            format: 文件格式
            
        Returns:
            配置字典
        """
        if format == "json":
            file_path = self.config_dir / f"{agent_type}_config.json"
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif format == "yaml":
            file_path = self.config_dir / f"{agent_type}_config.yaml"
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
    
    def export_all_configs(self, format: str = "json"):
        """
        导出所有Agent配置
        
        Args:
            format: 文件格式
        """
        agent_types = ["router", "display", "vanna"]
        
        for agent_type in agent_types:
            try:
                self.save_config_to_file(agent_type, format)
                print(f"✅ {agent_type} Agent配置导出成功")
            except Exception as e:
                print(f"❌ {agent_type} Agent配置导出失败: {e}")
    
    def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        return {
            "system_name": "TikTok AI Agent System",
            "version": "1.0.0",
            "agents": {
                "router": {
                    "name": self.router_role.name,
                    "description": self.router_role.description,
                    "capabilities_count": len(self.router_role.capabilities)
                },
                "display": {
                    "name": self.display_role.name,
                    "description": self.display_role.description,
                    "capabilities_count": len(self.display_role.capabilities)
                },
                "vanna": {
                    "name": self.vanna_role.name,
                    "description": self.vanna_role.description,
                    "capabilities_count": len(self.vanna_role.capabilities)
                }
            },
            "workflow": [
                "用户输入 → 路由Agent → 意图识别",
                "数据查询 → Vanna核心 → SQL生成和执行",
                "结果分析 → 展示Agent → 洞察和可视化",
                "最终输出 → 用户界面 → 完整报告"
            ]
        }
    
    def validate_config(self, agent_type: str) -> Dict[str, Any]:
        """
        验证Agent配置
        
        Args:
            agent_type: Agent类型
            
        Returns:
            验证结果
        """
        try:
            config = self.get_agent_config(agent_type)
            
            # 基本字段检查
            required_fields = ["name", "description", "version", "capabilities", "workflow"]
            missing_fields = [field for field in required_fields if field not in config]
            
            # 能力数量检查
            capabilities_count = len(config.get("capabilities", []))
            
            # 工作流程步骤检查
            workflow_steps = len(config.get("workflow", []))
            
            return {
                "valid": len(missing_fields) == 0,
                "missing_fields": missing_fields,
                "capabilities_count": capabilities_count,
                "workflow_steps": workflow_steps,
                "config_size": len(str(config))
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": str(e)
            }


# 全局配置管理器实例
config_manager = AgentConfigManager()


def get_agent_config(agent_type: str) -> Dict[str, Any]:
    """获取Agent配置的便捷函数"""
    return config_manager.get_agent_config(agent_type)


def get_prompt_manager(agent_type: str):
    """获取提示词管理器的便捷函数"""
    return config_manager.get_prompt_manager(agent_type)


if __name__ == "__main__":
    # 测试配置管理器
    manager = AgentConfigManager()
    
    print("🤖 TikTok AI Agent 配置管理器")
    print("=" * 50)
    
    # 显示系统概览
    overview = manager.get_system_overview()
    print(f"系统名称: {overview['system_name']}")
    print(f"版本: {overview['version']}")
    print()
    
    # 显示各Agent信息
    for agent_type, info in overview['agents'].items():
        print(f"📋 {info['name']}")
        print(f"   描述: {info['description']}")
        print(f"   能力数量: {info['capabilities_count']}")
        
        # 验证配置
        validation = manager.validate_config(agent_type)
        status = "✅ 有效" if validation['valid'] else "❌ 无效"
        print(f"   配置状态: {status}")
        print()
    
    # 导出配置文件
    print("📁 导出配置文件...")
    manager.export_all_configs("json")
    print("\n✅ 配置管理器测试完成！")