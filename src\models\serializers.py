"""数据模型序列化工具"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel


class ModelSerializer:
    """模型序列化器"""
    
    @staticmethod
    def to_dict(model: BaseModel, exclude_none: bool = True) -> Dict[str, Any]:
        """将模型转换为字典"""
        return model.model_dump(exclude_none=exclude_none)
    
    @staticmethod
    def to_json(model: BaseModel, exclude_none: bool = True, indent: int = None) -> str:
        """将模型转换为JSON字符串"""
        return model.model_dump_json(exclude_none=exclude_none, indent=indent)
    
    @staticmethod
    def from_dict(model_class: type, data: Dict[str, Any]) -> BaseModel:
        """从字典创建模型实例"""
        return model_class(**data)
    
    @staticmethod
    def from_json(model_class: type, json_str: str) -> BaseModel:
        """从JSON字符串创建模型实例"""
        return model_class.parse_raw(json_str)


class DatabaseSerializer:
    """数据库序列化器"""
    
    @staticmethod
    def model_to_db_dict(model: BaseModel) -> Dict[str, Any]:
        """将模型转换为数据库字典格式"""
        data = model.model_dump()
        
        # 处理特殊类型
        for key, value in data.items():
            if isinstance(value, UUID):
                data[key] = str(value)
            elif isinstance(value, datetime):
                data[key] = value.isoformat()
            elif isinstance(value, list) and value and isinstance(value[0], float):
                # 处理嵌入向量
                data[key] = json.dumps(value)
                
        return data
    
    @staticmethod
    def db_dict_to_model(model_class: type, data: Dict[str, Any]) -> BaseModel:
        """从数据库字典创建模型实例"""
        # 处理特殊类型转换
        processed_data = {}
        
        for key, value in data.items():
            if key.endswith('_id') and isinstance(value, str):
                try:
                    processed_data[key] = UUID(value)
                except ValueError:
                    processed_data[key] = value
            elif key.endswith('_at') and isinstance(value, str):
                try:
                    processed_data[key] = datetime.fromisoformat(value)
                except ValueError:
                    processed_data[key] = value
            elif key == 'embedding' and isinstance(value, str):
                try:
                    processed_data[key] = json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    processed_data[key] = value
            else:
                processed_data[key] = value
                
        return model_class(**processed_data)


class APISerializer:
    """API序列化器"""
    
    @staticmethod
    def serialize_response(data: Any, success: bool = True, message: str = "") -> Dict[str, Any]:
        """序列化API响应"""
        response = {
            "success": success,
            "message": message,
            "data": None,
            "timestamp": datetime.now().isoformat()
        }
        
        if isinstance(data, BaseModel):
            response["data"] = data.model_dump()
        elif isinstance(data, list) and data and isinstance(data[0], BaseModel):
            response["data"] = [item.model_dump() for item in data]
        else:
            response["data"] = data
            
        return response
    
    @staticmethod
    def serialize_error(error: str, message: str, details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """序列化错误响应"""
        return {
            "success": False,
            "error": error,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def serialize_paginated(items: List[Any], total: int, page: int, page_size: int) -> Dict[str, Any]:
        """序列化分页响应"""
        serialized_items = []
        for item in items:
            if isinstance(item, BaseModel):
                serialized_items.append(item.model_dump())
            else:
                serialized_items.append(item)
        
        return {
            "items": serialized_items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "has_next": (page * page_size) < total,
            "has_prev": page > 1,
            "total_pages": (total + page_size - 1) // page_size
        }


class QuerySerializer:
    """查询序列化器"""
    
    @staticmethod
    def serialize_query_result(data: List[Dict[str, Any]], columns: List[str], execution_time: float) -> Dict[str, Any]:
        """序列化查询结果"""
        return {
            "data": data,
            "columns": columns,
            "row_count": len(data),
            "execution_time": execution_time,
            "has_data": len(data) > 0
        }
    
    @staticmethod
    def serialize_visualization(chart_type: str, title: str, data: Dict[str, Any], config: Dict[str, Any] = None) -> Dict[str, Any]:
        """序列化可视化配置"""
        return {
            "chart_type": chart_type,
            "title": title,
            "data": data,
            "config": config or {},
            "created_at": datetime.now().isoformat()
        }
    
    @staticmethod
    def serialize_analysis_result(summary: str, insights: List[str], key_metrics: Dict[str, Any], recommendations: List[str] = None) -> Dict[str, Any]:
        """序列化分析结果"""
        return {
            "summary": summary,
            "insights": insights,
            "key_metrics": key_metrics,
            "recommendations": recommendations or [],
            "generated_at": datetime.now().isoformat()
        }


def safe_serialize(obj: Any) -> Any:
    """安全序列化对象"""
    if isinstance(obj, BaseModel):
        return obj.model_dump()
    elif isinstance(obj, (UUID, datetime)):
        return str(obj)
    elif isinstance(obj, dict):
        return {k: safe_serialize(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [safe_serialize(item) for item in obj]
    else:
        return obj