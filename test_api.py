#!/usr/bin/env python3
"""测试API接口"""

import httpx
import json
import sys


def test_api():
    """测试API接口"""
    base_url = "http://localhost:8000"
    
    print("🧪 测试TikTok AI Agent API")
    print("=" * 40)
    
    try:
        # 测试根路径
        print("1. 测试根路径...")
        response = httpx.get(f"{base_url}/")
        if response.status_code == 200:
            print("✅ 根路径正常")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 根路径失败: {response.status_code}")
            
        # 测试健康检查
        print("\n2. 测试健康检查...")
        response = httpx.get(f"{base_url}/api/v1/health")
        if response.status_code == 200:
            print("✅ 健康检查正常")
            data = response.json()
            print(f"   状态: {data['status']}")
            print(f"   版本: {data['version']}")
            print(f"   依赖: {data['dependencies']}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
        # 测试配置信息
        print("\n3. 测试配置信息...")
        response = httpx.get(f"{base_url}/api/v1/config")
        if response.status_code == 200:
            print("✅ 配置信息正常")
            data = response.json()
            print(f"   应用: {data['app']['name']} v{data['app']['version']}")
            print(f"   千问API: {'已配置' if data['qwen']['api_configured'] else '未配置'}")
        else:
            print(f"❌ 配置信息失败: {response.status_code}")
            
        # 测试查询接口
        print("\n4. 测试查询接口...")
        query_data = {
            "question": "测试查询",
            "user_id": "test_user",
            "session_id": "test_session"
        }
        response = httpx.post(f"{base_url}/api/v1/query", json=query_data)
        if response.status_code == 200:
            print("✅ 查询接口正常")
            data = response.json()
            print(f"   消息: {data['message']}")
            print(f"   状态: {data['status']}")
        else:
            print(f"❌ 查询接口失败: {response.status_code}")
            if response.status_code == 500:
                error_data = response.json()
                print(f"   错误: {error_data.get('detail', '未知错误')}")
                
        print("\n🎉 API测试完成")
        
    except httpx.ConnectError:
        print("❌ 无法连接到API服务")
        print("请确保API服务正在运行:")
        print("python main.py --mode api --port 8000")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_api()