"""
查询处理路由
处理用户的自然语言查询请求
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
import uuid

from fastapi import APIRouter, HTTPException, BackgroundTasks, Query as QueryParam
from pydantic import BaseModel, Field

from ...agents.agent_coordinator import get_coordinator
from ...models.base import APIResponse

logger = logging.getLogger(__name__)

router = APIRouter()


class QueryRequest(BaseModel):
    """查询请求模型"""
    question: str = Field(..., description="用户的自然语言问题", min_length=1, max_length=1000)
    user_id: Optional[str] = Field(None, description="用户ID")
    session_id: Optional[str] = Field(None, description="会话ID")
    context: Optional[Dict[str, Any]] = Field(None, description="额外上下文信息")


class QueryResponse(BaseModel):
    """查询响应模型"""
    success: bool
    message: str
    query_id: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    task_id: str
    status: str
    progress: float
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class QueryHistoryItem(BaseModel):
    """查询历史项模型"""
    query_id: str
    question: str
    timestamp: str
    status: str
    processing_time: Optional[float] = None


class QueryHistoryResponse(BaseModel):
    """查询历史响应模型"""
    user_id: str
    total_queries: int
    queries: List[QueryHistoryItem]


@router.post("/query", response_model=QueryResponse, summary="处理自然语言查询")
async def process_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """
    处理用户的自然语言查询
    
    接收用户的自然语言问题，通过AI Agent系统进行处理，
    返回包含SQL查询、数据结果、分析和可视化的完整响应。
    
    - **question**: 用户的自然语言问题
    - **user_id**: 可选的用户标识
    - **session_id**: 可选的会话标识，用于上下文管理
    - **context**: 可选的额外上下文信息
    """
    start_time = datetime.now()
    query_id = str(uuid.uuid4())
    
    try:
        logger.info(f"Processing query {query_id}: {request.question[:100]}...")
        
        # 生成会话ID（如果未提供）
        session_id = request.session_id or str(uuid.uuid4())
        
        # 获取协调器
        coordinator = await get_coordinator()
        
        # 处理用户请求
        result = await coordinator.process_user_request(
            user_input=request.question,
            session_id=session_id,
            user_id=request.user_id
        )
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # 记录查询历史（后台任务）
        background_tasks.add_task(
            _record_query_history,
            query_id=query_id,
            user_id=request.user_id,
            question=request.question,
            processing_time=processing_time,
            success=result.success
        )
        
        return QueryResponse(
            success=result.success,
            message=result.message,
            query_id=query_id,
            data=result.data,
            error=result.error,
            timestamp=datetime.now().isoformat(),
            processing_time=processing_time
        )
        
    except Exception as e:
        processing_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Query processing failed for {query_id}: {e}")
        
        # 记录失败的查询
        background_tasks.add_task(
            _record_query_history,
            query_id=query_id,
            user_id=request.user_id,
            question=request.question,
            processing_time=processing_time,
            success=False,
            error=str(e)
        )
        
        return QueryResponse(
            success=False,
            message="查询处理失败",
            query_id=query_id,
            error=str(e),
            timestamp=datetime.now().isoformat(),
            processing_time=processing_time
        )


@router.get("/query/{query_id}/status", response_model=TaskStatusResponse, summary="获取查询状态")
async def get_query_status(query_id: str):
    """
    获取指定查询的处理状态
    
    用于查询异步处理任务的当前状态和进度。
    
    - **query_id**: 查询ID
    """
    try:
        coordinator = await get_coordinator()
        task_status = await coordinator.get_task_status(query_id)
        
        if not task_status:
            raise HTTPException(status_code=404, detail="Query not found")
        
        return TaskStatusResponse(**task_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get query status for {query_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get query status")


@router.delete("/query/{query_id}", summary="取消查询")
async def cancel_query(query_id: str):
    """
    取消正在处理的查询
    
    取消指定的查询任务（如果还在处理中）。
    
    - **query_id**: 查询ID
    """
    try:
        coordinator = await get_coordinator()
        cancelled = await coordinator.cancel_task(query_id)
        
        if not cancelled:
            raise HTTPException(status_code=404, detail="Query not found or cannot be cancelled")
        
        return {
            "success": True,
            "message": "Query cancelled successfully",
            "query_id": query_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel query {query_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel query")


@router.get("/query/history/{user_id}", response_model=QueryHistoryResponse, summary="获取用户查询历史")
async def get_user_query_history(
    user_id: str,
    limit: int = QueryParam(20, ge=1, le=100, description="返回记录数量限制"),
    offset: int = QueryParam(0, ge=0, description="偏移量")
):
    """
    获取指定用户的查询历史
    
    返回用户的历史查询记录，支持分页。
    
    - **user_id**: 用户ID
    - **limit**: 返回记录数量限制（1-100）
    - **offset**: 偏移量，用于分页
    """
    try:
        # 这里应该从数据库或缓存中获取查询历史
        # 目前返回模拟数据
        mock_queries = [
            QueryHistoryItem(
                query_id=str(uuid.uuid4()),
                question="今天涨粉最多的前10个达人是谁？",
                timestamp=(datetime.now()).isoformat(),
                status="completed",
                processing_time=1.23
            ),
            QueryHistoryItem(
                query_id=str(uuid.uuid4()),
                question="过去一周哪个游戏达人播放量最高？",
                timestamp=(datetime.now()).isoformat(),
                status="completed",
                processing_time=2.45
            )
        ]
        
        # 应用分页
        paginated_queries = mock_queries[offset:offset + limit]
        
        return QueryHistoryResponse(
            user_id=user_id,
            total_queries=len(mock_queries),
            queries=paginated_queries
        )
        
    except Exception as e:
        logger.error(f"Failed to get query history for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get query history")


@router.get("/query/examples", summary="获取查询示例")
async def get_query_examples():
    """
    获取查询示例
    
    返回一些示例查询，帮助用户了解系统能力。
    """
    examples = [
        {
            "category": "达人分析",
            "examples": [
                "今天涨粉最多的前10个达人是谁？",
                "各个分类达人的平均粉丝数和视频数是多少？",
                "粉丝数超过100万的游戏类达人有多少个？"
            ]
        },
        {
            "category": "视频分析",
            "examples": [
                "过去一周哪个游戏达人播放量最高？",
                "找出互动率最高的100个视频",
                "美妆类视频的平均点赞率是多少？"
            ]
        },
        {
            "category": "趋势分析",
            "examples": [
                "查询某个达人最近30天的粉丝增长趋势",
                "分析不同分类达人的增长情况",
                "统计本月热门话题的视频数量"
            ]
        },
        {
            "category": "对比分析",
            "examples": [
                "比较美妆和游戏类达人的平均互动率",
                "分析工作日和周末的视频发布量差异",
                "对比不同时间段的用户活跃度"
            ]
        }
    ]
    
    return {
        "success": True,
        "message": "Query examples retrieved successfully",
        "data": {
            "categories": examples,
            "total_examples": sum(len(cat["examples"]) for cat in examples)
        },
        "timestamp": datetime.now().isoformat()
    }


async def _record_query_history(query_id: str, user_id: Optional[str], question: str,
                               processing_time: float, success: bool, error: Optional[str] = None):
    """记录查询历史（后台任务）"""
    try:
        # 这里应该将查询历史保存到数据库
        # 目前只记录日志
        logger.info(f"Query history recorded: {query_id}, user: {user_id}, "
                   f"success: {success}, time: {processing_time:.3f}s")
        
        if error:
            logger.error(f"Query {query_id} failed: {error}")
            
    except Exception as e:
        logger.error(f"Failed to record query history: {e}")


@router.post("/query/batch", summary="批量查询处理")
async def process_batch_queries(queries: List[QueryRequest]):
    """
    批量处理多个查询
    
    同时处理多个查询请求，返回所有查询的结果。
    
    - **queries**: 查询请求列表
    """
    if len(queries) > 10:
        raise HTTPException(status_code=400, detail="Batch size cannot exceed 10 queries")
    
    try:
        coordinator = await get_coordinator()
        results = []
        
        # 并行处理所有查询
        tasks = []
        for i, query_request in enumerate(queries):
            session_id = query_request.session_id or f"batch_{uuid.uuid4()}_{i}"
            task = coordinator.process_user_request(
                user_input=query_request.question,
                session_id=session_id,
                user_id=query_request.user_id
            )
            tasks.append(task)
        
        # 等待所有任务完成
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(batch_results):
            query_id = str(uuid.uuid4())
            
            if isinstance(result, Exception):
                results.append(QueryResponse(
                    success=False,
                    message="查询处理失败",
                    query_id=query_id,
                    error=str(result),
                    timestamp=datetime.now().isoformat()
                ))
            else:
                results.append(QueryResponse(
                    success=result.success,
                    message=result.message,
                    query_id=query_id,
                    data=result.data,
                    error=result.error,
                    timestamp=datetime.now().isoformat()
                ))
        
        return {
            "success": True,
            "message": f"Processed {len(queries)} queries",
            "data": {
                "results": results,
                "total_queries": len(queries),
                "successful_queries": sum(1 for r in results if r.success),
                "failed_queries": sum(1 for r in results if not r.success)
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Batch query processing failed: {e}")
        raise HTTPException(status_code=500, detail="Batch query processing failed")