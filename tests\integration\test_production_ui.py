"""
生产版本UI集成测试
测试生产版本UI的基本功能和数据处理
"""

import pytest
import pandas as pd
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch
import json

# 基础功能测试，不依赖复杂的Agent系统
class TestProductionUI:
    """生产版本UI测试"""
    
    def test_pandas_data_processing(self):
        """测试pandas数据处理功能"""
        # 模拟TikTok数据
        sample_data = [
            {"username": "gamer_king", "display_name": "游戏王者", "follower_growth": 15420, "category": "游戏"},
            {"username": "beauty_queen", "display_name": "美妆女王", "follower_growth": 12350, "category": "美妆"},
            {"username": "dance_star", "display_name": "舞蹈之星", "follower_growth": 9870, "category": "舞蹈"},
            {"username": "food_lover", "display_name": "美食达人", "follower_growth": 8650, "category": "美食"},
            {"username": "music_master", "display_name": "音乐大师", "follower_growth": 7430, "category": "音乐"}
        ]
        
        df = pd.DataFrame(sample_data)
        
        # 基本数据验证
        assert not df.empty
        assert len(df) == 5
        assert list(df.columns) == ["username", "display_name", "follower_growth", "category"]
        
        # 数据统计
        assert df["follower_growth"].max() == 15420
        assert df["follower_growth"].min() == 7430
        assert df["follower_growth"].mean() == 10744.0
        
        # 数据筛选
        gaming_users = df[df["category"] == "游戏"]
        assert len(gaming_users) == 1
        assert gaming_users.iloc[0]["username"] == "gamer_king"
        
        # 数据排序
        sorted_df = df.sort_values("follower_growth", ascending=False)
        assert sorted_df.iloc[0]["username"] == "gamer_king"
        assert sorted_df.iloc[-1]["username"] == "music_master"
    
    def test_visualization_data_preparation(self):
        """测试可视化数据准备"""
        try:
            import plotly.express as px
            import plotly.graph_objects as go
            
            # 测试数据
            data = [
                {"category": "游戏", "count": 150, "avg_followers": 50000},
                {"category": "美妆", "count": 120, "avg_followers": 45000},
                {"category": "舞蹈", "count": 100, "avg_followers": 40000},
                {"category": "美食", "count": 80, "avg_followers": 35000}
            ]
            
            df = pd.DataFrame(data)
            
            # 测试柱状图数据
            fig_bar = px.bar(df, x="category", y="count", title="分类统计")
            assert fig_bar is not None
            assert len(fig_bar.data) > 0
            
            # 测试饼图数据
            fig_pie = px.pie(df, values="count", names="category", title="分类分布")
            assert fig_pie is not None
            assert len(fig_pie.data) > 0
            
            # 测试散点图数据
            fig_scatter = px.scatter(df, x="count", y="avg_followers", 
                                   color="category", title="粉丝数与内容数关系")
            assert fig_scatter is not None
            assert len(fig_scatter.data) > 0
            
        except ImportError:
            pytest.skip("Plotly未安装，跳过可视化测试")
    
    def test_query_response_structure(self):
        """测试查询响应结构"""
        # 模拟完整的查询响应
        response_data = {
            "type": "complete_analysis",
            "task_id": "test-task-123",
            "query": "今天涨粉最多的前10个达人是谁？",
            "sql": "SELECT username, display_name, follower_growth FROM creators ORDER BY follower_growth DESC LIMIT 10",
            "raw_data": {
                "data": [
                    {"username": "gamer_king", "display_name": "游戏王者", "follower_growth": 15420},
                    {"username": "beauty_queen", "display_name": "美妆女王", "follower_growth": 12350}
                ],
                "columns": ["username", "display_name", "follower_growth"],
                "row_count": 2,
                "execution_time": 0.245,
                "is_mock": True
            },
            "analysis": {
                "summary": "根据今日数据分析，游戏王者以15,420的涨粉量位居第一。",
                "insights": [
                    "游戏内容在当前具有很强的吸引力",
                    "美妆内容也表现不错"
                ],
                "recommendations": [
                    "关注高涨粉达人的内容策略",
                    "考虑游戏分类的相关合作"
                ],
                "key_metrics": {
                    "最高涨粉": "15,420",
                    "平均涨粉": "13,885",
                    "总涨粉": "27,770"
                }
            },
            "visualizations": [
                {
                    "chart_type": "bar",
                    "title": "今日涨粉排行榜",
                    "description": "显示今日涨粉最多的达人",
                    "code": "fig = px.bar(df, x='display_name', y='follower_growth', title='今日涨粉排行榜')\nst.plotly_chart(fig, use_container_width=True)"
                }
            ],
            "generated_at": "2025-01-24T10:30:00"
        }
        
        # 验证响应结构
        assert response_data["type"] == "complete_analysis"
        assert "task_id" in response_data
        assert "query" in response_data
        assert "sql" in response_data
        assert "raw_data" in response_data
        assert "analysis" in response_data
        assert "visualizations" in response_data
        
        # 验证原始数据结构
        raw_data = response_data["raw_data"]
        assert "data" in raw_data
        assert "columns" in raw_data
        assert "row_count" in raw_data
        assert "execution_time" in raw_data
        assert isinstance(raw_data["data"], list)
        assert isinstance(raw_data["columns"], list)
        assert isinstance(raw_data["row_count"], int)
        assert isinstance(raw_data["execution_time"], float)
        
        # 验证分析结果结构
        analysis = response_data["analysis"]
        assert "summary" in analysis
        assert "insights" in analysis
        assert "recommendations" in analysis
        assert "key_metrics" in analysis
        assert isinstance(analysis["insights"], list)
        assert isinstance(analysis["recommendations"], list)
        assert isinstance(analysis["key_metrics"], dict)
        
        # 验证可视化结构
        visualizations = response_data["visualizations"]
        assert isinstance(visualizations, list)
        assert len(visualizations) > 0
        
        for viz in visualizations:
            assert "chart_type" in viz
            assert "title" in viz
            assert "code" in viz
    
    def test_sql_analysis_function(self):
        """测试SQL分析功能"""
        def analyze_sql(sql: str) -> dict:
            """简单的SQL分析函数"""
            analysis = {}
            sql_upper = sql.upper()
            
            # 查询类型
            if 'SELECT' in sql_upper:
                analysis['查询类型'] = 'SELECT (数据查询)'
            elif 'INSERT' in sql_upper:
                analysis['查询类型'] = 'INSERT (数据插入)'
            elif 'UPDATE' in sql_upper:
                analysis['查询类型'] = 'UPDATE (数据更新)'
            elif 'DELETE' in sql_upper:
                analysis['查询类型'] = 'DELETE (数据删除)'
            
            # 涉及的表
            import re
            tables = re.findall(r'FROM\s+(\w+)', sql_upper)
            if tables:
                analysis['主要表'] = ', '.join(tables)
            
            # 是否有JOIN
            analysis['表连接'] = '是' if 'JOIN' in sql_upper else '否'
            
            # 是否有WHERE条件
            analysis['条件筛选'] = '是' if 'WHERE' in sql_upper else '否'
            
            # 是否有排序
            analysis['结果排序'] = '是' if 'ORDER BY' in sql_upper else '否'
            
            # 是否有限制
            analysis['结果限制'] = '是' if 'LIMIT' in sql_upper else '否'
            
            return analysis
        
        # 测试简单SELECT查询
        sql1 = "SELECT username, follower_count FROM creators WHERE category = 'gaming' ORDER BY follower_count DESC LIMIT 10"
        analysis1 = analyze_sql(sql1)
        
        assert analysis1["查询类型"] == "SELECT (数据查询)"
        assert analysis1["主要表"].lower() == "creators"
        assert analysis1["条件筛选"] == "是"
        assert analysis1["结果排序"] == "是"
        assert analysis1["结果限制"] == "是"
        assert analysis1["表连接"] == "否"
        
        # 测试JOIN查询
        sql2 = "SELECT c.username, v.title FROM creators c JOIN videos v ON c.id = v.creator_id"
        analysis2 = analyze_sql(sql2)
        
        assert analysis2["查询类型"] == "SELECT (数据查询)"
        assert analysis2["表连接"] == "是"
        assert analysis2["条件筛选"] == "否"
        assert analysis2["结果排序"] == "否"
        assert analysis2["结果限制"] == "否"
    
    def test_session_state_management(self):
        """测试会话状态管理"""
        # 模拟Streamlit会话状态
        class MockSessionState:
            def __init__(self):
                self._state = {}
            
            def __contains__(self, key):
                return key in self._state
            
            def __getitem__(self, key):
                return self._state[key]
            
            def __setitem__(self, key, value):
                self._state[key] = value
            
            def get(self, key, default=None):
                return self._state.get(key, default)
        
        # 初始化会话状态
        session_state = MockSessionState()
        
        # 模拟初始化逻辑
        if 'session_id' not in session_state:
            session_state['session_id'] = "test-session-123"
        
        if 'query_history' not in session_state:
            session_state['query_history'] = []
        
        if 'current_query' not in session_state:
            session_state['current_query'] = None
        
        # 验证初始化
        assert session_state['session_id'] == "test-session-123"
        assert isinstance(session_state['query_history'], list)
        assert session_state['current_query'] is None
        
        # 测试添加查询历史
        query_history = session_state['query_history']
        query_history.append({
            'question': "今天涨粉最多的前10个达人是谁？",
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        
        assert len(query_history) == 1
        assert query_history[0]['question'] == "今天涨粉最多的前10个达人是谁？"
        assert 'timestamp' in query_history[0]
    
    def test_data_export_functionality(self):
        """测试数据导出功能"""
        # 创建测试数据
        data = [
            {"username": "user1", "followers": 10000, "category": "游戏"},
            {"username": "user2", "followers": 8000, "category": "美妆"},
            {"username": "user3", "followers": 12000, "category": "舞蹈"}
        ]
        
        df = pd.DataFrame(data)
        
        # 测试CSV导出
        csv_data = df.to_csv(index=False, encoding='utf-8-sig')
        assert isinstance(csv_data, str)
        assert "username,followers,category" in csv_data
        assert "user1,10000,游戏" in csv_data
        
        # 测试JSON导出
        json_data = df.to_json(orient='records', force_ascii=False)
        parsed_json = json.loads(json_data)
        assert isinstance(parsed_json, list)
        assert len(parsed_json) == 3
        assert parsed_json[0]["username"] == "user1"
    
    def test_error_handling_scenarios(self):
        """测试错误处理场景"""
        # 测试空数据处理
        empty_df = pd.DataFrame()
        assert empty_df.empty
        
        # 测试无效数据处理
        invalid_data = [
            {"username": None, "followers": "invalid"},
            {"username": "user2", "followers": -1000}
        ]
        
        df = pd.DataFrame(invalid_data)
        
        # 检查数据清理
        cleaned_df = df.dropna()  # 移除空值
        numeric_df = pd.to_numeric(df["followers"], errors='coerce')  # 转换数值
        
        assert len(cleaned_df) == 1  # 应该只剩一行
        assert pd.isna(numeric_df.iloc[0])  # 第一个值应该是NaN
    
    def test_visualization_code_execution(self):
        """测试可视化代码执行"""
        try:
            import plotly.express as px
            
            # 模拟可视化代码
            viz_code = """
fig = px.bar(df, x='display_name', y='follower_growth', 
             title='今日涨粉排行榜',
             color='follower_growth',
             color_continuous_scale='viridis')
fig.update_layout(xaxis_title='达人名称', yaxis_title='涨粉数量')
"""
            
            # 准备执行环境
            df = pd.DataFrame([
                {"display_name": "游戏王者", "follower_growth": 15420},
                {"display_name": "美妆女王", "follower_growth": 12350},
                {"display_name": "舞蹈之星", "follower_growth": 9870}
            ])
            
            exec_globals = {
                'df': df,
                'px': px,
                'pd': pd
            }
            
            # 执行可视化代码
            exec(viz_code, exec_globals)
            
            # 验证图表对象被创建
            assert 'fig' in exec_globals
            fig = exec_globals['fig']
            assert fig is not None
            assert len(fig.data) > 0
            
        except ImportError:
            pytest.skip("Plotly未安装，跳过可视化代码测试")
    
    def test_metrics_calculation(self):
        """测试指标计算"""
        # 模拟粉丝增长数据
        data = [15420, 12350, 9870, 8650, 7430]
        
        # 计算关键指标
        max_growth = max(data)
        min_growth = min(data)
        avg_growth = sum(data) / len(data)
        total_growth = sum(data)
        
        assert max_growth == 15420
        assert min_growth == 7430
        assert avg_growth == 10744.0
        assert total_growth == 53720
        
        # 格式化指标
        formatted_metrics = {
            "最高涨粉": f"{max_growth:,}",
            "最低涨粉": f"{min_growth:,}",
            "平均涨粉": f"{avg_growth:,.0f}",
            "总涨粉": f"{total_growth:,}"
        }
        
        assert formatted_metrics["最高涨粉"] == "15,420"
        assert formatted_metrics["平均涨粉"] == "10,744"
        assert formatted_metrics["总涨粉"] == "53,720"
    
    def test_query_examples_validation(self):
        """测试查询示例验证"""
        # 定义查询示例
        examples = [
            "今天涨粉最多的前10个达人是谁？",
            "过去一周哪个游戏达人播放量最高？",
            "美妆类视频的平均点赞率是多少？",
            "各个分类达人的平均粉丝数和视频数是多少？",
            "找出互动率最高的100个视频？",
            "分析最近热门视频的标签分布？",
            "统计不同时间段的用户活跃度？"
        ]
        
        # 验证示例格式
        for example in examples:
            assert isinstance(example, str)
            assert len(example) > 0
            assert example.endswith("？") or example.endswith("?")
        
        # 验证示例覆盖不同类型的查询
        query_types = {
            "排行": any("前" in ex and "达人" in ex for ex in examples),
            "统计": any("统计" in ex for ex in examples),
            "分析": any("分析" in ex for ex in examples),
            "筛选": any("找出" in ex for ex in examples)
        }
        
        assert all(query_types.values()), f"查询示例类型覆盖不完整: {query_types}"


class TestUIHelperFunctions:
    """UI辅助函数测试"""
    
    def test_timestamp_formatting(self):
        """测试时间戳格式化"""
        now = datetime.now()
        formatted = now.strftime("%Y-%m-%d %H:%M:%S")
        
        assert len(formatted) == 19  # YYYY-MM-DD HH:MM:SS
        assert formatted.count("-") == 2
        assert formatted.count(":") == 2
        assert formatted.count(" ") == 1
    
    def test_data_size_calculation(self):
        """测试数据大小计算"""
        # 创建测试数据
        df = pd.DataFrame({
            "username": ["user1", "user2", "user3"],
            "followers": [10000, 20000, 30000],
            "category": ["游戏", "美妆", "舞蹈"]
        })
        
        # 计算内存使用
        memory_usage = df.memory_usage(deep=True).sum()
        size_kb = memory_usage / 1024
        
        assert memory_usage > 0
        assert size_kb > 0
        assert isinstance(size_kb, float)
    
    def test_progress_tracking(self):
        """测试进度跟踪"""
        # 模拟进度跟踪
        progress_steps = [
            ("🔧 初始化Agent系统...", 0.1),
            ("🧠 分析用户意图...", 0.3),
            ("⚡ 生成SQL查询...", 0.5),
            ("🔍 执行数据查询...", 0.7),
            ("📊 生成分析报告...", 0.9),
            ("✅ 分析完成！", 1.0)
        ]
        
        for step_text, progress in progress_steps:
            assert isinstance(step_text, str)
            assert 0.0 <= progress <= 1.0
            assert len(step_text) > 0
        
        # 验证进度递增
        for i in range(1, len(progress_steps)):
            assert progress_steps[i][1] > progress_steps[i-1][1]


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])