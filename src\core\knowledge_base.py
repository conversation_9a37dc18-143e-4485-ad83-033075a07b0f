"""
知识库管理器
负责Schema DDL存储、业务文档嵌入和检索功能
"""

import json
import logging
import sqlite3
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID, uuid4
import numpy as np
from datetime import datetime

try:
    from ..models.knowledge import (
        SchemaInfo, BusinessDoc, SQLExample, 
        TrainingData, RetrievalResult
    )
except ImportError:
    # 绝对导入作为备选
    from models.knowledge import (
        SchemaInfo, BusinessDoc, SQLExample, 
        TrainingData, RetrievalResult
    )
from .config import get_config
from .qwen_embedding import CachedQwenEmbeddingService

logger = logging.getLogger(__name__)


class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self, db_path: Optional[str] = None, enable_embedding: bool = True):
        """
        初始化知识库管理器
        
        Args:
            db_path: 数据库文件路径，默认为 data/knowledge_base.db
            enable_embedding: 是否启用嵌入功能
        """
        self.config = get_config()
        
        # 设置数据库路径
        if db_path is None:
            db_path = Path("data") / "knowledge_base.db"
        
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化嵌入服务
        self.embedding_service = None
        if enable_embedding:
            try:
                self.embedding_service = CachedQwenEmbeddingService()
                logger.info("Embedding service initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize embedding service: {e}")
                self.embedding_service = None
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"KnowledgeBaseManager initialized with database: {self.db_path}")
    
    def _init_database(self) -> None:
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建Schema信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schema_info (
                    id TEXT PRIMARY KEY,
                    table_name TEXT NOT NULL UNIQUE,
                    ddl TEXT NOT NULL,
                    description TEXT,
                    embedding TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # 创建业务文档表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS business_docs (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    category TEXT,
                    embedding TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # 创建SQL范例表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sql_examples (
                    id TEXT PRIMARY KEY,
                    question TEXT NOT NULL,
                    sql TEXT NOT NULL,
                    explanation TEXT,
                    difficulty TEXT,
                    embedding TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_schema_table_name ON schema_info(table_name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_docs_category ON business_docs(category)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_examples_difficulty ON sql_examples(difficulty)")
            
            conn.commit()
            logger.info("Database tables initialized successfully")
    
    async def add_schema(self, table_name: str, ddl: str, description: Optional[str] = None) -> bool:
        """
        添加Schema DDL到知识库
        
        Args:
            table_name: 表名
            ddl: DDL语句
            description: 表描述
            
        Returns:
            bool: 是否添加成功
        """
        try:
            schema_info = SchemaInfo(
                table_name=table_name,
                ddl=ddl,
                description=description
            )
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查是否已存在
                cursor.execute("SELECT id FROM schema_info WHERE table_name = ?", (table_name,))
                existing = cursor.fetchone()
                
                if existing:
                    # 更新现有记录
                    cursor.execute("""
                        UPDATE schema_info 
                        SET ddl = ?, description = ?, updated_at = ?
                        WHERE table_name = ?
                    """, (ddl, description, datetime.now().isoformat(), table_name))
                    logger.info(f"Updated schema for table: {table_name}")
                else:
                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO schema_info (id, table_name, ddl, description, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        str(schema_info.id), table_name, ddl, description,
                        schema_info.created_at.isoformat(), schema_info.updated_at.isoformat()
                    ))
                    logger.info(f"Added new schema for table: {table_name}")
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to add schema for table {table_name}: {e}")
            return False
    
    async def add_business_doc(self, title: str, content: str, category: Optional[str] = None) -> bool:
        """
        添加业务文档到知识库
        
        Args:
            title: 文档标题
            content: 文档内容
            category: 文档分类
            
        Returns:
            bool: 是否添加成功
        """
        try:
            doc = BusinessDoc(
                title=title,
                content=content,
                category=category
            )
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO business_docs (id, title, content, category, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    str(doc.id), title, content, category,
                    doc.created_at.isoformat(), doc.updated_at.isoformat()
                ))
                
                conn.commit()
                logger.info(f"Added business document: {title}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add business document {title}: {e}")
            return False
    
    async def add_sql_example(self, question: str, sql: str, explanation: Optional[str] = None, 
                            difficulty: Optional[str] = None) -> bool:
        """
        添加SQL范例到知识库
        
        Args:
            question: 问题
            sql: SQL语句
            explanation: 解释说明
            difficulty: 难度等级
            
        Returns:
            bool: 是否添加成功
        """
        try:
            example = SQLExample(
                question=question,
                sql=sql,
                explanation=explanation,
                difficulty=difficulty
            )
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT INTO sql_examples (id, question, sql, explanation, difficulty, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    str(example.id), question, sql, explanation, difficulty,
                    example.created_at.isoformat(), example.updated_at.isoformat()
                ))
                
                conn.commit()
                logger.info(f"Added SQL example: {question[:50]}...")
                return True
                
        except Exception as e:
            logger.error(f"Failed to add SQL example: {e}")
            return False
    
    async def get_all_schemas(self) -> List[SchemaInfo]:
        """获取所有Schema信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, table_name, ddl, description, embedding, created_at, updated_at
                    FROM schema_info ORDER BY table_name
                """)
                
                schemas = []
                for row in cursor.fetchall():
                    embedding = json.loads(row[4]) if row[4] else None
                    schema = SchemaInfo(
                        id=UUID(row[0]),
                        table_name=row[1],
                        ddl=row[2],
                        description=row[3],
                        embedding=embedding,
                        created_at=datetime.fromisoformat(row[5]),
                        updated_at=datetime.fromisoformat(row[6])
                    )
                    schemas.append(schema)
                
                return schemas
                
        except Exception as e:
            logger.error(f"Failed to get schemas: {e}")
            return []
    
    async def get_all_business_docs(self) -> List[BusinessDoc]:
        """获取所有业务文档"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, title, content, category, embedding, created_at, updated_at
                    FROM business_docs ORDER BY title
                """)
                
                docs = []
                for row in cursor.fetchall():
                    embedding = json.loads(row[4]) if row[4] else None
                    doc = BusinessDoc(
                        id=UUID(row[0]),
                        title=row[1],
                        content=row[2],
                        category=row[3],
                        embedding=embedding,
                        created_at=datetime.fromisoformat(row[5]),
                        updated_at=datetime.fromisoformat(row[6])
                    )
                    docs.append(doc)
                
                return docs
                
        except Exception as e:
            logger.error(f"Failed to get business docs: {e}")
            return []
    
    async def get_all_sql_examples(self) -> List[SQLExample]:
        """获取所有SQL范例"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, question, sql, explanation, difficulty, embedding, created_at, updated_at
                    FROM sql_examples ORDER BY question
                """)
                
                examples = []
                for row in cursor.fetchall():
                    embedding = json.loads(row[5]) if row[5] else None
                    example = SQLExample(
                        id=UUID(row[0]),
                        question=row[1],
                        sql=row[2],
                        explanation=row[3],
                        difficulty=row[4],
                        embedding=embedding,
                        created_at=datetime.fromisoformat(row[6]),
                        updated_at=datetime.fromisoformat(row[7])
                    )
                    examples.append(example)
                
                return examples
                
        except Exception as e:
            logger.error(f"Failed to get SQL examples: {e}")
            return []
    
    async def search_schemas(self, query: str, limit: int = 10) -> List[SchemaInfo]:
        """
        搜索Schema信息
        
        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            List[SchemaInfo]: 匹配的Schema列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, table_name, ddl, description, embedding, created_at, updated_at
                    FROM schema_info 
                    WHERE table_name LIKE ? OR description LIKE ? OR ddl LIKE ?
                    ORDER BY table_name
                    LIMIT ?
                """, (f"%{query}%", f"%{query}%", f"%{query}%", limit))
                
                schemas = []
                for row in cursor.fetchall():
                    embedding = json.loads(row[4]) if row[4] else None
                    schema = SchemaInfo(
                        id=UUID(row[0]),
                        table_name=row[1],
                        ddl=row[2],
                        description=row[3],
                        embedding=embedding,
                        created_at=datetime.fromisoformat(row[5]),
                        updated_at=datetime.fromisoformat(row[6])
                    )
                    schemas.append(schema)
                
                return schemas
                
        except Exception as e:
            logger.error(f"Failed to search schemas: {e}")
            return []
    
    async def search_business_docs(self, query: str, category: Optional[str] = None, 
                                 limit: int = 10) -> List[BusinessDoc]:
        """
        搜索业务文档
        
        Args:
            query: 搜索查询
            category: 文档分类过滤
            limit: 返回结果数量限制
            
        Returns:
            List[BusinessDoc]: 匹配的文档列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if category:
                    cursor.execute("""
                        SELECT id, title, content, category, embedding, created_at, updated_at
                        FROM business_docs 
                        WHERE (title LIKE ? OR content LIKE ?) AND category = ?
                        ORDER BY title
                        LIMIT ?
                    """, (f"%{query}%", f"%{query}%", category, limit))
                else:
                    cursor.execute("""
                        SELECT id, title, content, category, embedding, created_at, updated_at
                        FROM business_docs 
                        WHERE title LIKE ? OR content LIKE ?
                        ORDER BY title
                        LIMIT ?
                    """, (f"%{query}%", f"%{query}%", limit))
                
                docs = []
                for row in cursor.fetchall():
                    embedding = json.loads(row[4]) if row[4] else None
                    doc = BusinessDoc(
                        id=UUID(row[0]),
                        title=row[1],
                        content=row[2],
                        category=row[3],
                        embedding=embedding,
                        created_at=datetime.fromisoformat(row[5]),
                        updated_at=datetime.fromisoformat(row[6])
                    )
                    docs.append(doc)
                
                return docs
                
        except Exception as e:
            logger.error(f"Failed to search business docs: {e}")
            return []
    
    async def update_embedding(self, item_type: str, item_id: UUID, embedding: List[float]) -> bool:
        """
        更新嵌入向量
        
        Args:
            item_type: 项目类型 ('schema', 'doc', 'example')
            item_id: 项目ID
            embedding: 嵌入向量
            
        Returns:
            bool: 是否更新成功
        """
        try:
            table_map = {
                'schema': 'schema_info',
                'doc': 'business_docs',
                'example': 'sql_examples'
            }
            
            if item_type not in table_map:
                raise ValueError(f"Invalid item_type: {item_type}")
            
            table_name = table_map[item_type]
            embedding_json = json.dumps(embedding)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"""
                    UPDATE {table_name} 
                    SET embedding = ?, updated_at = ?
                    WHERE id = ?
                """, (embedding_json, datetime.now().isoformat(), str(item_id)))
                
                if cursor.rowcount > 0:
                    conn.commit()
                    logger.info(f"Updated embedding for {item_type} {item_id}")
                    return True
                else:
                    logger.warning(f"No {item_type} found with id {item_id}")
                    return False
                
        except Exception as e:
            logger.error(f"Failed to update embedding: {e}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 统计各类数据数量
                cursor.execute("SELECT COUNT(*) FROM schema_info")
                schema_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM business_docs")
                doc_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM sql_examples")
                example_count = cursor.fetchone()[0]
                
                # 统计有嵌入向量的数量
                cursor.execute("SELECT COUNT(*) FROM schema_info WHERE embedding IS NOT NULL")
                schema_with_embedding = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM business_docs WHERE embedding IS NOT NULL")
                doc_with_embedding = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM sql_examples WHERE embedding IS NOT NULL")
                example_with_embedding = cursor.fetchone()[0]
                
                return {
                    "total_items": schema_count + doc_count + example_count,
                    "schemas": {
                        "total": schema_count,
                        "with_embedding": schema_with_embedding
                    },
                    "business_docs": {
                        "total": doc_count,
                        "with_embedding": doc_with_embedding
                    },
                    "sql_examples": {
                        "total": example_count,
                        "with_embedding": example_with_embedding
                    },
                    "embedding_coverage": {
                        "schemas": schema_with_embedding / schema_count if schema_count > 0 else 0,
                        "docs": doc_with_embedding / doc_count if doc_count > 0 else 0,
                        "examples": example_with_embedding / example_count if example_count > 0 else 0
                    }
                }
                
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}
    
    async def generate_embeddings_for_all(self) -> Dict[str, int]:
        """
        为所有没有嵌入向量的项目生成嵌入向量
        
        Returns:
            Dict[str, int]: 各类型生成的嵌入向量数量
        """
        if not self.embedding_service:
            logger.warning("Embedding service not available")
            return {"schemas": 0, "docs": 0, "examples": 0}
        
        results = {"schemas": 0, "docs": 0, "examples": 0}
        
        try:
            # 为Schema生成嵌入向量
            schemas = await self.get_all_schemas()
            for schema in schemas:
                if schema.embedding is None:
                    # 组合表名、DDL和描述作为嵌入文本
                    text_parts = [schema.table_name, schema.ddl]
                    if schema.description:
                        text_parts.append(schema.description)
                    
                    text = "\n".join(text_parts)
                    embedding = await self.embedding_service.get_embedding(text)
                    
                    if embedding:
                        await self.update_embedding("schema", schema.id, embedding)
                        results["schemas"] += 1
                        logger.info(f"Generated embedding for schema: {schema.table_name}")
            
            # 为业务文档生成嵌入向量
            docs = await self.get_all_business_docs()
            for doc in docs:
                if doc.embedding is None:
                    # 组合标题和内容作为嵌入文本
                    text = f"{doc.title}\n{doc.content}"
                    embedding = await self.embedding_service.get_embedding(text)
                    
                    if embedding:
                        await self.update_embedding("doc", doc.id, embedding)
                        results["docs"] += 1
                        logger.info(f"Generated embedding for document: {doc.title}")
            
            # 为SQL范例生成嵌入向量
            examples = await self.get_all_sql_examples()
            for example in examples:
                if example.embedding is None:
                    # 组合问题、SQL和解释作为嵌入文本
                    text_parts = [example.question, example.sql]
                    if example.explanation:
                        text_parts.append(example.explanation)
                    
                    text = "\n".join(text_parts)
                    embedding = await self.embedding_service.get_embedding(text)
                    
                    if embedding:
                        await self.update_embedding("example", example.id, embedding)
                        results["examples"] += 1
                        logger.info(f"Generated embedding for SQL example: {example.question[:50]}...")
            
            logger.info(f"Generated embeddings: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            return results
    
    async def similarity_search(self, query: str, top_k: int = 5, 
                              similarity_threshold: float = 0.7) -> RetrievalResult:
        """
        基于嵌入向量的相似度搜索
        
        Args:
            query: 查询文本
            top_k: 返回前k个最相似的结果
            similarity_threshold: 相似度阈值
            
        Returns:
            RetrievalResult: 检索结果
        """
        if not self.embedding_service:
            logger.warning("Embedding service not available, falling back to text search")
            # 回退到文本搜索
            schemas = await self.search_schemas(query, top_k)
            docs = await self.search_business_docs(query, limit=top_k)
            return RetrievalResult(
                schemas=schemas,
                documents=docs,
                sql_examples=[],
                similarity_scores=[],
                total_results=len(schemas) + len(docs)
            )
        
        try:
            # 生成查询嵌入向量
            query_embedding = await self.embedding_service.get_embedding(query)
            if not query_embedding:
                logger.error("Failed to generate query embedding")
                return RetrievalResult()
            
            # 获取所有有嵌入向量的项目
            all_candidates = []
            
            # 获取Schema候选
            schemas = await self.get_all_schemas()
            schema_candidates = [(schema, "schema") for schema in schemas if schema.embedding]
            all_candidates.extend(schema_candidates)
            
            # 获取文档候选
            docs = await self.get_all_business_docs()
            doc_candidates = [(doc, "doc") for doc in docs if doc.embedding]
            all_candidates.extend(doc_candidates)
            
            # 获取SQL范例候选
            examples = await self.get_all_sql_examples()
            example_candidates = [(example, "example") for example in examples if example.embedding]
            all_candidates.extend(example_candidates)
            
            if not all_candidates:
                logger.warning("No items with embeddings found")
                return RetrievalResult()
            
            # 计算相似度
            similarities = []
            for item, item_type in all_candidates:
                similarity = self.embedding_service.calculate_similarity(
                    query_embedding, item.embedding
                )
                if similarity >= similarity_threshold:
                    similarities.append((item, item_type, similarity))
            
            # 按相似度排序
            similarities.sort(key=lambda x: x[2], reverse=True)
            similarities = similarities[:top_k]
            
            # 分类结果
            result_schemas = []
            result_docs = []
            result_examples = []
            similarity_scores = []
            
            for item, item_type, score in similarities:
                similarity_scores.append(score)
                if item_type == "schema":
                    result_schemas.append(item)
                elif item_type == "doc":
                    result_docs.append(item)
                elif item_type == "example":
                    result_examples.append(item)
            
            return RetrievalResult(
                schemas=result_schemas,
                documents=result_docs,
                sql_examples=result_examples,
                similarity_scores=similarity_scores,
                total_results=len(similarities)
            )
            
        except Exception as e:
            logger.error(f"Failed to perform similarity search: {e}")
            return RetrievalResult()
    
    async def find_similar_schemas(self, query: str, top_k: int = 5) -> List[Tuple[SchemaInfo, float]]:
        """
        查找相似的Schema
        
        Args:
            query: 查询文本
            top_k: 返回数量
            
        Returns:
            List[Tuple[SchemaInfo, float]]: (Schema, 相似度分数) 列表
        """
        if not self.embedding_service:
            return []
        
        try:
            query_embedding = await self.embedding_service.get_embedding(query)
            if not query_embedding:
                return []
            
            schemas = await self.get_all_schemas()
            schema_with_embeddings = [s for s in schemas if s.embedding]
            
            if not schema_with_embeddings:
                return []
            
            similarities = []
            for schema in schema_with_embeddings:
                similarity = self.embedding_service.calculate_similarity(
                    query_embedding, schema.embedding
                )
                similarities.append((schema, similarity))
            
            # 按相似度排序并返回前k个
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find similar schemas: {e}")
            return []
    
    async def find_similar_docs(self, query: str, top_k: int = 5) -> List[Tuple[BusinessDoc, float]]:
        """
        查找相似的业务文档
        
        Args:
            query: 查询文本
            top_k: 返回数量
            
        Returns:
            List[Tuple[BusinessDoc, float]]: (文档, 相似度分数) 列表
        """
        if not self.embedding_service:
            return []
        
        try:
            query_embedding = await self.embedding_service.get_embedding(query)
            if not query_embedding:
                return []
            
            docs = await self.get_all_business_docs()
            docs_with_embeddings = [d for d in docs if d.embedding]
            
            if not docs_with_embeddings:
                return []
            
            similarities = []
            for doc in docs_with_embeddings:
                similarity = self.embedding_service.calculate_similarity(
                    query_embedding, doc.embedding
                )
                similarities.append((doc, similarity))
            
            # 按相似度排序并返回前k个
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find similar docs: {e}")
            return []
    
    async def find_similar_sql_examples(self, query: str, top_k: int = 5) -> List[Tuple[SQLExample, float]]:
        """
        查找相似的SQL范例
        
        Args:
            query: 查询文本
            top_k: 返回数量
            
        Returns:
            List[Tuple[SQLExample, float]]: (SQL范例, 相似度分数) 列表
        """
        if not self.embedding_service:
            return []
        
        try:
            query_embedding = await self.embedding_service.get_embedding(query)
            if not query_embedding:
                return []
            
            examples = await self.get_all_sql_examples()
            examples_with_embeddings = [e for e in examples if e.embedding]
            
            if not examples_with_embeddings:
                return []
            
            similarities = []
            for example in examples_with_embeddings:
                similarity = self.embedding_service.calculate_similarity(
                    query_embedding, example.embedding
                )
                similarities.append((example, similarity))
            
            # 按相似度排序并返回前k个
            similarities.sort(key=lambda x: x[1], reverse=True)
            return similarities[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find similar SQL examples: {e}")
            return []
    
    async def clear_all(self) -> bool:
        """清空所有知识库数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM schema_info")
                cursor.execute("DELETE FROM business_docs")
                cursor.execute("DELETE FROM sql_examples")
                conn.commit()
                
                logger.info("All knowledge base data cleared")
                return True
                
        except Exception as e:
            logger.error(f"Failed to clear knowledge base: {e}")
            return False
    
    async def close(self) -> None:
        """关闭知识库连接"""
        # 关闭嵌入服务
        if self.embedding_service:
            await self.embedding_service.close()
        
        # SQLite连接是自动管理的，这里主要用于清理资源
        logger.info("KnowledgeBaseManager closed")