#!/usr/bin/env python3
"""
检查数据库表
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.database_executor import CachedDatabaseExecutor

async def check_tables():
    """检查数据库表"""
    executor = CachedDatabaseExecutor()
    
    try:
        await executor.initialize_pool()
        
        # 直接查询表列表
        sql = "SHOW TABLES"
        result = await executor.execute_query(sql)
        
        if result.error:
            print(f"❌ 查询失败: {result.error}")
        else:
            print(f"✅ 查询成功，找到 {result.row_count} 个表:")
            for row in result.data:
                print(f"  - {list(row.values())[0]}")
        
        # 查询information_schema
        sql2 = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'test_core'"
        result2 = await executor.execute_query(sql2)
        
        if result2.error:
            print(f"❌ Information schema查询失败: {result2.error}")
        else:
            print(f"\n通过information_schema找到 {result2.row_count} 个表:")
            for row in result2.data:
                print(f"  - {row['table_name']}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await executor.close()

if __name__ == "__main__":
    asyncio.run(check_tables())