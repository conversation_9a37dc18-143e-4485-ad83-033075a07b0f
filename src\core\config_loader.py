"""
配置文件加载器
支持多种配置文件格式的加载和解析
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging

# 可选依赖
try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False

try:
    import toml
    HAS_TOML = True
except ImportError:
    HAS_TOML = False

logger = logging.getLogger(__name__)


class ConfigFileLoader:
    """配置文件加载器"""
    
    SUPPORTED_FORMATS = {'.json', '.yaml', '.yml', '.toml'}
    
    @classmethod
    def load_config_file(cls, file_path: Union[str, Path]) -> Dict[str, Any]:
        """加载配置文件"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        if file_path.suffix not in cls.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.suffix == '.json':
                return cls._load_json(content)
            elif file_path.suffix in ['.yaml', '.yml']:
                return cls._load_yaml(content)
            elif file_path.suffix == '.toml':
                return cls._load_toml(content)
            
        except Exception as e:
            logger.error(f"Failed to load config file {file_path}: {e}")
            raise
    
    @staticmethod
    def _load_json(content: str) -> Dict[str, Any]:
        """加载JSON配置"""
        return json.loads(content)
    
    @staticmethod
    def _load_yaml(content: str) -> Dict[str, Any]:
        """加载YAML配置"""
        if not HAS_YAML:
            raise ImportError("PyYAML is required to load YAML config files")
        return yaml.safe_load(content)
    
    @staticmethod
    def _load_toml(content: str) -> Dict[str, Any]:
        """加载TOML配置"""
        if not HAS_TOML:
            raise ImportError("toml is required to load TOML config files")
        return toml.loads(content)
    
    @classmethod
    def save_config_file(cls, config_dict: Dict[str, Any], file_path: Union[str, Path]) -> None:
        """保存配置到文件"""
        file_path = Path(file_path)
        
        if file_path.suffix not in cls.SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                if file_path.suffix == '.json':
                    json.dump(config_dict, f, indent=2, ensure_ascii=False)
                elif file_path.suffix in ['.yaml', '.yml']:
                    if not HAS_YAML:
                        raise ImportError("PyYAML is required to save YAML config files")
                    yaml.safe_dump(config_dict, f, default_flow_style=False, allow_unicode=True)
                elif file_path.suffix == '.toml':
                    if not HAS_TOML:
                        raise ImportError("toml is required to save TOML config files")
                    toml.dump(config_dict, f)
            
            logger.info(f"Configuration saved to {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to save config file {file_path}: {e}")
            raise


class ConfigMerger:
    """配置合并器"""
    
    @staticmethod
    def merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个配置字典，后面的配置会覆盖前面的"""
        result = {}
        
        for config in configs:
            if config:
                result = ConfigMerger._deep_merge(result, config)
        
        return result
    
    @staticmethod
    def _deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = ConfigMerger._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result


class ConfigTemplate:
    """配置模板生成器"""
    
    DEFAULT_CONFIG_TEMPLATE = {
        "qwen": {
            "api_key": "${QWEN_API_KEY}",
            "api_base": "https://dashscope.aliyuncs.com/api/v1",
            "model": "qwen-turbo",
            "timeout": 30,
            "max_retries": 3
        },
        "database": {
            "host": "localhost",
            "port": 5432,
            "name": "tiktok_data",
            "user": "postgres",
            "password": "${DB_PASSWORD}",
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30
        },
        "redis": {
            "host": "localhost",
            "port": 6379,
            "db": 0,
            "password": "${REDIS_PASSWORD}",
            "timeout": 5,
            "max_connections": 10
        },
        "app": {
            "name": "TikTok AI Agent",
            "version": "0.1.0",
            "debug": False,
            "log_level": "INFO",
            "host": "0.0.0.0",
            "port": 8000
        },
        "vanna": {
            "model": "qwen",
            "db_type": "postgres",
            "embedding_dimension": 1536,
            "max_context_length": 4000,
            "similarity_threshold": 0.7
        }
    }
    
    @classmethod
    def generate_config_template(cls, file_path: Union[str, Path]) -> None:
        """生成配置文件模板"""
        file_path = Path(file_path)
        
        if file_path.exists():
            logger.warning(f"Config file {file_path} already exists, skipping template generation")
            return
        
        ConfigFileLoader.save_config_file(cls.DEFAULT_CONFIG_TEMPLATE, file_path)
        logger.info(f"Generated config template at {file_path}")
    
    @classmethod
    def generate_env_template(cls, file_path: Union[str, Path] = ".env.template") -> None:
        """生成环境变量模板文件"""
        env_template = """# TikTok AI Agent 环境配置模板
# 复制此文件为 .env 并填入实际配置

# 千问模型API配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_BASE=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo
QWEN_TIMEOUT=30
QWEN_MAX_RETRIES=3

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tiktok_data
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_CONNECTION=postgresql://postgres:your_password_here@localhost:5432/tiktok_data
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_TIMEOUT=5
REDIS_MAX_CONNECTIONS=10

# 应用配置
APP_NAME=TikTok AI Agent
APP_VERSION=0.1.0
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# Vanna配置
VANNA_MODEL=qwen
VANNA_DB_TYPE=postgres
VANNA_EMBEDDING_DIM=1536
VANNA_MAX_CONTEXT=4000
VANNA_SIMILARITY_THRESHOLD=0.7
"""
        
        file_path = Path(file_path)
        if file_path.exists():
            logger.warning(f"Env template {file_path} already exists, skipping generation")
            return
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(env_template)
        
        logger.info(f"Generated environment template at {file_path}")


class ConfigProfileManager:
    """配置环境管理器"""
    
    def __init__(self, config_dir: Union[str, Path] = "configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
    
    def create_profile(self, profile_name: str, config_dict: Dict[str, Any]) -> None:
        """创建配置环境"""
        profile_file = self.config_dir / f"{profile_name}.json"
        ConfigFileLoader.save_config_file(config_dict, profile_file)
        logger.info(f"Created config profile: {profile_name}")
    
    def load_profile(self, profile_name: str) -> Dict[str, Any]:
        """加载配置环境"""
        profile_file = self.config_dir / f"{profile_name}.json"
        if not profile_file.exists():
            raise FileNotFoundError(f"Profile '{profile_name}' not found")
        
        return ConfigFileLoader.load_config_file(profile_file)
    
    def list_profiles(self) -> list[str]:
        """列出所有配置环境"""
        profiles = []
        for file_path in self.config_dir.glob("*.json"):
            profiles.append(file_path.stem)
        return profiles
    
    def delete_profile(self, profile_name: str) -> None:
        """删除配置环境"""
        profile_file = self.config_dir / f"{profile_name}.json"
        if profile_file.exists():
            profile_file.unlink()
            logger.info(f"Deleted config profile: {profile_name}")
        else:
            logger.warning(f"Profile '{profile_name}' not found")


# 便捷函数
def create_default_config_files():
    """创建默认配置文件"""
    ConfigTemplate.generate_env_template()
    ConfigTemplate.generate_config_template("config.json")
    ConfigTemplate.generate_config_template("config.yaml")
    logger.info("Default configuration files created")