"""
配置管理命令行工具
提供配置验证、测试和管理功能
"""

import argparse
import sys
from pathlib import Path
from typing import Optional
import logging

from .config import SystemConfig, load_config
from .config_validator import Config<PERSON>alida<PERSON>, ConfigHealthChecker
from .config_loader import ConfigTemplate, create_default_config_files

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def validate_config_command(env_file: Optional[str] = None) -> int:
    """验证配置命令"""
    try:
        config = SystemConfig.from_env(env_file)
        config.validate()
        
        # 运行配置验证
        config_dict = config.to_dict()
        errors = ConfigValidator.validate_config_dict(config_dict)
        
        if errors:
            logger.error("Configuration validation failed:")
            for error in errors:
                logger.error(f"  - {error}")
            return 1
        
        logger.info("✓ Configuration validation passed")
        return 0
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return 1


def test_connections_command(env_file: Optional[str] = None) -> int:
    """测试连接命令"""
    try:
        config = SystemConfig.from_env(env_file)
        config.validate()
        
        logger.info("Testing external service connections...")
        
        health_results = ConfigHealthChecker.run_health_checks(config)
        
        all_passed = True
        for service, result in health_results.items():
            if result is True:
                logger.info(f"✓ {service.title()} connection: OK")
            elif result is False:
                logger.error(f"✗ {service.title()} connection: FAILED")
                all_passed = False
            else:
                logger.warning(f"? {service.title()} connection: SKIPPED (dependency missing)")
        
        return 0 if all_passed else 1
        
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return 1


def show_config_command(env_file: Optional[str] = None) -> int:
    """显示配置命令"""
    try:
        config = SystemConfig.from_env(env_file)
        config_dict = config.to_dict()
        
        logger.info("Current configuration:")
        
        def print_dict(d, indent=0):
            for key, value in d.items():
                if isinstance(value, dict):
                    print("  " * indent + f"{key}:")
                    print_dict(value, indent + 1)
                else:
                    print("  " * indent + f"{key}: {value}")
        
        print_dict(config_dict)
        return 0
        
    except Exception as e:
        logger.error(f"Failed to show configuration: {e}")
        return 1


def generate_templates_command() -> int:
    """生成配置模板命令"""
    try:
        create_default_config_files()
        logger.info("✓ Configuration templates generated successfully")
        return 0
        
    except Exception as e:
        logger.error(f"Failed to generate templates: {e}")
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="TikTok AI Agent Configuration Management Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m src.core.config_cli validate                    # 验证默认配置
  python -m src.core.config_cli validate --env .env.prod    # 验证生产环境配置
  python -m src.core.config_cli test                        # 测试服务连接
  python -m src.core.config_cli show                        # 显示当前配置
  python -m src.core.config_cli generate                    # 生成配置模板
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # 验证配置命令
    validate_parser = subparsers.add_parser('validate', help='Validate configuration')
    validate_parser.add_argument('--env', help='Environment file path')
    
    # 测试连接命令
    test_parser = subparsers.add_parser('test', help='Test external service connections')
    test_parser.add_argument('--env', help='Environment file path')
    
    # 显示配置命令
    show_parser = subparsers.add_parser('show', help='Show current configuration')
    show_parser.add_argument('--env', help='Environment file path')
    
    # 生成模板命令
    generate_parser = subparsers.add_parser('generate', help='Generate configuration templates')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 执行对应的命令
    if args.command == 'validate':
        return validate_config_command(args.env)
    elif args.command == 'test':
        return test_connections_command(args.env)
    elif args.command == 'show':
        return show_config_command(args.env)
    elif args.command == 'generate':
        return generate_templates_command()
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())