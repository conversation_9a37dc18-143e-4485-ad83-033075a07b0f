[project]
name = "tiktok-ai-agent"
version = "0.1.0"
description = "基于Vanna和千问模型的TikTok数据分析AI Agent系统"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "streamlit>=1.28.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.0",
    "psycopg2-binary>=2.9.0",
    "redis>=5.0.0",
    "httpx>=0.25.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "plotly>=5.17.0",
    "pandas>=2.1.0",
    "numpy>=1.25.0",
    "vanna>=0.3.0",
    "openai>=1.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
